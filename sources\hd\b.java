package hd;

import aw.n;
import com.google.android.exoplayer2.text.ttml.TtmlNode;
import com.wear.bean.ProgramPattern;
import ew.c2;
import ew.h1;
import ew.h2;
import ew.n0;
import ew.r2;
import ew.w2;
import hd.LightColor;
import java.util.List;
import kotlin.Deprecated;
import kotlin.DeprecationLevel;
import kotlin.Metadata;
import kotlin.jvm.JvmField;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlinx.serialization.UnknownFieldException;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* compiled from: LightColorGroup.kt */
@n
@Metadata(d1 = {"\u0000L\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u0012\b\u0087\b\u0018\u0000 02\u00020\u0001:\u0002\"&B;\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0004\u0012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u0012\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\b0\u0007¢\u0006\u0004\b\u000b\u0010\fBW\b\u0010\u0012\u0006\u0010\u000e\u001a\u00020\r\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\b\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0004\u0012\u000e\u0010\t\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007\u0012\u000e\u0010\n\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007\u0012\b\u0010\u0010\u001a\u0004\u0018\u00010\u000f¢\u0006\u0004\b\u000b\u0010\u0011J'\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u0012\u001a\u00020\u00002\u0006\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0016\u001a\u00020\u0015H\u0001¢\u0006\u0004\b\u0018\u0010\u0019J\u0010\u0010\u001a\u001a\u00020\u0004HÖ\u0001¢\u0006\u0004\b\u001a\u0010\u001bJ\u0010\u0010\u001c\u001a\u00020\rHÖ\u0001¢\u0006\u0004\b\u001c\u0010\u001dJ\u001a\u0010 \u001a\u00020\u001f2\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b \u0010!R\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\"\u0010#\u001a\u0004\b$\u0010%R\u0017\u0010\u0005\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b&\u0010'\u001a\u0004\b(\u0010\u001bR\u0017\u0010\u0006\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b)\u0010'\u001a\u0004\b*\u0010\u001bR(\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\u00078\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b(\u0010+\u001a\u0004\b)\u0010,\"\u0004\b-\u0010.R(\u0010\n\u001a\b\u0012\u0004\u0012\u00020\b0\u00078\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b*\u0010+\u001a\u0004\b&\u0010,\"\u0004\b/\u0010.¨\u00061"}, d2 = {"Lhd/b;", "", "", TtmlNode.ATTR_ID, "", "groupKey", "groupName", "", "Lhd/a;", "displayColors", "colors", "<init>", "(JLjava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;)V", "", "seen0", "Lew/r2;", "serializationConstructorMarker", "(IJLjava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;Lew/r2;)V", "self", "Ldw/d;", "output", "Lcw/f;", "serialDesc", "", "g", "(Lhd/b;Ldw/d;Lcw/f;)V", "toString", "()Ljava/lang/String;", "hashCode", "()I", "other", "", "equals", "(Ljava/lang/Object;)Z", "a", "J", ProgramPattern.writePatternChar100, "()J", r0.b.f37717b, "Ljava/lang/String;", "d", "c", "e", "Ljava/util/List;", "()Ljava/util/List;", "setDisplayColors", "(Ljava/util/List;)V", "setColors", "Companion", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
/* renamed from: hd.b, reason: from toString */
/* loaded from: classes4.dex */
public final /* data */ class LightColorGroup {

    /* renamed from: Companion, reason: from kotlin metadata */
    @NotNull
    public static final Companion INSTANCE = new Companion(null);

    /* renamed from: f, reason: collision with root package name */
    @JvmField
    @NotNull
    public static final aw.c<Object>[] f31313f;

    /* renamed from: a, reason: collision with root package name and from kotlin metadata and from toString */
    public final long id;

    /* renamed from: b, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final String groupKey;

    /* renamed from: c, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final String groupName;

    /* renamed from: d, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public List<LightColor> displayColors;

    /* renamed from: e, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public List<LightColor> colors;

    /* compiled from: LightColorGroup.kt */
    @Metadata(d1 = {"\u0000:\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\bÇ\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0003\u0010\u0004J\u001d\u0010\t\u001a\u00020\b2\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u0002¢\u0006\u0004\b\t\u0010\nJ\u0015\u0010\r\u001a\u00020\u00022\u0006\u0010\f\u001a\u00020\u000b¢\u0006\u0004\b\r\u0010\u000eJ\u0017\u0010\u0011\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00100\u000f¢\u0006\u0004\b\u0011\u0010\u0012R\u0017\u0010\u0014\u001a\u00020\u00138\u0006¢\u0006\f\n\u0004\b\u0014\u0010\u0015\u001a\u0004\b\u0016\u0010\u0017¨\u0006\u0018"}, d2 = {"com/remotekmp/lightEffect/domain/entities/LightColorGroup.$serializer", "Lew/n0;", "Lhd/b;", "<init>", "()V", "Ldw/f;", "encoder", "value", "", ProgramPattern.writePatternChar100, "(Ldw/f;Lhd/b;)V", "Ldw/e;", "decoder", "e", "(Ldw/e;)Lhd/b;", "", "Law/c;", "d", "()[Law/c;", "Lcw/f;", "descriptor", "Lcw/f;", "getDescriptor", "()Lcw/f;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    @Deprecated(level = DeprecationLevel.HIDDEN, message = "This synthesized declaration should not be used directly")
    /* renamed from: hd.b$a */
    public /* synthetic */ class a implements n0<LightColorGroup> {

        /* renamed from: a, reason: collision with root package name */
        @NotNull
        public static final a f31319a;

        @NotNull
        private static final cw.f descriptor;

        static {
            a aVar = new a();
            f31319a = aVar;
            h2 h2Var = new h2("com.remotekmp.lightEffect.domain.entities.LightColorGroup", aVar, 5);
            h2Var.o(TtmlNode.ATTR_ID, false);
            h2Var.o("groupKey", false);
            h2Var.o("groupName", false);
            h2Var.o("displayColors", false);
            h2Var.o("colors", false);
            descriptor = h2Var;
        }

        @Override // ew.n0
        @NotNull
        public aw.c<?>[] b() {
            return n0.a.a(this);
        }

        @Override // ew.n0
        @NotNull
        public final aw.c<?>[] d() {
            aw.c<?>[] cVarArr = LightColorGroup.f31313f;
            aw.c<?> cVar = cVarArr[3];
            aw.c<?> cVar2 = cVarArr[4];
            w2 w2Var = w2.f30386a;
            return new aw.c[]{h1.f30272a, w2Var, w2Var, cVar, cVar2};
        }

        @Override // aw.b
        @NotNull
        /* renamed from: e, reason: merged with bridge method [inline-methods] */
        public final LightColorGroup a(@NotNull dw.e decoder) {
            int i10;
            String str;
            String str2;
            List list;
            List list2;
            long j10;
            Intrinsics.checkNotNullParameter(decoder, "decoder");
            cw.f fVar = descriptor;
            dw.c b10 = decoder.b(fVar);
            aw.c[] cVarArr = LightColorGroup.f31313f;
            String str3 = null;
            if (b10.o()) {
                long p10 = b10.p(fVar, 0);
                String F = b10.F(fVar, 1);
                String F2 = b10.F(fVar, 2);
                List list3 = (List) b10.H(fVar, 3, cVarArr[3], null);
                list2 = (List) b10.H(fVar, 4, cVarArr[4], null);
                str = F;
                str2 = F2;
                list = list3;
                j10 = p10;
                i10 = 31;
            } else {
                List list4 = null;
                long j11 = 0;
                int i11 = 0;
                boolean z10 = true;
                String str4 = null;
                List list5 = null;
                while (z10) {
                    int h10 = b10.h(fVar);
                    if (h10 == -1) {
                        z10 = false;
                    } else if (h10 == 0) {
                        j11 = b10.p(fVar, 0);
                        i11 |= 1;
                    } else if (h10 == 1) {
                        str3 = b10.F(fVar, 1);
                        i11 |= 2;
                    } else if (h10 == 2) {
                        str4 = b10.F(fVar, 2);
                        i11 |= 4;
                    } else if (h10 == 3) {
                        list5 = (List) b10.H(fVar, 3, cVarArr[3], list5);
                        i11 |= 8;
                    } else {
                        if (h10 != 4) {
                            throw new UnknownFieldException(h10);
                        }
                        list4 = (List) b10.H(fVar, 4, cVarArr[4], list4);
                        i11 |= 16;
                    }
                }
                i10 = i11;
                str = str3;
                str2 = str4;
                list = list5;
                list2 = list4;
                j10 = j11;
            }
            b10.c(fVar);
            return new LightColorGroup(i10, j10, str, str2, list, list2, null);
        }

        @Override // aw.o
        /* renamed from: f, reason: merged with bridge method [inline-methods] */
        public final void c(@NotNull dw.f encoder, @NotNull LightColorGroup value) {
            Intrinsics.checkNotNullParameter(encoder, "encoder");
            Intrinsics.checkNotNullParameter(value, "value");
            cw.f fVar = descriptor;
            dw.d b10 = encoder.b(fVar);
            LightColorGroup.g(value, b10, fVar);
            b10.c(fVar);
        }

        @Override // aw.c, aw.o, aw.b
        @NotNull
        public final cw.f getDescriptor() {
            return descriptor;
        }
    }

    /* compiled from: LightColorGroup.kt */
    @Metadata(d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J\u0013\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004¢\u0006\u0004\b\u0006\u0010\u0007¨\u0006\b"}, d2 = {"Lhd/b$b;", "", "<init>", "()V", "Law/c;", "Lhd/b;", "serializer", "()Law/c;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    /* renamed from: hd.b$b, reason: collision with other inner class name and from kotlin metadata */
    public static final class Companion {
        public Companion() {
        }

        @NotNull
        public final aw.c<LightColorGroup> serializer() {
            return a.f31319a;
        }

        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }
    }

    static {
        LightColor.C0551a c0551a = LightColor.C0551a.f31312a;
        f31313f = new aw.c[]{null, null, null, new ew.f(c0551a), new ew.f(c0551a)};
    }

    public /* synthetic */ LightColorGroup(int i10, long j10, String str, String str2, List list, List list2, r2 r2Var) {
        if (31 != (i10 & 31)) {
            c2.b(i10, 31, a.f31319a.getDescriptor());
        }
        this.id = j10;
        this.groupKey = str;
        this.groupName = str2;
        this.displayColors = list;
        this.colors = list2;
    }

    @JvmStatic
    public static final /* synthetic */ void g(LightColorGroup self, dw.d output, cw.f serialDesc) {
        aw.c<Object>[] cVarArr = f31313f;
        output.E(serialDesc, 0, self.id);
        output.B(serialDesc, 1, self.groupKey);
        output.B(serialDesc, 2, self.groupName);
        output.r(serialDesc, 3, cVarArr[3], self.displayColors);
        output.r(serialDesc, 4, cVarArr[4], self.colors);
    }

    @NotNull
    public final List<LightColor> b() {
        return this.colors;
    }

    @NotNull
    public final List<LightColor> c() {
        return this.displayColors;
    }

    @NotNull
    /* renamed from: d, reason: from getter */
    public final String getGroupKey() {
        return this.groupKey;
    }

    @NotNull
    /* renamed from: e, reason: from getter */
    public final String getGroupName() {
        return this.groupName;
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof LightColorGroup)) {
            return false;
        }
        LightColorGroup lightColorGroup = (LightColorGroup) other;
        return this.id == lightColorGroup.id && Intrinsics.areEqual(this.groupKey, lightColorGroup.groupKey) && Intrinsics.areEqual(this.groupName, lightColorGroup.groupName) && Intrinsics.areEqual(this.displayColors, lightColorGroup.displayColors) && Intrinsics.areEqual(this.colors, lightColorGroup.colors);
    }

    /* renamed from: f, reason: from getter */
    public final long getId() {
        return this.id;
    }

    public int hashCode() {
        return (((((((androidx.compose.animation.a.a(this.id) * 31) + this.groupKey.hashCode()) * 31) + this.groupName.hashCode()) * 31) + this.displayColors.hashCode()) * 31) + this.colors.hashCode();
    }

    @NotNull
    public String toString() {
        return "LightColorGroup(id=" + this.id + ", groupKey=" + this.groupKey + ", groupName=" + this.groupName + ", displayColors=" + this.displayColors + ", colors=" + this.colors + ')';
    }

    public LightColorGroup(long j10, @NotNull String groupKey, @NotNull String groupName, @NotNull List<LightColor> displayColors, @NotNull List<LightColor> colors) {
        Intrinsics.checkNotNullParameter(groupKey, "groupKey");
        Intrinsics.checkNotNullParameter(groupName, "groupName");
        Intrinsics.checkNotNullParameter(displayColors, "displayColors");
        Intrinsics.checkNotNullParameter(colors, "colors");
        this.id = j10;
        this.groupKey = groupKey;
        this.groupName = groupName;
        this.displayColors = displayColors;
        this.colors = colors;
    }
}
