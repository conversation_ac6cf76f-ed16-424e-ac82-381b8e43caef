{"v": "5.6.10", "fr": 24, "ip": 0, "op": 24, "w": 686, "h": 200, "nm": "等待中", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Vector 179", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [274, 95.75, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-120.5, 6.177], [-22.926, 6.176], [-20.457, 0.412], [-17.165, 6.176], [-9.757, -14], [-2.761, 14], [-1.115, 6.176], [3.412, 6.176], [8.351, -3.706], [12.055, 6.176], [22.344, 6.176], [50.5, 6.176]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-120.5, 6.177], [-22.926, 6.176], [-20.414, 6.162], [-17.165, 6.176], [-9.764, 6.188], [-2.771, 6.188], [-1.115, 6.176], [3.412, 6.176], [8.327, 6.169], [12.055, 6.176], [22.344, 6.176], [50.5, 6.176]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 8, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-120.5, 6.177], [-22.926, 6.176], [-20.457, 0.412], [-17.165, 6.176], [-9.757, -14], [-2.761, 14], [-1.115, 6.176], [3.412, 6.176], [8.351, -3.706], [12.055, 6.176], [22.344, 6.176], [50.5, 6.176]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-120.5, 6.177], [-22.926, 6.176], [-20.414, 6.162], [-17.165, 6.176], [-9.764, 6.188], [-2.771, 6.188], [-1.115, 6.176], [3.412, 6.176], [8.327, 6.169], [12.055, 6.176], [22.344, 6.176], [50.5, 6.176]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 16, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-120.5, 6.177], [-22.926, 6.176], [-20.457, 0.412], [-17.165, 6.176], [-9.757, -14], [-2.761, 14], [-1.115, 6.176], [3.412, 6.176], [8.351, -3.706], [12.055, 6.176], [22.344, 6.176], [50.5, 6.176]], "c": false}]}, {"t": 32, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-120.5, 6.177], [-22.926, 6.176], [-20.457, 0.412], [-17.165, 6.176], [-9.757, -14], [-2.761, 14], [-1.115, 6.176], [3.412, 6.176], [8.351, -3.706], [12.055, 6.176], [22.344, 6.176], [50.5, 6.176]], "c": false}]}], "ix": 2, "x": "var $bm_rt;\nvar timeStart, duration, pingPong, quant, quant, t, t, t;\ntry {\n    timeStart = thisProperty.key(1).time;\n    duration = $bm_sub(thisProperty.key(thisProperty.numKeys).time, timeStart);\n    pingPong = false;\n    quant = Math.floor($bm_div($bm_sub(time, timeStart), duration));\n    if (quant < 0)\n        quant = 0;\n    if (quant % 2 == 1 && pingPong == true) {\n        t = $bm_sub($bm_sum($bm_mul(2, timeStart), $bm_mul($bm_sum(quant, 1), duration)), time);\n    } else {\n        t = $bm_sub(time, $bm_mul(quant, duration));\n    }\n} catch (err) {\n    t = time;\n}\n$bm_rt = thisProperty.valueAtTime(t);"}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100, "ix": 9}, "w": {"a": 0, "k": 4, "ix": 10}, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.505, 0.722, 0.5, 0.753, 0.629, 0.861, 1, 0.505, 0.753, 1], "ix": 8}}, "s": {"a": 0, "k": [-50.5, 14], "ix": 4}, "e": {"a": 0, "k": [179.5, 14], "ix": 5}, "t": 1, "lc": 2, "lj": 2, "bm": 0, "nm": "Gradient Stroke 1", "mn": "ADBE Vector Graphic - G-Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Gradient Stroke 1", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 12, "s": [100]}], "ix": 1}, "e": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 245, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Vector 178", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [415, 97.603, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 4, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[118.533, 4.301], [15.44, 4.301], [7.999, 9.265], [3.472, -9.265], [-1.878, 4.324], [-9.286, -0.206], [-17.928, 4.324], [-48.5, 4.324]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[118.533, 4.237], [15.44, 4.237], [7.873, 4.507], [3.428, 4.485], [-1.878, 4.324], [-9.322, 4.294], [-17.928, 4.324], [-48.5, 4.324]], "c": false}]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0}, "t": 12, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[118.533, 4.301], [15.44, 4.301], [7.999, 9.265], [3.472, -9.265], [-1.878, 4.324], [-9.286, -0.206], [-17.928, 4.324], [-48.5, 4.324]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.167, "y": 0.167}, "t": 16, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[118.533, 4.237], [15.44, 4.237], [7.873, 4.507], [3.428, 4.485], [-1.878, 4.324], [-9.322, 4.294], [-17.928, 4.324], [-48.5, 4.324]], "c": false}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 20, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[118.533, 4.301], [15.44, 4.301], [7.999, 9.265], [3.472, -9.265], [-1.878, 4.324], [-9.286, -0.206], [-17.928, 4.324], [-48.5, 4.324]], "c": false}]}, {"t": 32, "s": [{"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[118.533, 4.301], [15.44, 4.301], [7.999, 9.265], [3.472, -9.265], [-1.878, 4.324], [-9.286, -0.206], [-17.928, 4.324], [-48.5, 4.324]], "c": false}]}], "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100, "ix": 9}, "w": {"a": 0, "k": 4, "ix": 10}, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.505, 0.722, 0.5, 0.753, 0.629, 0.861, 1, 0.505, 0.753, 1], "ix": 8}}, "s": {"a": 0, "k": [-191.5, 12.147], "ix": 4}, "e": {"a": 0, "k": [38.5, 12.147], "ix": 5}, "t": 1, "lc": 2, "lj": 2, "bm": 0, "nm": "Gradient Stroke 2", "mn": "ADBE Vector Graphic - G-Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Gradient Stroke 2", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 12, "s": [100]}], "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 245, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Vector 177", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [345.5, 100.25, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 1, "k": [{"i": {"x": 0.833, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [{"i": [[1.597, 0.677], [1.728, 0], [1.597, -0.677], [1.222, -1.251], [1.387, 1.42], [3.49, 0], [2.468, -2.525], [0, -3.571], [-2.468, -2.525], [0, 0], [0, 0], [-1.588, 0], [-2.532, 2.591], [0, 0], [0, 0], [-0.662, 1.634], [0, 1.768], [0.662, 1.634], [1.222, 1.25]], "o": [[-1.597, -0.677], [-1.728, 0], [-1.597, 0.677], [-1.387, 1.42], [-2.468, -2.525], [-3.49, 0], [-2.468, 2.525], [0, 3.571], [0, 0], [0, 0], [2.532, 2.591], [1.588, 0], [0, 0], [0, 0], [1.222, -1.25], [0.662, -1.634], [0, -1.768], [-0.662, -1.634], [-1.222, -1.251]], "v": [[16.876, -20.475], [11.84, -21.5], [6.804, -20.475], [2.535, -17.555], [-2.536, -17.555], [-11.841, -21.499], [-21.146, -17.555], [-25, -8.034], [-21.146, 1.488], [-18.61, 4.082], [-5.386, 17.614], [-0.001, 21.5], [5.385, 17.614], [18.609, 4.082], [21.145, 1.488], [23.998, -2.88], [25, -8.034], [23.998, -13.187], [21.145, -17.555]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 24, "s": [{"i": [[1.597, 0.677], [1.728, 0], [1.597, -0.677], [1.222, -1.251], [1.387, 1.42], [3.49, 0], [2.468, -2.525], [0, -3.571], [-2.468, -2.525], [0, 0], [0, 0], [-1.588, 0], [-2.532, 2.591], [0, 0], [0, 0], [-0.662, 1.634], [0, 1.768], [0.662, 1.634], [1.222, 1.25]], "o": [[-1.597, -0.677], [-1.728, 0], [-1.597, 0.677], [-1.387, 1.42], [-2.468, -2.525], [-3.49, 0], [-2.468, 2.525], [0, 3.571], [0, 0], [0, 0], [2.532, 2.591], [1.588, 0], [0, 0], [0, 0], [1.222, -1.25], [0.662, -1.634], [0, -1.768], [-0.662, -1.634], [-1.222, -1.251]], "v": [[16.876, -20.475], [11.84, -21.5], [6.804, -20.475], [2.535, -17.555], [-2.536, -17.555], [-11.841, -21.499], [-21.146, -17.555], [-25, -8.034], [-21.146, 1.488], [-18.61, 4.082], [-5.386, 17.614], [-0.001, 21.5], [5.385, 17.614], [18.609, 4.082], [21.145, 1.488], [23.998, -2.88], [25, -8.034], [23.998, -13.187], [21.145, -17.555]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 26.667, "s": [{"i": [[1.66, 0.704], [1.797, 0], [1.66, -0.704], [1.271, -1.301], [1.443, 1.476], [3.63, 0], [2.567, -2.626], [0, -3.714], [-2.566, -2.626], [0, 0], [0, 0], [-1.651, 0], [-2.633, 2.695], [0, 0], [0, 0], [-0.688, 1.699], [0, 1.839], [0.688, 1.699], [1.271, 1.3]], "o": [[-1.66, -0.704], [-1.797, 0], [-1.66, 0.704], [-1.442, 1.477], [-2.567, -2.626], [-3.63, 0], [-2.566, 2.626], [0, 3.714], [0, 0], [0, 0], [2.633, 2.695], [1.651, 0], [0, 0], [0, 0], [1.271, -1.3], [0.688, -1.699], [0, -1.839], [-0.688, -1.699], [-1.271, -1.3]], "v": [[17.551, -21.294], [12.313, -22.36], [7.076, -21.294], [2.636, -18.257], [-2.638, -18.257], [-12.315, -22.359], [-21.992, -18.257], [-26, -8.355], [-21.992, 1.547], [-19.355, 4.246], [-5.602, 18.318], [-0.001, 22.36], [5.601, 18.318], [19.353, 4.246], [21.99, 1.547], [24.958, -2.996], [26, -8.355], [24.958, -13.714], [21.99, -18.257]], "c": true}]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 29.333, "s": [{"i": [[1.437, 0.609], [1.555, 0], [1.437, -0.609], [1.1, -1.126], [1.249, 1.278], [3.141, 0], [2.221, -2.273], [0, -3.214], [-2.221, -2.273], [0, 0], [0, 0], [-1.429, 0], [-2.279, 2.332], [0, 0], [0, 0], [-0.595, 1.47], [0, 1.592], [0.595, 1.47], [1.1, 1.125]], "o": [[-1.437, -0.609], [-1.555, 0], [-1.437, 0.609], [-1.248, 1.278], [-2.221, -2.273], [-3.141, 0], [-2.221, 2.273], [0, 3.214], [0, 0], [0, 0], [2.279, 2.332], [1.429, 0], [0, 0], [0, 0], [1.1, -1.125], [0.595, -1.47], [0, -1.592], [-0.595, -1.47], [-1.1, -1.125]], "v": [[15.188, -18.427], [10.656, -19.35], [6.123, -18.427], [2.281, -15.799], [-2.283, -15.799], [-10.657, -19.349], [-19.031, -15.799], [-22.5, -7.23], [-19.031, 1.339], [-16.749, 3.674], [-4.848, 15.852], [-0.001, 19.35], [4.847, 15.852], [16.748, 3.674], [19.03, 1.339], [21.598, -2.592], [22.5, -7.23], [21.598, -11.868], [19.03, -15.799]], "c": true}]}, {"t": 32, "s": [{"i": [[1.597, 0.677], [1.728, 0], [1.597, -0.677], [1.222, -1.251], [1.387, 1.42], [3.49, 0], [2.468, -2.525], [0, -3.571], [-2.468, -2.525], [0, 0], [0, 0], [-1.588, 0], [-2.532, 2.591], [0, 0], [0, 0], [-0.662, 1.634], [0, 1.768], [0.662, 1.634], [1.222, 1.25]], "o": [[-1.597, -0.677], [-1.728, 0], [-1.597, 0.677], [-1.387, 1.42], [-2.468, -2.525], [-3.49, 0], [-2.468, 2.525], [0, 3.571], [0, 0], [0, 0], [2.532, 2.591], [1.588, 0], [0, 0], [0, 0], [1.222, -1.25], [0.662, -1.634], [0, -1.768], [-0.662, -1.634], [-1.222, -1.251]], "v": [[16.876, -20.475], [11.84, -21.5], [6.804, -20.475], [2.535, -17.555], [-2.536, -17.555], [-11.841, -21.499], [-21.146, -17.555], [-25, -8.034], [-21.146, 1.488], [-18.61, 4.082], [-5.386, 17.614], [-0.001, 21.5], [5.385, 17.614], [18.609, 4.082], [21.145, 1.488], [23.998, -2.88], [25, -8.034], [23.998, -13.187], [21.145, -17.555]], "c": true}]}], "ix": 2, "x": "var $bm_rt;\nvar timeStart, duration, pingPong, quant, quant, t, t, t;\ntry {\n    timeStart = thisProperty.key(1).time;\n    duration = $bm_sub(thisProperty.key(thisProperty.numKeys).time, timeStart);\n    pingPong = false;\n    quant = Math.floor($bm_div($bm_sub(time, timeStart), duration));\n    if (quant < 0)\n        quant = 0;\n    if (quant % 2 == 1 && pingPong == true) {\n        t = $bm_sub($bm_sum($bm_mul(2, timeStart), $bm_mul($bm_sum(quant, 1), duration)), time);\n    } else {\n        t = $bm_sub(time, $bm_mul(quant, duration));\n    }\n} catch (err) {\n    t = time;\n}\n$bm_rt = thisProperty.valueAtTime(t);"}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "gs", "o": {"a": 0, "k": 100, "ix": 9}, "w": {"a": 0, "k": 4, "ix": 10}, "g": {"p": 3, "k": {"a": 0, "k": [0, 1, 0.505, 0.722, 0.5, 0.753, 0.629, 0.861, 1, 0.505, 0.753, 1], "ix": 8}}, "s": {"a": 0, "k": [-122, 9.5], "ix": 4}, "e": {"a": 0, "k": [108, 9.5], "ix": 5}, "t": 1, "lc": 2, "lj": 2, "bm": 0, "nm": "Gradient Stroke 3", "mn": "ADBE Vector Graphic - G-Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Gradient Stroke 3", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 12, "s": [0]}, {"t": 24, "s": [100]}], "ix": 1}, "e": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 245, "st": 0, "bm": 0}], "markers": []}