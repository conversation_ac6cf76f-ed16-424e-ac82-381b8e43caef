package fd;

import aw.n;
import com.wear.bean.ProgramPattern;
import ew.c2;
import ew.h2;
import ew.n0;
import ew.r2;
import ew.w0;
import fd.LightGearDTO;
import hd.LightMode;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import kotlin.Deprecated;
import kotlin.DeprecationLevel;
import kotlin.Metadata;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.JvmField;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.SourceDebugExtension;
import kotlinx.serialization.UnknownFieldException;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* compiled from: LightModeDTO.kt */
@n
@Metadata(d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u000b\b\u0087\b\u0018\u0000 )2\u00020\u0001:\u0002\"\u0017B%\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0002\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005¢\u0006\u0004\b\b\u0010\tB;\b\u0010\u0012\u0006\u0010\n\u001a\u00020\u0002\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0002\u0012\u000e\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u0012\b\u0010\f\u001a\u0004\u0018\u00010\u000b¢\u0006\u0004\b\b\u0010\rJ'\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u000e\u001a\u00020\u00002\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\u0011H\u0001¢\u0006\u0004\b\u0014\u0010\u0015J\r\u0010\u0017\u001a\u00020\u0016¢\u0006\u0004\b\u0017\u0010\u0018J\u0010\u0010\u001a\u001a\u00020\u0019HÖ\u0001¢\u0006\u0004\b\u001a\u0010\u001bJ\u0010\u0010\u001c\u001a\u00020\u0002HÖ\u0001¢\u0006\u0004\b\u001c\u0010\u001dJ\u001a\u0010 \u001a\u00020\u001f2\b\u0010\u001e\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b \u0010!R\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\"\u0010#\u001a\u0004\b$\u0010\u001dR\u0017\u0010\u0004\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0017\u0010#\u001a\u0004\b%\u0010\u001dR\u001d\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u00058\u0006¢\u0006\f\n\u0004\b\u0014\u0010&\u001a\u0004\b'\u0010(¨\u0006*"}, d2 = {"Lfd/f;", "", "", "colorType", "modeCode", "", "Lfd/e;", "gears", "<init>", "(IILjava/util/List;)V", "seen0", "Lew/r2;", "serializationConstructorMarker", "(IIILjava/util/List;Lew/r2;)V", "self", "Ldw/d;", "output", "Lcw/f;", "serialDesc", "", "c", "(Lfd/f;Ldw/d;Lcw/f;)V", "Lhd/f;", r0.b.f37717b, "()Lhd/f;", "", "toString", "()Ljava/lang/String;", "hashCode", "()I", "other", "", "equals", "(Ljava/lang/Object;)Z", "a", "I", "getColorType", "getModeCode", "Ljava/util/List;", "getGears", "()Ljava/util/List;", "Companion", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
@SourceDebugExtension({"SMAP\nLightModeDTO.kt\nKotlin\n*S Kotlin\n*F\n+ 1 LightModeDTO.kt\ncom/remotekmp/lightEffect/data/entities/LightModeDTO\n+ 2 _Collections.kt\nkotlin/collections/CollectionsKt___CollectionsKt\n*L\n1#1,28:1\n1557#2:29\n1628#2,3:30\n*S KotlinDebug\n*F\n+ 1 LightModeDTO.kt\ncom/remotekmp/lightEffect/data/entities/LightModeDTO\n*L\n25#1:29\n25#1:30,3\n*E\n"})
/* renamed from: fd.f, reason: from toString */
/* loaded from: classes4.dex */
public final /* data */ class LightModeDTO {

    /* renamed from: Companion, reason: from kotlin metadata */
    @NotNull
    public static final Companion INSTANCE = new Companion(null);

    /* renamed from: d, reason: collision with root package name */
    @JvmField
    @NotNull
    public static final aw.c<Object>[] f30628d = {null, null, new ew.f(LightGearDTO.a.f30627a)};

    /* renamed from: a, reason: collision with root package name and from kotlin metadata and from toString */
    public final int colorType;

    /* renamed from: b, reason: collision with root package name and from kotlin metadata and from toString */
    public final int modeCode;

    /* renamed from: c, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final List<LightGearDTO> gears;

    /* compiled from: LightModeDTO.kt */
    @Metadata(d1 = {"\u0000:\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\bÇ\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0003\u0010\u0004J\u001d\u0010\t\u001a\u00020\b2\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u0002¢\u0006\u0004\b\t\u0010\nJ\u0015\u0010\r\u001a\u00020\u00022\u0006\u0010\f\u001a\u00020\u000b¢\u0006\u0004\b\r\u0010\u000eJ\u0017\u0010\u0011\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00100\u000f¢\u0006\u0004\b\u0011\u0010\u0012R\u0017\u0010\u0014\u001a\u00020\u00138\u0006¢\u0006\f\n\u0004\b\u0014\u0010\u0015\u001a\u0004\b\u0016\u0010\u0017¨\u0006\u0018"}, d2 = {"com/remotekmp/lightEffect/data/entities/LightModeDTO.$serializer", "Lew/n0;", "Lfd/f;", "<init>", "()V", "Ldw/f;", "encoder", "value", "", ProgramPattern.writePatternChar100, "(Ldw/f;Lfd/f;)V", "Ldw/e;", "decoder", "e", "(Ldw/e;)Lfd/f;", "", "Law/c;", "d", "()[Law/c;", "Lcw/f;", "descriptor", "Lcw/f;", "getDescriptor", "()Lcw/f;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    @Deprecated(level = DeprecationLevel.HIDDEN, message = "This synthesized declaration should not be used directly")
    /* renamed from: fd.f$a */
    public /* synthetic */ class a implements n0<LightModeDTO> {

        /* renamed from: a, reason: collision with root package name */
        @NotNull
        public static final a f30632a;

        @NotNull
        private static final cw.f descriptor;

        static {
            a aVar = new a();
            f30632a = aVar;
            h2 h2Var = new h2("com.remotekmp.lightEffect.data.entities.LightModeDTO", aVar, 3);
            h2Var.o("colorType", false);
            h2Var.o("modeCode", false);
            h2Var.o("gears", false);
            descriptor = h2Var;
        }

        @Override // ew.n0
        @NotNull
        public aw.c<?>[] b() {
            return n0.a.a(this);
        }

        @Override // ew.n0
        @NotNull
        public final aw.c<?>[] d() {
            aw.c<?> cVar = LightModeDTO.f30628d[2];
            w0 w0Var = w0.f30382a;
            return new aw.c[]{w0Var, w0Var, cVar};
        }

        @Override // aw.b
        @NotNull
        /* renamed from: e, reason: merged with bridge method [inline-methods] */
        public final LightModeDTO a(@NotNull dw.e decoder) {
            int i10;
            int i11;
            int i12;
            List list;
            Intrinsics.checkNotNullParameter(decoder, "decoder");
            cw.f fVar = descriptor;
            dw.c b10 = decoder.b(fVar);
            aw.c[] cVarArr = LightModeDTO.f30628d;
            if (b10.o()) {
                int f10 = b10.f(fVar, 0);
                int f11 = b10.f(fVar, 1);
                list = (List) b10.H(fVar, 2, cVarArr[2], null);
                i10 = f10;
                i12 = f11;
                i11 = 7;
            } else {
                List list2 = null;
                int i13 = 0;
                int i14 = 0;
                int i15 = 0;
                boolean z10 = true;
                while (z10) {
                    int h10 = b10.h(fVar);
                    if (h10 == -1) {
                        z10 = false;
                    } else if (h10 == 0) {
                        i13 = b10.f(fVar, 0);
                        i14 |= 1;
                    } else if (h10 == 1) {
                        i15 = b10.f(fVar, 1);
                        i14 |= 2;
                    } else {
                        if (h10 != 2) {
                            throw new UnknownFieldException(h10);
                        }
                        list2 = (List) b10.H(fVar, 2, cVarArr[2], list2);
                        i14 |= 4;
                    }
                }
                i10 = i13;
                i11 = i14;
                i12 = i15;
                list = list2;
            }
            b10.c(fVar);
            return new LightModeDTO(i11, i10, i12, list, null);
        }

        @Override // aw.o
        /* renamed from: f, reason: merged with bridge method [inline-methods] */
        public final void c(@NotNull dw.f encoder, @NotNull LightModeDTO value) {
            Intrinsics.checkNotNullParameter(encoder, "encoder");
            Intrinsics.checkNotNullParameter(value, "value");
            cw.f fVar = descriptor;
            dw.d b10 = encoder.b(fVar);
            LightModeDTO.c(value, b10, fVar);
            b10.c(fVar);
        }

        @Override // aw.c, aw.o, aw.b
        @NotNull
        public final cw.f getDescriptor() {
            return descriptor;
        }
    }

    /* compiled from: LightModeDTO.kt */
    @Metadata(d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J\u0013\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004¢\u0006\u0004\b\u0006\u0010\u0007¨\u0006\b"}, d2 = {"Lfd/f$b;", "", "<init>", "()V", "Law/c;", "Lfd/f;", "serializer", "()Law/c;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    /* renamed from: fd.f$b, reason: from kotlin metadata */
    public static final class Companion {
        public Companion() {
        }

        @NotNull
        public final aw.c<LightModeDTO> serializer() {
            return a.f30632a;
        }

        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }
    }

    public /* synthetic */ LightModeDTO(int i10, int i11, int i12, List list, r2 r2Var) {
        if (7 != (i10 & 7)) {
            c2.b(i10, 7, a.f30632a.getDescriptor());
        }
        this.colorType = i11;
        this.modeCode = i12;
        this.gears = list;
    }

    @JvmStatic
    public static final /* synthetic */ void c(LightModeDTO self, dw.d output, cw.f serialDesc) {
        aw.c<Object>[] cVarArr = f30628d;
        output.z(serialDesc, 0, self.colorType);
        output.z(serialDesc, 1, self.modeCode);
        output.r(serialDesc, 2, cVarArr[2], self.gears);
    }

    @NotNull
    public final LightMode b() {
        int i10 = this.colorType;
        int i11 = this.modeCode;
        List<LightGearDTO> list = this.gears;
        ArrayList arrayList = new ArrayList(CollectionsKt.collectionSizeOrDefault(list, 10));
        Iterator<T> it = list.iterator();
        while (it.hasNext()) {
            arrayList.add(((LightGearDTO) it.next()).a());
        }
        return new LightMode(i10, i11, arrayList);
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof LightModeDTO)) {
            return false;
        }
        LightModeDTO lightModeDTO = (LightModeDTO) other;
        return this.colorType == lightModeDTO.colorType && this.modeCode == lightModeDTO.modeCode && Intrinsics.areEqual(this.gears, lightModeDTO.gears);
    }

    public int hashCode() {
        return (((this.colorType * 31) + this.modeCode) * 31) + this.gears.hashCode();
    }

    @NotNull
    public String toString() {
        return "LightModeDTO(colorType=" + this.colorType + ", modeCode=" + this.modeCode + ", gears=" + this.gears + ')';
    }

    public LightModeDTO(int i10, int i11, @NotNull List<LightGearDTO> gears) {
        Intrinsics.checkNotNullParameter(gears, "gears");
        this.colorType = i10;
        this.modeCode = i11;
        this.gears = gears;
    }
}
