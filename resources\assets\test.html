<!DOCTYPE HTML>
<html>
<head>
    <meta charset="utf-8">
    <title>test</title>

    <script type="text/javascript">
         function WebSocketTest()
         {
            if ("WebSocket" in window)
            {
               alert("您的浏览器支持 WebSocket!");
               
               // 打开一个 web socket
               var curPath = window.document.location.href;
    //获取主机地址之后的目录，
    var pathName = window.document.location.pathname;
    var pos = curPath.indexOf(pathName);
    //获取主机地址
    var localhostPaht = curPath.substring(0, pos).replace("http://","");
               var ws = new WebSocket("ws://"+localhostPaht+"/log");
                
               ws.onopen = function()
               {
                  // Web Socket 已连接上，使用 send() 方法发送数据
<!--                  ws.send("88888");-->
               };
                
               ws.onmessage = function (evt) 
               { 
                  var received_msg = evt.data;
                  console.log(received_msg);
                  document.getElementById('evtData').innerHTML=received_msg;
                  addRow("888",received_msg);
               };
                
               ws.onclose = function()
               { 
                  // 关闭 websocket
                  alert("连接已关闭..."); 
               };
            }
            
            else
            {
               // 浏览器不支持 WebSocket
               alert("您的浏览器不支持 WebSocket!");
            }
         }

 function addRow(tag,context) {
            var tb = document.getElementById("mytableid");
            var row = tb.insertRow();
            var cell = row.insertCell();
            cell.innerText = tag + "  : " + context;
            cell = row.insertCell();
            cell.innerHTML = "<input type='text' value = '新一行'>";
            
        }


    </script>

</head>
<body>

<div id="sse">
    <a href="javascript:WebSocketTest()">运行 WebSocket</a>
</div>
<div id="evtData">
</div>
<table id="mytableid">
    <!-- <tr><td>第一行</td><td>输入</td></tr> -->
</table>
</body>
</html>