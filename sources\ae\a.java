package ae;

import com.google.android.exoplayer2.text.ttml.TtmlNode;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* compiled from: BookEntity.kt */
@Metadata(d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0007\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\r\b\u0086\b\u0018\u00002\u00020\u0001B-\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\b\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0004\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\u0002¢\u0006\u0004\b\b\u0010\tJ\u0010\u0010\n\u001a\u00020\u0004HÖ\u0001¢\u0006\u0004\b\n\u0010\u000bJ\u0010\u0010\r\u001a\u00020\fHÖ\u0001¢\u0006\u0004\b\r\u0010\u000eJ\u001a\u0010\u0011\u001a\u00020\u00102\b\u0010\u000f\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\u0011\u0010\u0012R\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0013\u0010\u0014\u001a\u0004\b\u0015\u0010\u0016R\u0019\u0010\u0005\u001a\u0004\u0018\u00010\u00048\u0006¢\u0006\f\n\u0004\b\u0017\u0010\u0018\u001a\u0004\b\u0019\u0010\u000bR\u0019\u0010\u0006\u001a\u0004\u0018\u00010\u00048\u0006¢\u0006\f\n\u0004\b\u0019\u0010\u0018\u001a\u0004\b\u0013\u0010\u000bR\u0019\u0010\u0007\u001a\u0004\u0018\u00010\u00028\u0006¢\u0006\f\n\u0004\b\u001a\u0010\u001b\u001a\u0004\b\u0017\u0010\u001c¨\u0006\u001d"}, d2 = {"Lae/a;", "", "", TtmlNode.ATTR_ID, "", "title", "author", "publishedYear", "<init>", "(JLjava/lang/String;Ljava/lang/String;Ljava/lang/Long;)V", "toString", "()Ljava/lang/String;", "", "hashCode", "()I", "other", "", "equals", "(Ljava/lang/Object;)Z", "a", "J", "getId", "()J", r0.b.f37717b, "Ljava/lang/String;", "c", "d", "Ljava/lang/Long;", "()Ljava/lang/Long;", "sqliteDelight"}, k = 1, mv = {2, 0, 0}, xi = 48)
/* renamed from: ae.a, reason: from toString */
/* loaded from: classes4.dex */
public final /* data */ class BookEntity {

    /* renamed from: a, reason: collision with root package name and from kotlin metadata and from toString */
    public final long id;

    /* renamed from: b, reason: collision with root package name and from kotlin metadata and from toString */
    @Nullable
    public final String title;

    /* renamed from: c, reason: collision with root package name and from kotlin metadata and from toString */
    @Nullable
    public final String author;

    /* renamed from: d, reason: collision with root package name and from kotlin metadata and from toString */
    @Nullable
    public final Long publishedYear;

    public BookEntity(long j10, @Nullable String str, @Nullable String str2, @Nullable Long l10) {
        this.id = j10;
        this.title = str;
        this.author = str2;
        this.publishedYear = l10;
    }

    @Nullable
    /* renamed from: a, reason: from getter */
    public final String getAuthor() {
        return this.author;
    }

    @Nullable
    /* renamed from: b, reason: from getter */
    public final Long getPublishedYear() {
        return this.publishedYear;
    }

    @Nullable
    /* renamed from: c, reason: from getter */
    public final String getTitle() {
        return this.title;
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof BookEntity)) {
            return false;
        }
        BookEntity bookEntity = (BookEntity) other;
        return this.id == bookEntity.id && Intrinsics.areEqual(this.title, bookEntity.title) && Intrinsics.areEqual(this.author, bookEntity.author) && Intrinsics.areEqual(this.publishedYear, bookEntity.publishedYear);
    }

    public int hashCode() {
        int a10 = androidx.compose.animation.a.a(this.id) * 31;
        String str = this.title;
        int hashCode = (a10 + (str == null ? 0 : str.hashCode())) * 31;
        String str2 = this.author;
        int hashCode2 = (hashCode + (str2 == null ? 0 : str2.hashCode())) * 31;
        Long l10 = this.publishedYear;
        return hashCode2 + (l10 != null ? l10.hashCode() : 0);
    }

    @NotNull
    public String toString() {
        return "BookEntity(id=" + this.id + ", title=" + this.title + ", author=" + this.author + ", publishedYear=" + this.publishedYear + ')';
    }
}
