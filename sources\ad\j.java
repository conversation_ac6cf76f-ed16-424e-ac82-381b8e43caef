package ad;

import androidx.exifinterface.media.ExifInterface;
import aw.n;
import com.wear.bean.ProgramPattern;
import ew.c2;
import ew.h2;
import ew.n0;
import ew.r2;
import ew.w0;
import ew.w2;
import kotlin.Deprecated;
import kotlin.DeprecationLevel;
import kotlin.Metadata;
import kotlin.jvm.JvmField;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlinx.serialization.UnknownFieldException;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jivesoftware.smackx.xhtmlim.XHTMLText;

/* compiled from: RemoteApiResponse.kt */
@n
@Metadata(d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0000\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0015\b\u0087\b\u0018\u0000 +*\u0004\b\u0000\u0010\u00012\u00020\u0002:\u0002!%B+\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\b\u0010\b\u001a\u0004\u0018\u00010\u0007\u0012\b\u0010\t\u001a\u0004\u0018\u00018\u0000¢\u0006\u0004\b\n\u0010\u000bB?\b\u0010\u0012\u0006\u0010\f\u001a\u00020\u0005\u0012\u0006\u0010\u0004\u001a\u00020\u0003\u0012\u0006\u0010\u0006\u001a\u00020\u0005\u0012\b\u0010\b\u001a\u0004\u0018\u00010\u0007\u0012\b\u0010\t\u001a\u0004\u0018\u00018\u0000\u0012\b\u0010\u000e\u001a\u0004\u0018\u00010\r¢\u0006\u0004\b\n\u0010\u000fJG\u0010\u0018\u001a\u00020\u0017\"\n\b\u0001\u0010\u0001*\u0004\u0018\u00010\u00022\f\u0010\u0010\u001a\b\u0012\u0004\u0012\u00028\u00010\u00002\u0006\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0014\u001a\u00020\u00132\f\u0010\u0016\u001a\b\u0012\u0004\u0012\u00028\u00010\u0015H\u0001¢\u0006\u0004\b\u0018\u0010\u0019J\u0010\u0010\u001a\u001a\u00020\u0007HÖ\u0001¢\u0006\u0004\b\u001a\u0010\u001bJ\u0010\u0010\u001c\u001a\u00020\u0005HÖ\u0001¢\u0006\u0004\b\u001c\u0010\u001dJ\u001a\u0010\u001f\u001a\u00020\u00032\b\u0010\u001e\u001a\u0004\u0018\u00010\u0002HÖ\u0003¢\u0006\u0004\b\u001f\u0010 R\u0017\u0010\u0004\u001a\u00020\u00038\u0006¢\u0006\f\n\u0004\b!\u0010\"\u001a\u0004\b#\u0010$R\u0017\u0010\u0006\u001a\u00020\u00058\u0006¢\u0006\f\n\u0004\b%\u0010&\u001a\u0004\b!\u0010\u001dR\u0019\u0010\b\u001a\u0004\u0018\u00010\u00078\u0006¢\u0006\f\n\u0004\b'\u0010(\u001a\u0004\b'\u0010\u001bR\u0019\u0010\t\u001a\u0004\u0018\u00018\u00008\u0006¢\u0006\f\n\u0004\b#\u0010)\u001a\u0004\b%\u0010*¨\u0006,"}, d2 = {"Lad/j;", ExifInterface.GPS_DIRECTION_TRUE, "", "", "result", "", XHTMLText.CODE, "", "message", "data", "<init>", "(ZILjava/lang/String;Ljava/lang/Object;)V", "seen0", "Lew/r2;", "serializationConstructorMarker", "(IZILjava/lang/String;Ljava/lang/Object;Lew/r2;)V", "self", "Ldw/d;", "output", "Lcw/f;", "serialDesc", "Law/c;", "typeSerial0", "", "e", "(Lad/j;Ldw/d;Lcw/f;Law/c;)V", "toString", "()Ljava/lang/String;", "hashCode", "()I", "other", "equals", "(Ljava/lang/Object;)Z", "a", "Z", "d", "()Z", r0.b.f37717b, "I", "c", "Ljava/lang/String;", "Ljava/lang/Object;", "()Ljava/lang/Object;", "Companion", "core"}, k = 1, mv = {2, 0, 0}, xi = 48)
/* renamed from: ad.j, reason: from toString */
/* loaded from: classes4.dex */
public final /* data */ class RemoteApiResponse<T> {

    /* renamed from: Companion, reason: from kotlin metadata */
    @NotNull
    public static final Companion INSTANCE = new Companion(null);

    /* renamed from: e, reason: collision with root package name */
    @JvmField
    @NotNull
    public static final cw.f f195e;

    /* renamed from: a, reason: collision with root package name and from kotlin metadata and from toString */
    public final boolean result;

    /* renamed from: b, reason: collision with root package name and from kotlin metadata and from toString */
    public final int code;

    /* renamed from: c, reason: collision with root package name and from kotlin metadata and from toString */
    @Nullable
    public final String message;

    /* renamed from: d, reason: collision with root package name and from kotlin metadata and from toString */
    @Nullable
    public final T data;

    /* compiled from: RemoteApiResponse.kt */
    @Metadata(d1 = {"\u0000@\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0005\b\u0007\u0018\u0000*\u0004\b\u0001\u0010\u00012\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00028\u00010\u00030\u0002B\t\b\u0002¢\u0006\u0004\b\u0004\u0010\u0005B\u0017\b\u0016\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00028\u00010\u0006¢\u0006\u0004\b\u0004\u0010\bJ#\u0010\r\u001a\u00020\f2\u0006\u0010\n\u001a\u00020\t2\f\u0010\u000b\u001a\b\u0012\u0004\u0012\u00028\u00010\u0003¢\u0006\u0004\b\r\u0010\u000eJ\u001b\u0010\u0011\u001a\b\u0012\u0004\u0012\u00028\u00010\u00032\u0006\u0010\u0010\u001a\u00020\u000f¢\u0006\u0004\b\u0011\u0010\u0012J\u0017\u0010\u0014\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00060\u0013¢\u0006\u0004\b\u0014\u0010\u0015J\u0017\u0010\u0016\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00060\u0013¢\u0006\u0004\b\u0016\u0010\u0015R\u0017\u0010\u0018\u001a\u00020\u00178\u0006¢\u0006\f\n\u0004\b\u0018\u0010\u0019\u001a\u0004\b\u001a\u0010\u001b¨\u0006\u001c"}, d2 = {"com/remotekmp/core/http/RemoteApiResponse.$serializer", ExifInterface.GPS_DIRECTION_TRUE, "Lew/n0;", "Lad/j;", "<init>", "()V", "Law/c;", "typeSerial0", "(Law/c;)V", "Ldw/f;", "encoder", "value", "", ProgramPattern.writePatternChar100, "(Ldw/f;Lad/j;)V", "Ldw/e;", "decoder", "e", "(Ldw/e;)Lad/j;", "", "d", "()[Law/c;", r0.b.f37717b, "Lcw/f;", "descriptor", "Lcw/f;", "getDescriptor", "()Lcw/f;", "core"}, k = 1, mv = {2, 0, 0}, xi = 48)
    @Deprecated(level = DeprecationLevel.HIDDEN, message = "This synthesized declaration should not be used directly")
    /* renamed from: ad.j$a */
    public /* synthetic */ class a<T> implements n0<RemoteApiResponse<T>> {

        /* renamed from: a, reason: collision with root package name */
        public final /* synthetic */ aw.c<?> f200a;

        @NotNull
        private final cw.f descriptor;

        public a() {
            h2 h2Var = new h2("com.remotekmp.core.http.RemoteApiResponse", this, 4);
            h2Var.o("result", false);
            h2Var.o(XHTMLText.CODE, false);
            h2Var.o("message", false);
            h2Var.o("data", false);
            this.descriptor = h2Var;
        }

        @Override // ew.n0
        @NotNull
        public final aw.c<?>[] b() {
            return new aw.c[]{this.f200a};
        }

        @Override // ew.n0
        @NotNull
        public final aw.c<?>[] d() {
            return new aw.c[]{ew.i.f30287a, w0.f30382a, bw.a.u(w2.f30386a), bw.a.u(this.f200a)};
        }

        /* JADX WARN: Multi-variable type inference failed */
        @Override // aw.b
        @NotNull
        /* renamed from: e, reason: merged with bridge method [inline-methods] */
        public final RemoteApiResponse<T> a(@NotNull dw.e decoder) {
            boolean z10;
            int i10;
            int i11;
            String str;
            Object obj;
            Intrinsics.checkNotNullParameter(decoder, "decoder");
            cw.f fVar = this.descriptor;
            dw.c b10 = decoder.b(fVar);
            if (b10.o()) {
                boolean E = b10.E(fVar, 0);
                int f10 = b10.f(fVar, 1);
                String str2 = (String) b10.D(fVar, 2, w2.f30386a, null);
                z10 = E;
                obj = b10.D(fVar, 3, this.f200a, null);
                str = str2;
                i10 = f10;
                i11 = 15;
            } else {
                String str3 = null;
                Object obj2 = null;
                boolean z11 = false;
                int i12 = 0;
                int i13 = 0;
                boolean z12 = true;
                while (z12) {
                    int h10 = b10.h(fVar);
                    if (h10 == -1) {
                        z12 = false;
                    } else if (h10 == 0) {
                        z11 = b10.E(fVar, 0);
                        i13 |= 1;
                    } else if (h10 == 1) {
                        i12 = b10.f(fVar, 1);
                        i13 |= 2;
                    } else if (h10 == 2) {
                        str3 = (String) b10.D(fVar, 2, w2.f30386a, str3);
                        i13 |= 4;
                    } else {
                        if (h10 != 3) {
                            throw new UnknownFieldException(h10);
                        }
                        obj2 = b10.D(fVar, 3, this.f200a, obj2);
                        i13 |= 8;
                    }
                }
                z10 = z11;
                i10 = i12;
                i11 = i13;
                str = str3;
                obj = obj2;
            }
            b10.c(fVar);
            return new RemoteApiResponse<>(i11, z10, i10, str, obj, null);
        }

        @Override // aw.o
        /* renamed from: f, reason: merged with bridge method [inline-methods] */
        public final void c(@NotNull dw.f encoder, @NotNull RemoteApiResponse<T> value) {
            Intrinsics.checkNotNullParameter(encoder, "encoder");
            Intrinsics.checkNotNullParameter(value, "value");
            cw.f fVar = this.descriptor;
            dw.d b10 = encoder.b(fVar);
            RemoteApiResponse.e(value, b10, fVar, this.f200a);
            b10.c(fVar);
        }

        @Override // aw.c, aw.o, aw.b
        @NotNull
        public final cw.f getDescriptor() {
            return this.descriptor;
        }

        /* JADX WARN: 'this' call moved to the top of the method (can break code semantics) */
        /* JADX WARN: Multi-variable type inference failed */
        public a(@NotNull aw.c<T> typeSerial0) {
            this();
            Intrinsics.checkNotNullParameter(typeSerial0, "typeSerial0");
            this.f200a = typeSerial0;
        }
    }

    /* compiled from: RemoteApiResponse.kt */
    @Metadata(d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J-\u0010\b\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00028\u00010\u00070\u0005\"\u0004\b\u0001\u0010\u00042\f\u0010\u0006\u001a\b\u0012\u0004\u0012\u00028\u00010\u0005¢\u0006\u0004\b\b\u0010\t¨\u0006\n"}, d2 = {"Lad/j$b;", "", "<init>", "()V", ExifInterface.GPS_DIRECTION_TRUE, "Law/c;", "typeSerial0", "Lad/j;", "serializer", "(Law/c;)Law/c;", "core"}, k = 1, mv = {2, 0, 0}, xi = 48)
    /* renamed from: ad.j$b, reason: from kotlin metadata */
    public static final class Companion {
        public Companion() {
        }

        @NotNull
        public final <T> aw.c<RemoteApiResponse<T>> serializer(@NotNull aw.c<T> typeSerial0) {
            Intrinsics.checkNotNullParameter(typeSerial0, "typeSerial0");
            return new a(typeSerial0);
        }

        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }
    }

    static {
        h2 h2Var = new h2("com.remotekmp.core.http.RemoteApiResponse", null, 4);
        h2Var.o("result", false);
        h2Var.o(XHTMLText.CODE, false);
        h2Var.o("message", false);
        h2Var.o("data", false);
        f195e = h2Var;
    }

    /* JADX WARN: Multi-variable type inference failed */
    public /* synthetic */ RemoteApiResponse(int i10, boolean z10, int i11, String str, Object obj, r2 r2Var) {
        if (15 != (i10 & 15)) {
            c2.b(i10, 15, f195e);
        }
        this.result = z10;
        this.code = i11;
        this.message = str;
        this.data = obj;
    }

    @JvmStatic
    public static final /* synthetic */ void e(RemoteApiResponse self, dw.d output, cw.f serialDesc, aw.c typeSerial0) {
        output.x(serialDesc, 0, self.result);
        output.z(serialDesc, 1, self.code);
        output.m(serialDesc, 2, w2.f30386a, self.message);
        output.m(serialDesc, 3, typeSerial0, self.data);
    }

    /* renamed from: a, reason: from getter */
    public final int getCode() {
        return this.code;
    }

    @Nullable
    public final T b() {
        return this.data;
    }

    @Nullable
    /* renamed from: c, reason: from getter */
    public final String getMessage() {
        return this.message;
    }

    /* renamed from: d, reason: from getter */
    public final boolean getResult() {
        return this.result;
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof RemoteApiResponse)) {
            return false;
        }
        RemoteApiResponse remoteApiResponse = (RemoteApiResponse) other;
        return this.result == remoteApiResponse.result && this.code == remoteApiResponse.code && Intrinsics.areEqual(this.message, remoteApiResponse.message) && Intrinsics.areEqual(this.data, remoteApiResponse.data);
    }

    public int hashCode() {
        int a10 = ((androidx.compose.foundation.c.a(this.result) * 31) + this.code) * 31;
        String str = this.message;
        int hashCode = (a10 + (str == null ? 0 : str.hashCode())) * 31;
        T t10 = this.data;
        return hashCode + (t10 != null ? t10.hashCode() : 0);
    }

    @NotNull
    public String toString() {
        return "RemoteApiResponse(result=" + this.result + ", code=" + this.code + ", message=" + this.message + ", data=" + this.data + ')';
    }

    public RemoteApiResponse(boolean z10, int i10, @Nullable String str, @Nullable T t10) {
        this.result = z10;
        this.code = i10;
        this.message = str;
        this.data = t10;
    }
}
