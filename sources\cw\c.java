package cw;

import com.google.firebase.analytics.FirebaseAnalytics;
import com.wear.bean.ProgramPattern;
import java.lang.annotation.Annotation;
import java.util.List;
import kotlin.Metadata;
import kotlin.jvm.JvmField;
import kotlin.jvm.internal.Intrinsics;
import kotlin.reflect.KClass;
import kotlin.text.Typography;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jivesoftware.smackx.xhtmlim.XHTMLText;

/* compiled from: ContextAware.kt */
@Metadata(d1 = {"\u0000D\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0010 \n\u0002\u0010\u001b\n\u0002\b\r\n\u0002\u0018\u0002\n\u0002\b\f\b\u0002\u0018\u00002\u00020\u0001B\u001b\u0012\u0006\u0010\u0002\u001a\u00020\u0001\u0012\n\u0010\u0004\u001a\u0006\u0012\u0002\b\u00030\u0003¢\u0006\u0004\b\u0005\u0010\u0006J\u001a\u0010\n\u001a\u00020\t2\b\u0010\b\u001a\u0004\u0018\u00010\u0007H\u0096\u0002¢\u0006\u0004\b\n\u0010\u000bJ\u000f\u0010\r\u001a\u00020\fH\u0016¢\u0006\u0004\b\r\u0010\u000eJ\u000f\u0010\u0010\u001a\u00020\u000fH\u0016¢\u0006\u0004\b\u0010\u0010\u0011J\u0018\u0010\u0013\u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\fH\u0097\u0001¢\u0006\u0004\b\u0013\u0010\u0014J\u0018\u0010\u0016\u001a\u00020\f2\u0006\u0010\u0015\u001a\u00020\u000fH\u0097\u0001¢\u0006\u0004\b\u0016\u0010\u0017J\u001e\u0010\u001a\u001a\b\u0012\u0004\u0012\u00020\u00190\u00182\u0006\u0010\u0012\u001a\u00020\fH\u0097\u0001¢\u0006\u0004\b\u001a\u0010\u001bJ\u0018\u0010\u001c\u001a\u00020\u00012\u0006\u0010\u0012\u001a\u00020\fH\u0097\u0001¢\u0006\u0004\b\u001c\u0010\u001dJ\u0018\u0010\u001e\u001a\u00020\t2\u0006\u0010\u0012\u001a\u00020\fH\u0097\u0001¢\u0006\u0004\b\u001e\u0010\u001fR\u0014\u0010\u0002\u001a\u00020\u00018\u0002X\u0082\u0004¢\u0006\u0006\n\u0004\b \u0010!R\u0018\u0010\u0004\u001a\u0006\u0012\u0002\b\u00030\u00038\u0006X\u0087\u0004¢\u0006\u0006\n\u0004\b\"\u0010#R\u001a\u0010&\u001a\u00020\u000f8\u0016X\u0096\u0004¢\u0006\f\n\u0004\b\u0016\u0010$\u001a\u0004\b%\u0010\u0011R\u0014\u0010*\u001a\u00020'8\u0016X\u0097\u0005¢\u0006\u0006\u001a\u0004\b(\u0010)R\u0014\u0010,\u001a\u00020\t8VX\u0097\u0005¢\u0006\u0006\u001a\u0004\b\"\u0010+R\u0014\u0010-\u001a\u00020\t8VX\u0096\u0005¢\u0006\u0006\u001a\u0004\b-\u0010+R\u0014\u0010/\u001a\u00020\f8\u0016X\u0097\u0005¢\u0006\u0006\u001a\u0004\b.\u0010\u000eR\u001a\u00102\u001a\b\u0012\u0004\u0012\u00020\u00190\u00188VX\u0097\u0005¢\u0006\u0006\u001a\u0004\b0\u00101¨\u00063"}, d2 = {"Lcw/c;", "Lcw/f;", "original", "Lkotlin/reflect/KClass;", "kClass", "<init>", "(Lcw/f;Lkotlin/reflect/KClass;)V", "", "other", "", "equals", "(Ljava/lang/Object;)Z", "", "hashCode", "()I", "", "toString", "()Ljava/lang/String;", FirebaseAnalytics.Param.INDEX, "e", "(I)Ljava/lang/String;", "name", "c", "(Ljava/lang/String;)I", "", "", ProgramPattern.writePatternChar100, "(I)Ljava/util/List;", "g", "(I)Lcw/f;", jl.i.f32548i, "(I)Z", "a", "Lcw/f;", r0.b.f37717b, "Lkotlin/reflect/KClass;", "Ljava/lang/String;", XHTMLText.H, "serialName", "Lcw/m;", "getKind", "()Lcw/m;", "kind", "()Z", "isNullable", "isInline", "d", "elementsCount", "getAnnotations", "()Ljava/util/List;", "annotations", "kotlinx-serialization-core"}, k = 1, mv = {2, 0, 0}, xi = 48)
/* renamed from: cw.c, reason: from toString */
/* loaded from: classes6.dex */
public final class ContextDescriptor implements f {

    /* renamed from: a, reason: collision with root package name and from kotlin metadata */
    @NotNull
    public final f original;

    /* renamed from: b, reason: collision with root package name and from kotlin metadata */
    @JvmField
    @NotNull
    public final KClass<?> kClass;

    /* renamed from: c, reason: collision with root package name and from kotlin metadata */
    @NotNull
    public final String serialName;

    public ContextDescriptor(@NotNull f original, @NotNull KClass<?> kClass) {
        Intrinsics.checkNotNullParameter(original, "original");
        Intrinsics.checkNotNullParameter(kClass, "kClass");
        this.original = original;
        this.kClass = kClass;
        this.serialName = original.getSerialName() + Typography.less + kClass.getSimpleName() + Typography.greater;
    }

    @Override // cw.f
    public boolean b() {
        return this.original.b();
    }

    @Override // cw.f
    public int c(@NotNull String name) {
        Intrinsics.checkNotNullParameter(name, "name");
        return this.original.c(name);
    }

    @Override // cw.f
    /* renamed from: d */
    public int getElementsCount() {
        return this.original.getElementsCount();
    }

    @Override // cw.f
    @NotNull
    public String e(int index) {
        return this.original.e(index);
    }

    public boolean equals(@Nullable Object other) {
        ContextDescriptor contextDescriptor = other instanceof ContextDescriptor ? (ContextDescriptor) other : null;
        return contextDescriptor != null && Intrinsics.areEqual(this.original, contextDescriptor.original) && Intrinsics.areEqual(contextDescriptor.kClass, this.kClass);
    }

    @Override // cw.f
    @NotNull
    public List<Annotation> f(int index) {
        return this.original.f(index);
    }

    @Override // cw.f
    @NotNull
    public f g(int index) {
        return this.original.g(index);
    }

    @Override // cw.f
    @NotNull
    public List<Annotation> getAnnotations() {
        return this.original.getAnnotations();
    }

    @Override // cw.f
    @NotNull
    public m getKind() {
        return this.original.getKind();
    }

    @Override // cw.f
    @NotNull
    /* renamed from: h, reason: from getter */
    public String getSerialName() {
        return this.serialName;
    }

    public int hashCode() {
        return (this.kClass.hashCode() * 31) + getSerialName().hashCode();
    }

    @Override // cw.f
    public boolean i(int index) {
        return this.original.i(index);
    }

    @Override // cw.f
    public boolean isInline() {
        return this.original.isInline();
    }

    @NotNull
    public String toString() {
        return "ContextDescriptor(kClass: " + this.kClass + ", original: " + this.original + ')';
    }
}
