package cu;

import com.koushikdutta.async.http.AsyncHttpDelete;
import com.koushikdutta.async.http.AsyncHttpHead;
import com.koushikdutta.async.http.AsyncHttpPut;
import com.wear.bean.ProgramPattern;
import java.util.List;
import kotlin.Metadata;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jivesoftware.smackx.xhtmlim.XHTMLText;

/* compiled from: HttpMethod.kt */
@Metadata(d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0007\b\u0086\b\u0018\u0000 \u00122\u00020\u0001:\u0001\u000fB\u000f\u0012\u0006\u0010\u0003\u001a\u00020\u0002¢\u0006\u0004\b\u0004\u0010\u0005J\u0010\u0010\u0006\u001a\u00020\u0002HÖ\u0001¢\u0006\u0004\b\u0006\u0010\u0007J\u0010\u0010\t\u001a\u00020\bHÖ\u0001¢\u0006\u0004\b\t\u0010\nJ\u001a\u0010\r\u001a\u00020\f2\b\u0010\u000b\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\r\u0010\u000eR\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u000f\u0010\u0010\u001a\u0004\b\u0011\u0010\u0007¨\u0006\u0013"}, d2 = {"Lcu/c0;", "", "", "value", "<init>", "(Ljava/lang/String;)V", "toString", "()Ljava/lang/String;", "", "hashCode", "()I", "other", "", "equals", "(Ljava/lang/Object;)Z", "a", "Ljava/lang/String;", XHTMLText.H, r0.b.f37717b, "ktor-http"}, k = 1, mv = {2, 0, 0}, xi = 48)
/* renamed from: cu.c0, reason: from toString */
/* loaded from: classes5.dex */
public final /* data */ class HttpMethod {

    /* renamed from: b, reason: collision with root package name and from kotlin metadata */
    @NotNull
    public static final Companion INSTANCE = new Companion(null);

    /* renamed from: c, reason: collision with root package name */
    @NotNull
    public static final HttpMethod f29246c;

    /* renamed from: d, reason: collision with root package name */
    @NotNull
    public static final HttpMethod f29247d;

    /* renamed from: e, reason: collision with root package name */
    @NotNull
    public static final HttpMethod f29248e;

    /* renamed from: f, reason: collision with root package name */
    @NotNull
    public static final HttpMethod f29249f;

    /* renamed from: g, reason: collision with root package name */
    @NotNull
    public static final HttpMethod f29250g;

    /* renamed from: h, reason: collision with root package name */
    @NotNull
    public static final HttpMethod f29251h;

    /* renamed from: i, reason: collision with root package name */
    @NotNull
    public static final HttpMethod f29252i;

    /* renamed from: j, reason: collision with root package name */
    @NotNull
    public static final List<HttpMethod> f29253j;

    /* renamed from: a, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final String value;

    /* compiled from: HttpMethod.kt */
    @Metadata(d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0011\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003R\u0017\u0010\u0005\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b\u0005\u0010\u0006\u001a\u0004\b\u0007\u0010\bR\u0017\u0010\t\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b\t\u0010\u0006\u001a\u0004\b\n\u0010\bR\u0017\u0010\u000b\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b\u000b\u0010\u0006\u001a\u0004\b\f\u0010\bR\u0017\u0010\r\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b\r\u0010\u0006\u001a\u0004\b\u000e\u0010\bR\u0017\u0010\u000f\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b\u000f\u0010\u0006\u001a\u0004\b\u0010\u0010\bR\u0017\u0010\u0011\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b\u0011\u0010\u0006\u001a\u0004\b\u0012\u0010\bR\u0017\u0010\u0013\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b\u0013\u0010\u0006\u001a\u0004\b\u0014\u0010\b¨\u0006\u0015"}, d2 = {"Lcu/c0$a;", "", "<init>", "()V", "Lcu/c0;", "Get", "Lcu/c0;", r0.b.f37717b, "()Lcu/c0;", "Post", ProgramPattern.writePatternChar100, "Put", "g", "Patch", "e", "Delete", "a", "Head", "c", "Options", "d", "ktor-http"}, k = 1, mv = {2, 0, 0}, xi = 48)
    /* renamed from: cu.c0$a, reason: from kotlin metadata */
    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        @NotNull
        public final HttpMethod a() {
            return HttpMethod.f29250g;
        }

        @NotNull
        public final HttpMethod b() {
            return HttpMethod.f29246c;
        }

        @NotNull
        public final HttpMethod c() {
            return HttpMethod.f29251h;
        }

        @NotNull
        public final HttpMethod d() {
            return HttpMethod.f29252i;
        }

        @NotNull
        public final HttpMethod e() {
            return HttpMethod.f29249f;
        }

        @NotNull
        public final HttpMethod f() {
            return HttpMethod.f29247d;
        }

        @NotNull
        public final HttpMethod g() {
            return HttpMethod.f29248e;
        }

        public Companion() {
        }
    }

    static {
        HttpMethod httpMethod = new HttpMethod("GET");
        f29246c = httpMethod;
        HttpMethod httpMethod2 = new HttpMethod("POST");
        f29247d = httpMethod2;
        HttpMethod httpMethod3 = new HttpMethod(AsyncHttpPut.METHOD);
        f29248e = httpMethod3;
        HttpMethod httpMethod4 = new HttpMethod("PATCH");
        f29249f = httpMethod4;
        HttpMethod httpMethod5 = new HttpMethod(AsyncHttpDelete.METHOD);
        f29250g = httpMethod5;
        HttpMethod httpMethod6 = new HttpMethod(AsyncHttpHead.METHOD);
        f29251h = httpMethod6;
        HttpMethod httpMethod7 = new HttpMethod("OPTIONS");
        f29252i = httpMethod7;
        f29253j = CollectionsKt.listOf((Object[]) new HttpMethod[]{httpMethod, httpMethod2, httpMethod3, httpMethod4, httpMethod5, httpMethod6, httpMethod7});
    }

    public HttpMethod(@NotNull String value) {
        Intrinsics.checkNotNullParameter(value, "value");
        this.value = value;
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        return (other instanceof HttpMethod) && Intrinsics.areEqual(this.value, ((HttpMethod) other).value);
    }

    @NotNull
    /* renamed from: h, reason: from getter */
    public final String getValue() {
        return this.value;
    }

    public int hashCode() {
        return this.value.hashCode();
    }

    @NotNull
    public String toString() {
        return "HttpMethod(value=" + this.value + ')';
    }
}
