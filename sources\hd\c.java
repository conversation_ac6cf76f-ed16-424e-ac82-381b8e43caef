package hd;

import aw.n;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.wear.bean.ProgramPattern;
import ew.c2;
import ew.h1;
import ew.h2;
import ew.n0;
import ew.r2;
import java.util.List;
import kotlin.Deprecated;
import kotlin.DeprecationLevel;
import kotlin.Metadata;
import kotlin.jvm.JvmField;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlinx.serialization.UnknownFieldException;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* compiled from: LightCommand.kt */
@n
@Metadata(d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010 \n\u0002\b\u0005\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0010\b\u0087\b\u0018\u0000 ,2\u00020\u0001:\u0002 $B-\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00020\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0002\u0012\u0006\u0010\u0007\u001a\u00020\u0002¢\u0006\u0004\b\b\u0010\tBC\b\u0010\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u000e\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0002\u0012\u0006\u0010\u0007\u001a\u00020\u0002\u0012\b\u0010\r\u001a\u0004\u0018\u00010\f¢\u0006\u0004\b\b\u0010\u000eJ'\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u000f\u001a\u00020\u00002\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u0012H\u0001¢\u0006\u0004\b\u0015\u0010\u0016J\u0010\u0010\u0018\u001a\u00020\u0017HÖ\u0001¢\u0006\u0004\b\u0018\u0010\u0019J\u0010\u0010\u001a\u001a\u00020\nHÖ\u0001¢\u0006\u0004\b\u001a\u0010\u001bJ\u001a\u0010\u001e\u001a\u00020\u001d2\b\u0010\u001c\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\u001e\u0010\u001fR\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b \u0010!\u001a\u0004\b\"\u0010#R\u001d\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00020\u00048\u0006¢\u0006\f\n\u0004\b$\u0010%\u001a\u0004\b&\u0010'R\"\u0010\u0006\u001a\u00020\u00028\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\"\u0010!\u001a\u0004\b(\u0010#\"\u0004\b)\u0010*R\"\u0010\u0007\u001a\u00020\u00028\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b&\u0010!\u001a\u0004\b$\u0010#\"\u0004\b+\u0010*¨\u0006-"}, d2 = {"Lhd/c;", "", "", FirebaseAnalytics.Param.INDEX, "", "ledList", "mode", "freq", "<init>", "(JLjava/util/List;JJ)V", "", "seen0", "Lew/r2;", "serializationConstructorMarker", "(IJLjava/util/List;JJLew/r2;)V", "self", "Ldw/d;", "output", "Lcw/f;", "serialDesc", "", ProgramPattern.writePatternChar100, "(Lhd/c;Ldw/d;Lcw/f;)V", "", "toString", "()Ljava/lang/String;", "hashCode", "()I", "other", "", "equals", "(Ljava/lang/Object;)Z", "a", "J", "c", "()J", r0.b.f37717b, "Ljava/util/List;", "d", "()Ljava/util/List;", "e", "setMode", "(J)V", "setFreq", "Companion", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
/* renamed from: hd.c, reason: from toString */
/* loaded from: classes4.dex */
public final /* data */ class LightCommand {

    /* renamed from: Companion, reason: from kotlin metadata */
    @NotNull
    public static final Companion INSTANCE = new Companion(null);

    /* renamed from: e */
    @JvmField
    @NotNull
    public static final aw.c<Object>[] f31320e = {null, new ew.f(h1.f30272a), null, null};

    /* renamed from: a, reason: from kotlin metadata and from toString */
    public final long index;

    /* renamed from: b, reason: from toString */
    @NotNull
    public final List<Long> ledList;

    /* renamed from: c, reason: from kotlin metadata and from toString */
    public long mode;

    /* renamed from: d, reason: from kotlin metadata and from toString */
    public long freq;

    /* compiled from: LightCommand.kt */
    @Metadata(d1 = {"\u0000:\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\bÇ\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0003\u0010\u0004J\u001d\u0010\t\u001a\u00020\b2\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u0002¢\u0006\u0004\b\t\u0010\nJ\u0015\u0010\r\u001a\u00020\u00022\u0006\u0010\f\u001a\u00020\u000b¢\u0006\u0004\b\r\u0010\u000eJ\u0017\u0010\u0011\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00100\u000f¢\u0006\u0004\b\u0011\u0010\u0012R\u0017\u0010\u0014\u001a\u00020\u00138\u0006¢\u0006\f\n\u0004\b\u0014\u0010\u0015\u001a\u0004\b\u0016\u0010\u0017¨\u0006\u0018"}, d2 = {"com/remotekmp/lightEffect/domain/entities/LightCommand.$serializer", "Lew/n0;", "Lhd/c;", "<init>", "()V", "Ldw/f;", "encoder", "value", "", ProgramPattern.writePatternChar100, "(Ldw/f;Lhd/c;)V", "Ldw/e;", "decoder", "e", "(Ldw/e;)Lhd/c;", "", "Law/c;", "d", "()[Law/c;", "Lcw/f;", "descriptor", "Lcw/f;", "getDescriptor", "()Lcw/f;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    @Deprecated(level = DeprecationLevel.HIDDEN, message = "This synthesized declaration should not be used directly")
    /* renamed from: hd.c$a */
    public /* synthetic */ class a implements n0<LightCommand> {

        /* renamed from: a */
        @NotNull
        public static final a f31325a;

        @NotNull
        private static final cw.f descriptor;

        static {
            a aVar = new a();
            f31325a = aVar;
            h2 h2Var = new h2("com.remotekmp.lightEffect.domain.entities.LightCommand", aVar, 4);
            h2Var.o(FirebaseAnalytics.Param.INDEX, false);
            h2Var.o("ledList", false);
            h2Var.o("mode", false);
            h2Var.o("freq", false);
            descriptor = h2Var;
        }

        @Override // ew.n0
        @NotNull
        public aw.c<?>[] b() {
            return n0.a.a(this);
        }

        @Override // ew.n0
        @NotNull
        public final aw.c<?>[] d() {
            aw.c<?> cVar = LightCommand.f31320e[1];
            h1 h1Var = h1.f30272a;
            return new aw.c[]{h1Var, cVar, h1Var, h1Var};
        }

        @Override // aw.b
        @NotNull
        /* renamed from: e */
        public final LightCommand a(@NotNull dw.e decoder) {
            int i10;
            List list;
            long j10;
            long j11;
            long j12;
            Intrinsics.checkNotNullParameter(decoder, "decoder");
            cw.f fVar = descriptor;
            dw.c b10 = decoder.b(fVar);
            aw.c[] cVarArr = LightCommand.f31320e;
            List list2 = null;
            if (b10.o()) {
                long p10 = b10.p(fVar, 0);
                List list3 = (List) b10.H(fVar, 1, cVarArr[1], null);
                long p11 = b10.p(fVar, 2);
                list = list3;
                j10 = b10.p(fVar, 3);
                j11 = p11;
                j12 = p10;
                i10 = 15;
            } else {
                long j13 = 0;
                long j14 = 0;
                long j15 = 0;
                int i11 = 0;
                boolean z10 = true;
                while (z10) {
                    int h10 = b10.h(fVar);
                    if (h10 == -1) {
                        z10 = false;
                    } else if (h10 == 0) {
                        j15 = b10.p(fVar, 0);
                        i11 |= 1;
                    } else if (h10 == 1) {
                        list2 = (List) b10.H(fVar, 1, cVarArr[1], list2);
                        i11 |= 2;
                    } else if (h10 == 2) {
                        j14 = b10.p(fVar, 2);
                        i11 |= 4;
                    } else {
                        if (h10 != 3) {
                            throw new UnknownFieldException(h10);
                        }
                        j13 = b10.p(fVar, 3);
                        i11 |= 8;
                    }
                }
                i10 = i11;
                list = list2;
                j10 = j13;
                j11 = j14;
                j12 = j15;
            }
            b10.c(fVar);
            return new LightCommand(i10, j12, list, j11, j10, null);
        }

        @Override // aw.o
        /* renamed from: f */
        public final void c(@NotNull dw.f encoder, @NotNull LightCommand value) {
            Intrinsics.checkNotNullParameter(encoder, "encoder");
            Intrinsics.checkNotNullParameter(value, "value");
            cw.f fVar = descriptor;
            dw.d b10 = encoder.b(fVar);
            LightCommand.f(value, b10, fVar);
            b10.c(fVar);
        }

        @Override // aw.c, aw.o, aw.b
        @NotNull
        public final cw.f getDescriptor() {
            return descriptor;
        }
    }

    /* compiled from: LightCommand.kt */
    @Metadata(d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J\u0013\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004¢\u0006\u0004\b\u0006\u0010\u0007¨\u0006\b"}, d2 = {"Lhd/c$b;", "", "<init>", "()V", "Law/c;", "Lhd/c;", "serializer", "()Law/c;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    /* renamed from: hd.c$b, reason: from kotlin metadata */
    public static final class Companion {
        public Companion() {
        }

        @NotNull
        public final aw.c<LightCommand> serializer() {
            return a.f31325a;
        }

        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }
    }

    public /* synthetic */ LightCommand(int i10, long j10, List list, long j11, long j12, r2 r2Var) {
        if (15 != (i10 & 15)) {
            c2.b(i10, 15, a.f31325a.getDescriptor());
        }
        this.index = j10;
        this.ledList = list;
        this.mode = j11;
        this.freq = j12;
    }

    @JvmStatic
    public static final /* synthetic */ void f(LightCommand lightCommand, dw.d dVar, cw.f fVar) {
        aw.c<Object>[] cVarArr = f31320e;
        dVar.E(fVar, 0, lightCommand.index);
        dVar.r(fVar, 1, cVarArr[1], lightCommand.ledList);
        dVar.E(fVar, 2, lightCommand.mode);
        dVar.E(fVar, 3, lightCommand.freq);
    }

    /* renamed from: b, reason: from getter */
    public final long getFreq() {
        return this.freq;
    }

    /* renamed from: c, reason: from getter */
    public final long getIndex() {
        return this.index;
    }

    @NotNull
    public final List<Long> d() {
        return this.ledList;
    }

    /* renamed from: e, reason: from getter */
    public final long getMode() {
        return this.mode;
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof LightCommand)) {
            return false;
        }
        LightCommand lightCommand = (LightCommand) other;
        return this.index == lightCommand.index && Intrinsics.areEqual(this.ledList, lightCommand.ledList) && this.mode == lightCommand.mode && this.freq == lightCommand.freq;
    }

    public int hashCode() {
        return (((((androidx.compose.animation.a.a(this.index) * 31) + this.ledList.hashCode()) * 31) + androidx.compose.animation.a.a(this.mode)) * 31) + androidx.compose.animation.a.a(this.freq);
    }

    @NotNull
    public String toString() {
        return "LightCommand(index=" + this.index + ", ledList=" + this.ledList + ", mode=" + this.mode + ", freq=" + this.freq + ')';
    }

    public LightCommand(long j10, @NotNull List<Long> ledList, long j11, long j12) {
        Intrinsics.checkNotNullParameter(ledList, "ledList");
        this.index = j10;
        this.ledList = ledList;
        this.mode = j11;
        this.freq = j12;
    }
}
