<?xml version="1.0" encoding="utf-8"?>
<widget xmlns:idurit="http://www.w3.org/ns/widgets"
    id="com.example.demo"
    version="1.0.0">
    <name>Demo
    </name>
    <description>Sample Apache Cordova App
    </description>
    <author
        email="<EMAIL>"
        href="https://cordova.apache.org">Apache Cordova Team 
    </author>
    <content src="index.html"/>
    <allow-navigation href="*"/>
    <allow-intent href="http://*/*"/>
    <allow-intent href="https://*/*"/>
    <access origin="*"/>
    <preference
        name="loglevel"
        value="DEBUG"/>
    <feature name="appGallery">
        <param
            name="android-package"
            value="org.apache.cordova.gallery.XRemotePlugin"/>
    </feature>
    <feature name="appGalleryToy">
        <param
            name="android-package"
            value="org.apache.cordova.gallery.XRemoteToyPlugin"/>
    </feature>
    <feature name="appBaseInfo">
        <param
            name="android-package"
            value="org.apache.cordova.gallery.AppBaseInfoPlugin"/>
    </feature>
    <feature name="Accelerometer">
        <param
            name="android-package"
            value="org.apache.cordova.devicemotion.AccelListener"/>
    </feature>
    <feature name="CDVOrientation">
        <param
            name="android-package"
            value="cordova.plugins.screenorientation.CDVOrientation"/>
    </feature>
    <feature name="appGalleryDevice">
        <param
            name="android-package"
            value="org.apache.cordova.rotate.RotatePlugin"/>
    </feature>
    <feature name="appGalleryFileSystem">
        <param
            name="android-package"
            value="org.apache.cordova.gallery.FileSystemPlugin"/>
    </feature>
    <feature name="appGalleryMedia">
        <param
            name="android-package"
            value="org.apache.cordova.gallery.XRemoteMediaPlugin"/>
    </feature>
    <feature name="appGalleryPattern">
        <param
            name="android-package"
            value="org.apache.cordova.gallery.PatternPlugin"/>
    </feature>
    <feature name="appGalleryUI">
        <param
            name="android-package"
            value="org.apache.cordova.gallery.UIManagerPlugin"/>
    </feature>
    <feature name="appGalleryAICompanion">
        <param
            name="android-package"
            value="org.apache.cordova.gallery.AICompanionPlugin"/>
    </feature>
</widget>
