package ew;

import com.google.firebase.analytics.FirebaseAnalytics;
import com.wear.bean.ProgramPattern;
import cw.f;
import java.lang.annotation.Annotation;
import java.util.List;
import kotlin.KotlinNothingValueException;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jivesoftware.smackx.xhtmlim.XHTMLText;

/* compiled from: Primitives.kt */
@Metadata(d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0001\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010 \n\u0002\u0010\u001b\n\u0002\b\u0004\n\u0002\u0010\u0000\n\u0002\b\u000e\b\u0000\u0018\u00002\u00020\u0001B\u0017\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004¢\u0006\u0004\b\u0006\u0010\u0007J\u000f\u0010\t\u001a\u00020\bH\u0002¢\u0006\u0004\b\t\u0010\nJ\u0017\u0010\r\u001a\u00020\u00022\u0006\u0010\f\u001a\u00020\u000bH\u0016¢\u0006\u0004\b\r\u0010\u000eJ\u0017\u0010\u0010\u001a\u00020\u000b2\u0006\u0010\u000f\u001a\u00020\u0002H\u0016¢\u0006\u0004\b\u0010\u0010\u0011J\u0017\u0010\u0013\u001a\u00020\u00122\u0006\u0010\f\u001a\u00020\u000bH\u0016¢\u0006\u0004\b\u0013\u0010\u0014J\u0017\u0010\u0015\u001a\u00020\u00012\u0006\u0010\f\u001a\u00020\u000bH\u0016¢\u0006\u0004\b\u0015\u0010\u0016J\u001d\u0010\u0019\u001a\b\u0012\u0004\u0012\u00020\u00180\u00172\u0006\u0010\f\u001a\u00020\u000bH\u0016¢\u0006\u0004\b\u0019\u0010\u001aJ\u000f\u0010\u001b\u001a\u00020\u0002H\u0016¢\u0006\u0004\b\u001b\u0010\u001cJ\u001a\u0010\u001f\u001a\u00020\u00122\b\u0010\u001e\u001a\u0004\u0018\u00010\u001dH\u0096\u0002¢\u0006\u0004\b\u001f\u0010 J\u000f\u0010!\u001a\u00020\u000bH\u0016¢\u0006\u0004\b!\u0010\"R\u001a\u0010\u0003\u001a\u00020\u00028\u0016X\u0096\u0004¢\u0006\f\n\u0004\b\t\u0010#\u001a\u0004\b$\u0010\u001cR\u001a\u0010\u0005\u001a\u00020\u00048\u0016X\u0096\u0004¢\u0006\f\n\u0004\b%\u0010&\u001a\u0004\b'\u0010(R\u0014\u0010*\u001a\u00020\u000b8VX\u0096\u0004¢\u0006\u0006\u001a\u0004\b)\u0010\"¨\u0006+"}, d2 = {"Lew/n2;", "Lcw/f;", "", "serialName", "Lcw/e;", "kind", "<init>", "(Ljava/lang/String;Lcw/e;)V", "", "a", "()Ljava/lang/Void;", "", FirebaseAnalytics.Param.INDEX, "e", "(I)Ljava/lang/String;", "name", "c", "(Ljava/lang/String;)I", "", jl.i.f32548i, "(I)Z", "g", "(I)Lcw/f;", "", "", ProgramPattern.writePatternChar100, "(I)Ljava/util/List;", "toString", "()Ljava/lang/String;", "", "other", "equals", "(Ljava/lang/Object;)Z", "hashCode", "()I", "Ljava/lang/String;", XHTMLText.H, r0.b.f37717b, "Lcw/e;", "j", "()Lcw/e;", "d", "elementsCount", "kotlinx-serialization-core"}, k = 1, mv = {2, 0, 0}, xi = 48)
/* renamed from: ew.n2, reason: from toString */
/* loaded from: classes6.dex */
public final class PrimitiveDescriptor implements cw.f {

    /* renamed from: a, reason: collision with root package name and from kotlin metadata */
    @NotNull
    public final String serialName;

    /* renamed from: b, reason: collision with root package name and from kotlin metadata */
    @NotNull
    public final cw.e kind;

    public PrimitiveDescriptor(@NotNull String serialName, @NotNull cw.e kind) {
        Intrinsics.checkNotNullParameter(serialName, "serialName");
        Intrinsics.checkNotNullParameter(kind, "kind");
        this.serialName = serialName;
        this.kind = kind;
    }

    private final Void a() {
        throw new IllegalStateException("Primitive descriptor does not have elements");
    }

    @Override // cw.f
    public boolean b() {
        return f.a.c(this);
    }

    @Override // cw.f
    public int c(@NotNull String name) {
        Intrinsics.checkNotNullParameter(name, "name");
        a();
        throw new KotlinNothingValueException();
    }

    @Override // cw.f
    /* renamed from: d */
    public int getElementsCount() {
        return 0;
    }

    @Override // cw.f
    @NotNull
    public String e(int index) {
        a();
        throw new KotlinNothingValueException();
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof PrimitiveDescriptor)) {
            return false;
        }
        PrimitiveDescriptor primitiveDescriptor = (PrimitiveDescriptor) other;
        return Intrinsics.areEqual(getSerialName(), primitiveDescriptor.getSerialName()) && Intrinsics.areEqual(getKind(), primitiveDescriptor.getKind());
    }

    @Override // cw.f
    @NotNull
    public List<Annotation> f(int index) {
        a();
        throw new KotlinNothingValueException();
    }

    @Override // cw.f
    @NotNull
    public cw.f g(int index) {
        a();
        throw new KotlinNothingValueException();
    }

    @Override // cw.f
    @NotNull
    public List<Annotation> getAnnotations() {
        return f.a.a(this);
    }

    @Override // cw.f
    @NotNull
    /* renamed from: h, reason: from getter */
    public String getSerialName() {
        return this.serialName;
    }

    public int hashCode() {
        return getSerialName().hashCode() + (getKind().hashCode() * 31);
    }

    @Override // cw.f
    public boolean i(int index) {
        a();
        throw new KotlinNothingValueException();
    }

    @Override // cw.f
    public boolean isInline() {
        return f.a.b(this);
    }

    @Override // cw.f
    @NotNull
    /* renamed from: j, reason: from getter */
    public cw.e getKind() {
        return this.kind;
    }

    @NotNull
    public String toString() {
        return "PrimitiveDescriptor(" + getSerialName() + ')';
    }
}
