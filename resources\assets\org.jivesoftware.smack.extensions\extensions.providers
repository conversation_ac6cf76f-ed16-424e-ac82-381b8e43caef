<?xml version="1.0"?> 
<!-- Providers file for default Smack extensions -->
<smackProviders> 

    <!-- Private Data Storage -->
    <iqProvider> 
        <elementName>query</elementName>
        <namespace>jabber:iq:private</namespace>
        <className>org.jivesoftware.smackx.iqprivate.PrivateDataManager$PrivateDataIQProvider</className>
    </iqProvider>

    <!-- Time -->
    <iqProvider> 
        <elementName>time</elementName>
        <namespace>urn:xmpp:time</namespace>
        <className>org.jivesoftware.smackx.time.provider.TimeProvider</className>
    </iqProvider>

    <!-- Chat State -->
    <extensionProvider>
        <elementName>active</elementName>
        <namespace>http://jabber.org/protocol/chatstates</namespace>
        <className>org.jivesoftware.smackx.chatstates.packet.ChatStateExtension$Provider</className>
    </extensionProvider>

    <extensionProvider>
        <elementName>composing</elementName>
        <namespace>http://jabber.org/protocol/chatstates</namespace>
        <className>org.jivesoftware.smackx.chatstates.packet.ChatStateExtension$Provider</className>
    </extensionProvider>

    <extensionProvider>
        <elementName>paused</elementName>
        <namespace>http://jabber.org/protocol/chatstates</namespace>
        <className>org.jivesoftware.smackx.chatstates.packet.ChatStateExtension$Provider</className>
    </extensionProvider>

    <extensionProvider>
        <elementName>inactive</elementName>
        <namespace>http://jabber.org/protocol/chatstates</namespace>
        <className>org.jivesoftware.smackx.chatstates.packet.ChatStateExtension$Provider</className>
    </extensionProvider>

    <extensionProvider>
        <elementName>gone</elementName>
        <namespace>http://jabber.org/protocol/chatstates</namespace>
        <className>org.jivesoftware.smackx.chatstates.packet.ChatStateExtension$Provider</className>
    </extensionProvider>
    
    <!-- XHTML -->
    <extensionProvider>
        <elementName>html</elementName>
        <namespace>http://jabber.org/protocol/xhtml-im</namespace>
        <className>org.jivesoftware.smackx.xhtmlim.provider.XHTMLExtensionProvider</className>
    </extensionProvider>

    <!-- Group Chat Invitations -->
    <extensionProvider>
        <elementName>x</elementName>
        <namespace>jabber:x:conference</namespace>
        <className>org.jivesoftware.smackx.muc.packet.GroupChatInvitation$Provider</className>
    </extensionProvider>	

    <!-- Service Discovery # Items -->
    <iqProvider> 
        <elementName>query</elementName> 
        <namespace>http://jabber.org/protocol/disco#items</namespace> 
        <className>org.jivesoftware.smackx.disco.provider.DiscoverItemsProvider</className> 
    </iqProvider>

            <iqProvider>
                <elementName>query</elementName>
                <namespace>http://www.jivesoftware.org/protocol/room</namespace>
                <className>org.jivesoftware.smackx.disco.provider.DafultProvider</className>
            </iqProvider>

    <!-- Service Discovery # Info -->
    <iqProvider>
        <elementName>query</elementName>
        <namespace>http://jabber.org/protocol/disco#info</namespace>
        <className>org.jivesoftware.smackx.disco.provider.DiscoverInfoProvider</className>
    </iqProvider>



    <!-- Data Forms-->
    <extensionProvider>
        <elementName>x</elementName>
        <namespace>jabber:x:data</namespace>
        <className>org.jivesoftware.smackx.xdata.provider.DataFormProvider</className>
    </extensionProvider>

    <!-- MUC User -->
    <extensionProvider>
        <elementName>x</elementName>
        <namespace>http://jabber.org/protocol/muc#user</namespace>
        <className>org.jivesoftware.smackx.muc.provider.MUCUserProvider</className>
    </extensionProvider>

    <!-- MUC Admin -->
    <iqProvider> 
        <elementName>query</elementName> 
        <namespace>http://jabber.org/protocol/muc#admin</namespace> 
        <className>org.jivesoftware.smackx.muc.provider.MUCAdminProvider</className> 
    </iqProvider>
    <!-- MUC Member -->
    <iqProvider>
        <elementName>query</elementName>
        <namespace>http://www.jivesoftware.org/protocol/room#members</namespace>
        <className>org.jivesoftware.smackx.muc.provider.MUCMemberProvider</className>
    </iqProvider>
    <!-- MUC Owner -->
    <iqProvider> 
        <elementName>query</elementName> 
        <namespace>http://jabber.org/protocol/muc#owner</namespace> 
        <className>org.jivesoftware.smackx.muc.provider.MUCOwnerProvider</className> 
    </iqProvider>

    <!-- Delayed Delivery -->
    <extensionProvider>
        <elementName>x</elementName>
        <namespace>jabber:x:delay</namespace>
        <className>org.jivesoftware.smackx.delay.provider.LegacyDelayInformationProvider</className>
    </extensionProvider>
    
    <extensionProvider>
    	<elementName>delay</elementName>
        <namespace>urn:xmpp:delay</namespace>
        <className>org.jivesoftware.smackx.delay.provider.DelayInformationProvider</className>
    </extensionProvider>

    <!-- Version -->
    <iqProvider>
        <elementName>query</elementName>
        <namespace>jabber:iq:version</namespace>
        <className>org.jivesoftware.smackx.iqversion.provider.VersionProvider</className>
    </iqProvider>

    <!-- VCard -->
    <iqProvider>
        <elementName>vCard</elementName>
        <namespace>vcard-temp</namespace>
        <className>org.jivesoftware.smackx.vcardtemp.provider.VCardProvider</className> 
    </iqProvider>

    <!-- Offline Message Requests -->
    <iqProvider>
        <elementName>offline</elementName>
        <namespace>http://jabber.org/protocol/offline</namespace>
        <className>org.jivesoftware.smackx.offline.packet.OfflineMessageRequest$Provider</className>
    </iqProvider>

    <!-- Offline Message Indicator -->
    <extensionProvider>
        <elementName>offline</elementName>
        <namespace>http://jabber.org/protocol/offline</namespace>
        <className>org.jivesoftware.smackx.offline.packet.OfflineMessageInfo$Provider</className>
    </extensionProvider>

    <!-- Last Activity -->
    <iqProvider>
        <elementName>query</elementName>
        <namespace>jabber:iq:last</namespace>
        <className>org.jivesoftware.smackx.iqlast.packet.LastActivity$Provider</className> 
    </iqProvider>

    <!-- User Search -->
    <iqProvider>
        <elementName>query</elementName>
        <namespace>jabber:iq:search</namespace>
        <className>org.jivesoftware.smackx.search.UserSearch$Provider</className>
    </iqProvider>

    <!-- SharedGroupsInfo -->
    <iqProvider>
        <elementName>sharedgroup</elementName>
        <namespace>http://www.jivesoftware.org/protocol/sharedgroup</namespace>
        <className>org.jivesoftware.smackx.sharedgroups.packet.SharedGroupsInfo$Provider</className>
    </iqProvider>

    <!-- XEP-33: Extended Stanza Addressing -->
    <extensionProvider>
        <elementName>addresses</elementName>
        <namespace>http://jabber.org/protocol/address</namespace>
        <className>org.jivesoftware.smackx.address.provider.MultipleAddressesProvider</className>
    </extensionProvider>

    <!-- FileTransfer -->
    <iqProvider>
    	<elementName>si</elementName>
    	<namespace>http://jabber.org/protocol/si</namespace>
    	<className>org.jivesoftware.smackx.si.provider.StreamInitiationProvider</className>
    </iqProvider>

    <iqProvider>
    	<elementName>query</elementName>
    	<namespace>http://jabber.org/protocol/bytestreams</namespace>
    	<className>org.jivesoftware.smackx.bytestreams.socks5.provider.BytestreamsProvider</className>
    </iqProvider>

    <iqProvider>
    	<elementName>open</elementName>
    	<namespace>http://jabber.org/protocol/ibb</namespace>
    	<className>org.jivesoftware.smackx.bytestreams.ibb.provider.OpenIQProvider</className>
    </iqProvider>

    <iqProvider>
      <elementName>data</elementName>
      <namespace>http://jabber.org/protocol/ibb</namespace>
      <className>org.jivesoftware.smackx.bytestreams.ibb.provider.DataPacketProvider$IQProvider</className>
    </iqProvider>

    <iqProvider>
    	<elementName>close</elementName>
    	<namespace>http://jabber.org/protocol/ibb</namespace>
    	<className>org.jivesoftware.smackx.bytestreams.ibb.provider.CloseIQProvider</className>
    </iqProvider>

    <extensionProvider>
        <elementName>data</elementName>
        <namespace>http://jabber.org/protocol/ibb</namespace>
        <className>org.jivesoftware.smackx.bytestreams.ibb.provider.DataPacketProvider$PacketExtensionProvider</className>
    </extensionProvider>

    <!-- Ad-Hoc Command -->
    <iqProvider>
        <elementName>command</elementName>
        <namespace>http://jabber.org/protocol/commands</namespace>
        <className>org.jivesoftware.smackx.commands.provider.AdHocCommandDataProvider</className>
    </iqProvider>

    <extensionProvider>
        <elementName>bad-action</elementName>
        <namespace>http://jabber.org/protocol/commands</namespace>
        <className>org.jivesoftware.smackx.commands.provider.AdHocCommandDataProvider$BadActionError</className>
    </extensionProvider>

    <extensionProvider>
        <elementName>malformed-actionn</elementName>
        <namespace>http://jabber.org/protocol/commands</namespace>
        <className>org.jivesoftware.smackx.commands.provider.AdHocCommandDataProvider$MalformedActionError</className>
    </extensionProvider>

    <extensionProvider>
        <elementName>bad-locale</elementName>
        <namespace>http://jabber.org/protocol/commands</namespace>
        <className>org.jivesoftware.smackx.commands.provider.AdHocCommandDataProvider$BadLocaleError</className>
    </extensionProvider>

    <extensionProvider>
        <elementName>bad-payload</elementName>
        <namespace>http://jabber.org/protocol/commands</namespace>
        <className>org.jivesoftware.smackx.commands.provider.AdHocCommandDataProvider$BadPayloadError</className>
    </extensionProvider>

    <extensionProvider>
        <elementName>bad-sessionid</elementName>
        <namespace>http://jabber.org/protocol/commands</namespace>
        <className>org.jivesoftware.smackx.commands.provider.AdHocCommandDataProvider$BadSessionIDError</className>
    </extensionProvider>

    <extensionProvider>
        <elementName>session-expired</elementName>
        <namespace>http://jabber.org/protocol/commands</namespace>
        <className>org.jivesoftware.smackx.commands.provider.AdHocCommandDataProvider$SessionExpiredError</className>
    </extensionProvider>

    <!-- SHIM -->
    <extensionProvider>
    	<elementName>headers</elementName>
        <namespace>http://jabber.org/protocol/shim</namespace>
        <className>org.jivesoftware.smackx.shim.provider.HeadersProvider</className>
    </extensionProvider>

    <extensionProvider>
    	<elementName>header</elementName>
        <namespace>http://jabber.org/protocol/shim</namespace>
        <className>org.jivesoftware.smackx.shim.provider.HeaderProvider</className>
    </extensionProvider>

    <!-- XEP-0060 pubsub -->
    <iqProvider>
        <elementName>pubsub</elementName>
        <namespace>http://jabber.org/protocol/pubsub</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.PubSubProvider</className>
    </iqProvider>

    <extensionProvider>
    	<elementName>create</elementName>
        <namespace>http://jabber.org/protocol/pubsub</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.SimpleNodeProvider</className>
    </extensionProvider>

    <extensionProvider>
    	<elementName>items</elementName>
        <namespace>http://jabber.org/protocol/pubsub</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.ItemsProvider</className>
    </extensionProvider>

    <extensionProvider>
    	<elementName>item</elementName>
        <namespace>http://jabber.org/protocol/pubsub</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.ItemProvider</className>
    </extensionProvider>

    <extensionProvider>
    	<elementName>subscriptions</elementName>
        <namespace>http://jabber.org/protocol/pubsub</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.SubscriptionsProvider</className>
    </extensionProvider>

    <extensionProvider>
    	<elementName>subscription</elementName>
        <namespace>http://jabber.org/protocol/pubsub</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.SubscriptionProvider</className>
    </extensionProvider>

    <extensionProvider>
    	<elementName>affiliations</elementName>
        <namespace>http://jabber.org/protocol/pubsub</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.AffiliationsProvider</className>
    </extensionProvider>

    <extensionProvider>
    	<elementName>affiliation</elementName>
        <namespace>http://jabber.org/protocol/pubsub</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.AffiliationProvider</className>
    </extensionProvider>

    <extensionProvider>
    	<elementName>options</elementName>
        <namespace>http://jabber.org/protocol/pubsub</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.FormNodeProvider</className>
    </extensionProvider>
	
    <!-- XEP-0060 pubsub#owner -->
    <iqProvider>
        <elementName>pubsub</elementName>
        <namespace>http://jabber.org/protocol/pubsub#owner</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.PubSubProvider</className>
    </iqProvider>

    <extensionProvider>
    	<elementName>configure</elementName>
        <namespace>http://jabber.org/protocol/pubsub#owner</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.FormNodeProvider</className>
    </extensionProvider>

    <extensionProvider>
    	<elementName>default</elementName>
        <namespace>http://jabber.org/protocol/pubsub#owner</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.FormNodeProvider</className>
    </extensionProvider>

	<extensionProvider>
		<elementName>subscriptions</elementName>
		<namespace>http://jabber.org/protocol/pubsub#owner</namespace>
		<className>org.jivesoftware.smackx.pubsub.provider.SubscriptionsProvider</className>
	</extensionProvider>

	<extensionProvider>
		<elementName>subscription</elementName>
		<namespace>http://jabber.org/protocol/pubsub#owner</namespace>
		<className>org.jivesoftware.smackx.pubsub.provider.SubscriptionProvider</className>
	</extensionProvider>

    <!-- XEP-0060 pubsub#event -->
    <extensionProvider>
    	<elementName>event</elementName>
        <namespace>http://jabber.org/protocol/pubsub#event</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.EventProvider</className>
    </extensionProvider>

    <extensionProvider>
    	<elementName>configuration</elementName>
        <namespace>http://jabber.org/protocol/pubsub#event</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.ConfigEventProvider</className>
    </extensionProvider>

    <extensionProvider>
    	<elementName>delete</elementName>
        <namespace>http://jabber.org/protocol/pubsub#event</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.SimpleNodeProvider</className>
    </extensionProvider>

    <extensionProvider>
    	<elementName>options</elementName>
        <namespace>http://jabber.org/protocol/pubsub#event</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.FormNodeProvider</className>
    </extensionProvider>

    <extensionProvider>
    	<elementName>items</elementName>
        <namespace>http://jabber.org/protocol/pubsub#event</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.ItemsProvider</className>
    </extensionProvider>

    <extensionProvider>
    	<elementName>item</elementName>
        <namespace>http://jabber.org/protocol/pubsub#event</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.ItemProvider</className>
    </extensionProvider>

    <extensionProvider>
    	<elementName>retract</elementName>
        <namespace>http://jabber.org/protocol/pubsub#event</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.RetractEventProvider</className>
    </extensionProvider>

    <extensionProvider>
    	<elementName>purge</elementName>
        <namespace>http://jabber.org/protocol/pubsub#event</namespace>
        <className>org.jivesoftware.smackx.pubsub.provider.SimpleNodeProvider</className>
    </extensionProvider>

    <!-- Nick Exchange -->
    <extensionProvider>
        <elementName>nick</elementName>
        <namespace>http://jabber.org/protocol/nick</namespace>
        <className>org.jivesoftware.smackx.nick.packet.Nick$Provider</className>
    </extensionProvider>

    <!-- Attention -->
    <extensionProvider>
        <elementName>attention</elementName>
        <namespace>urn:xmpp:attention:0</namespace>
        <className>org.jivesoftware.smackx.attention.packet.AttentionExtension$Provider</className>
    </extensionProvider>

    <!-- XEP-0184 Message Delivery Receipts -->
    <extensionProvider>
        <elementName>received</elementName>
        <namespace>urn:xmpp:receipts</namespace>
        <className>org.jivesoftware.smackx.receipts.DeliveryReceipt$Provider</className>
    </extensionProvider>
    <extensionProvider>
        <elementName>request</elementName>
        <namespace>urn:xmpp:receipts</namespace>
        <className>org.jivesoftware.smackx.receipts.DeliveryReceiptRequest$Provider</className>
    </extensionProvider>

    <!-- XEP-0115 Entity Capabilities -->
    <extensionProvider>
        <elementName>c</elementName>
        <namespace>http://jabber.org/protocol/caps</namespace>
        <className>org.jivesoftware.smackx.caps.provider.CapsExtensionProvider</className>
    </extensionProvider>

    <streamFeatureProvider>
        <elementName>c</elementName>
        <namespace>http://jabber.org/protocol/caps</namespace>
        <className>org.jivesoftware.smackx.caps.provider.CapsExtensionProvider</className>
    </streamFeatureProvider>

   	<!-- XEP-0297 Stanza Forwarding -->
 	<extensionProvider>
 	 	<elementName>forwarded</elementName>
 	 	<namespace>urn:xmpp:forward:0</namespace>
		<className>org.jivesoftware.smackx.forward.provider.ForwardedProvider</className>
	</extensionProvider>

    <!-- Ping (XEP-199) Manager -->
    <iqProvider>
        <elementName>ping</elementName>
        <namespace>urn:xmpp:ping</namespace>
        <className>org.jivesoftware.smackx.ping.provider.PingProvider</className>
    </iqProvider>

    <!-- Privacy -->
    <iqProvider>
        <elementName>query</elementName>
        <namespace>jabber:iq:privacy</namespace>
        <className>org.jivesoftware.smackx.privacy.provider.PrivacyProvider</className>
    </iqProvider>

    <!-- XEP-0079 Advanced Message Processing -->
    <extensionProvider>
        <elementName>amp</elementName>
        <namespace>http://jabber.org/protocol/amp</namespace>
        <className>org.jivesoftware.smackx.amp.provider.AMPExtensionProvider</className>
    </extensionProvider>

    <!-- JiveProperties -->
    <extensionProvider>
        <elementName>properties</elementName>
        <namespace>http://www.jivesoftware.com/xmlns/xmpp/properties</namespace>
        <className>org.jivesoftware.smackx.jiveproperties.provider.JivePropertiesExtensionProvider</className>
    </extensionProvider>

    <!-- XEP-0077: In-Band Registration -->
    <iqProvider>
        <elementName>query</elementName>
        <namespace>jabber:iq:register</namespace>
        <className>org.jivesoftware.smackx.iqregister.provider.RegistrationProvider</className>
    </iqProvider>

    <streamFeatureProvider>
        <elementName>register</elementName>
        <namespace>http://jabber.org/features/iq-register</namespace>
        <className>org.jivesoftware.smackx.iqregister.provider.RegistrationStreamFeatureProvider</className>
    </streamFeatureProvider>

   <!-- XEP-0059: Result Set Management -->
   <extensionProvider>
       <elementName>set</elementName>
       <namespace>http://jabber.org/protocol/rsm</namespace>
       <className>org.jivesoftware.smackx.rsm.provider.RSMSetProvider</className>
    </extensionProvider>
</smackProviders>
