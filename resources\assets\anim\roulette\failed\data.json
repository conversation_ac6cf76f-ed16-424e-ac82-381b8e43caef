{"v": "5.6.10", "fr": 24, "ip": 0, "op": 24, "w": 686, "h": 200, "nm": "失败曲线", "ddd": 0, "assets": [{"id": "image_0", "w": 200, "h": 200, "u": "images/", "p": "img_0.png", "e": 0}, {"id": "comp_0", "layers": [{"ddd": 0, "ind": 1, "ty": 2, "nm": "7337-41224.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [100, 100, 0], "ix": 2}, "a": {"a": 0, "k": [100, 100, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 240, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "image 56", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 12, "s": [0]}, {"t": 24, "s": [15]}], "ix": 10}, "p": {"a": 0, "k": [344.625, 126.875, 0], "ix": 2}, "a": {"a": 0, "k": [106.232, 181.981, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1.337]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 6, "s": [50, 50, 100]}, {"t": 10, "s": [40, 40, 100]}], "ix": 6, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 0.06;\n    frequency = 2;\n    decay = 6;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[107.748, -13], [280, -19], [280, 210.965], [99.248, 203.715], [118.565, 144.238], [104.4, 108.227], [115.498, 83], [107.748, 45.5]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "蒙版 1"}], "w": 200, "h": 200, "ip": -96, "op": 144, "st": -96, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "image 55", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 12, "s": [0]}, {"t": 24, "s": [-15]}], "ix": 10}, "p": {"a": 0, "k": [345.375, 126.25, 0], "ix": 2}, "a": {"a": 0, "k": [107.566, 178.585, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [0, 0, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 1.337]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 6, "s": [50, 50, 100]}, {"t": 10, "s": [40, 40, 100]}], "ix": 6, "x": "var $bm_rt;\nvar nearestKeyIndex, nearestKeyIndex, currentTime, currentTime, calculatedVelocity, amplitude, frequency, decay;\n$bm_rt = nearestKeyIndex = 0;\nif (numKeys > 0) {\n    $bm_rt = nearestKeyIndex = nearestKey(time).index;\n    if (key(nearestKeyIndex).time > time) {\n        nearestKeyIndex--;\n    }\n}\nif (nearestKeyIndex == 0) {\n    $bm_rt = currentTime = 0;\n} else {\n    $bm_rt = currentTime = $bm_sub(time, key(nearestKeyIndex).time);\n}\nif (nearestKeyIndex > 0 && currentTime < 1) {\n    calculatedVelocity = velocityAtTime($bm_sub(key(nearestKeyIndex).time, $bm_div(thisComp.frameDuration, 10)));\n    amplitude = 0.06;\n    frequency = 2;\n    decay = 6;\n    $bm_rt = $bm_sum(value, $bm_div($bm_mul($bm_mul(calculatedVelocity, amplitude), Math.sin($bm_mul($bm_mul($bm_mul(frequency, currentTime), 2), Math.PI))), Math.exp($bm_mul(decay, currentTime))));\n} else {\n    $bm_rt = value;\n}"}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[107.748, -13], [-11.5, -13], [-11.5, 216.965], [99.248, 203.715], [118.565, 144.238], [104.4, 108.227], [115.498, 83], [107.748, 45.5]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "蒙版 1"}], "w": 200, "h": 200, "ip": -96, "op": 144, "st": -96, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Vector 179", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [274, 95.75, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-120.5, 6.177], [-22.926, 6.176], [-20.457, 0.412], [-17.165, 6.176], [-9.757, -14], [-2.761, 14], [-1.115, 6.176], [3.412, 6.176], [8.351, -3.706], [12.055, 6.176], [22.344, 6.176], [50.5, 6.176]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.752941176471, 0.752941176471, 0.752941176471, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Vector 178", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 100, "ix": 1}, "e": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 245, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Vector 178", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [415, 97.603, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[118.533, 4.301], [15.44, 4.301], [7.999, 9.265], [3.472, -9.265], [-1.878, 4.324], [-9.286, -0.206], [-17.928, 4.324], [-48.5, 4.324]], "c": false}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.752941176471, 0.752941176471, 0.752941176471, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Vector 177", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 0, "ix": 1}, "e": {"a": 0, "k": 100, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 245, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Vector 177", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 3, "s": [100]}, {"t": 4, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [345.5, 100.25, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[1.597, 0.677], [1.728, 0], [1.597, -0.677], [1.222, -1.251], [1.387, 1.42], [3.49, 0], [2.468, -2.525], [0, -3.571], [-2.468, -2.525], [0, 0], [0, 0], [-1.588, 0], [-2.532, 2.591], [0, 0], [0, 0], [-0.662, 1.634], [0, 1.768], [0.662, 1.634], [1.222, 1.25]], "o": [[-1.597, -0.677], [-1.728, 0], [-1.597, 0.677], [-1.387, 1.42], [-2.468, -2.525], [-3.49, 0], [-2.468, 2.525], [0, 3.571], [0, 0], [0, 0], [2.532, 2.591], [1.588, 0], [0, 0], [0, 0], [1.222, -1.25], [0.662, -1.634], [0, -1.768], [-0.662, -1.634], [-1.222, -1.251]], "v": [[16.876, -20.475], [11.84, -21.5], [6.804, -20.475], [2.535, -17.555], [-2.536, -17.555], [-11.841, -21.499], [-21.146, -17.555], [-25, -8.034], [-21.146, 1.488], [-18.61, 4.082], [-5.386, 17.614], [-0.001, 21.5], [5.385, 17.614], [18.609, 4.082], [21.145, 1.488], [23.998, -2.88], [25, -8.034], [23.998, -13.187], [21.145, -17.555]], "c": true}, "ix": 2, "x": "var $bm_rt;\nvar timeStart, duration, pingPong, quant, quant, t, t, t;\ntry {\n    timeStart = thisProperty.key(1).time;\n    duration = $bm_sub(thisProperty.key(thisProperty.numKeys).time, timeStart);\n    pingPong = false;\n    quant = Math.floor($bm_div($bm_sub(time, timeStart), duration));\n    if (quant < 0)\n        quant = 0;\n    if (quant % 2 == 1 && pingPong == true) {\n        t = $bm_sub($bm_sum($bm_mul(2, timeStart), $bm_mul($bm_sum(quant, 1), duration)), time);\n    } else {\n        t = $bm_sub(time, $bm_mul(quant, duration));\n    }\n} catch (err) {\n    t = time;\n}\n$bm_rt = thisProperty.valueAtTime(t);"}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.752941176471, 0.752941176471, 0.752941176471, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 4, "ix": 5}, "lc": 2, "lj": 2, "bm": 0, "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Vector 176", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}, {"ty": "tm", "s": {"a": 0, "k": 100, "ix": 1}, "e": {"a": 0, "k": 0, "ix": 2}, "o": {"a": 0, "k": 0, "ix": 3}, "m": 1, "ix": 2, "nm": "修剪路径 1", "mn": "ADBE Vector Filter - Trim", "hd": false}], "ip": 0, "op": 6, "st": 0, "bm": 0}], "markers": []}