package bd;

import androidx.compose.foundation.c;
import com.wear.bean.ProgramPattern;
import jl.i;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jivesoftware.smackx.xhtmlim.XHTMLText;

/* compiled from: RemoteInfo.kt */
@Metadata(d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0002\b\t\n\u0002\u0010\u000b\n\u0002\b\u0005\n\u0002\u0010\b\n\u0002\b\u001a\b\u0086\b\u0018\u00002\u00020\u0001BW\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0002\u0012\u0006\u0010\u0006\u001a\u00020\u0002\u0012\u0006\u0010\u0007\u001a\u00020\u0002\u0012\u0006\u0010\b\u001a\u00020\u0002\u0012\u0006\u0010\t\u001a\u00020\u0002\u0012\u0006\u0010\n\u001a\u00020\u0002\u0012\u0006\u0010\u000b\u001a\u00020\u0002\u0012\u0006\u0010\r\u001a\u00020\f¢\u0006\u0004\b\u000e\u0010\u000fJ\u0010\u0010\u0010\u001a\u00020\u0002HÖ\u0001¢\u0006\u0004\b\u0010\u0010\u0011J\u0010\u0010\u0013\u001a\u00020\u0012HÖ\u0001¢\u0006\u0004\b\u0013\u0010\u0014J\u001a\u0010\u0016\u001a\u00020\f2\b\u0010\u0015\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\u0016\u0010\u0017R\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0018\u0010\u0019\u001a\u0004\b\u001a\u0010\u0011R\u0017\u0010\u0004\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u001a\u0010\u0019\u001a\u0004\b\u001b\u0010\u0011R\u0017\u0010\u0005\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u001c\u0010\u0019\u001a\u0004\b\u001d\u0010\u0011R\u0017\u0010\u0006\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u001e\u0010\u0019\u001a\u0004\b\u0018\u0010\u0011R\u0017\u0010\u0007\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u001f\u0010\u0019\u001a\u0004\b \u0010\u0011R\u0017\u0010\b\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b!\u0010\u0019\u001a\u0004\b\"\u0010\u0011R\u0017\u0010\t\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b#\u0010\u0019\u001a\u0004\b$\u0010\u0011R\u0017\u0010\n\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b%\u0010\u0019\u001a\u0004\b&\u0010\u0011R\u0017\u0010\u000b\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b'\u0010\u0019\u001a\u0004\b(\u0010\u0011R\u0017\u0010\r\u001a\u00020\f8\u0006¢\u0006\f\n\u0004\b)\u0010*\u001a\u0004\b\r\u0010+¨\u0006,"}, d2 = {"Lbd/b;", "", "", "shortVersion", "aesKey", "aesIV", "pf", "userAgent", "openId", "sessionId", "deviceId", "osVersion", "", "isDebug", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Z)V", "toString", "()Ljava/lang/String;", "", "hashCode", "()I", "other", "equals", "(Ljava/lang/Object;)Z", "a", "Ljava/lang/String;", r0.b.f37717b, "getAesKey", "c", "getAesIV", "d", "e", "getUserAgent", ProgramPattern.writePatternChar100, "getOpenId", "g", "getSessionId", XHTMLText.H, "getDeviceId", i.f32548i, "getOsVersion", "j", "Z", "()Z", "device"}, k = 1, mv = {2, 0, 0}, xi = 48)
/* renamed from: bd.b, reason: from toString */
/* loaded from: classes4.dex */
public final /* data */ class RemoteInfo {

    /* renamed from: a, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final String shortVersion;

    /* renamed from: b, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final String aesKey;

    /* renamed from: c, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final String aesIV;

    /* renamed from: d, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final String pf;

    /* renamed from: e, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final String userAgent;

    /* renamed from: f, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final String openId;

    /* renamed from: g, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final String sessionId;

    /* renamed from: h, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final String deviceId;

    /* renamed from: i, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final String osVersion;

    /* renamed from: j, reason: collision with root package name and from kotlin metadata and from toString */
    public final boolean isDebug;

    public RemoteInfo(@NotNull String shortVersion, @NotNull String aesKey, @NotNull String aesIV, @NotNull String pf2, @NotNull String userAgent, @NotNull String openId, @NotNull String sessionId, @NotNull String deviceId, @NotNull String osVersion, boolean z10) {
        Intrinsics.checkNotNullParameter(shortVersion, "shortVersion");
        Intrinsics.checkNotNullParameter(aesKey, "aesKey");
        Intrinsics.checkNotNullParameter(aesIV, "aesIV");
        Intrinsics.checkNotNullParameter(pf2, "pf");
        Intrinsics.checkNotNullParameter(userAgent, "userAgent");
        Intrinsics.checkNotNullParameter(openId, "openId");
        Intrinsics.checkNotNullParameter(sessionId, "sessionId");
        Intrinsics.checkNotNullParameter(deviceId, "deviceId");
        Intrinsics.checkNotNullParameter(osVersion, "osVersion");
        this.shortVersion = shortVersion;
        this.aesKey = aesKey;
        this.aesIV = aesIV;
        this.pf = pf2;
        this.userAgent = userAgent;
        this.openId = openId;
        this.sessionId = sessionId;
        this.deviceId = deviceId;
        this.osVersion = osVersion;
        this.isDebug = z10;
    }

    @NotNull
    /* renamed from: a, reason: from getter */
    public final String getPf() {
        return this.pf;
    }

    @NotNull
    /* renamed from: b, reason: from getter */
    public final String getShortVersion() {
        return this.shortVersion;
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof RemoteInfo)) {
            return false;
        }
        RemoteInfo remoteInfo = (RemoteInfo) other;
        return Intrinsics.areEqual(this.shortVersion, remoteInfo.shortVersion) && Intrinsics.areEqual(this.aesKey, remoteInfo.aesKey) && Intrinsics.areEqual(this.aesIV, remoteInfo.aesIV) && Intrinsics.areEqual(this.pf, remoteInfo.pf) && Intrinsics.areEqual(this.userAgent, remoteInfo.userAgent) && Intrinsics.areEqual(this.openId, remoteInfo.openId) && Intrinsics.areEqual(this.sessionId, remoteInfo.sessionId) && Intrinsics.areEqual(this.deviceId, remoteInfo.deviceId) && Intrinsics.areEqual(this.osVersion, remoteInfo.osVersion) && this.isDebug == remoteInfo.isDebug;
    }

    public int hashCode() {
        return (((((((((((((((((this.shortVersion.hashCode() * 31) + this.aesKey.hashCode()) * 31) + this.aesIV.hashCode()) * 31) + this.pf.hashCode()) * 31) + this.userAgent.hashCode()) * 31) + this.openId.hashCode()) * 31) + this.sessionId.hashCode()) * 31) + this.deviceId.hashCode()) * 31) + this.osVersion.hashCode()) * 31) + c.a(this.isDebug);
    }

    @NotNull
    public String toString() {
        return "RemoteInfo(shortVersion=" + this.shortVersion + ", aesKey=" + this.aesKey + ", aesIV=" + this.aesIV + ", pf=" + this.pf + ", userAgent=" + this.userAgent + ", openId=" + this.openId + ", sessionId=" + this.sessionId + ", deviceId=" + this.deviceId + ", osVersion=" + this.osVersion + ", isDebug=" + this.isDebug + ')';
    }
}
