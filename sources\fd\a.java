package fd;

import aw.n;
import com.google.firebase.remoteconfig.FirebaseRemoteConfig;
import com.wear.bean.ProgramPattern;
import ew.c0;
import ew.c2;
import ew.h1;
import ew.h2;
import ew.n0;
import ew.r2;
import hd.LightColor;
import jl.i;
import kotlin.Deprecated;
import kotlin.DeprecationLevel;
import kotlin.Metadata;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlinx.serialization.UnknownFieldException;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jivesoftware.smack.sm.packet.StreamManagement;

/* compiled from: LightColorDTO.kt */
@n
@Metadata(d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0010\u0006\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0011\b\u0087\b\u0018\u0000 02\u00020\u0001:\u0002\b\u0006B/\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0002\u0012\u0006\u0010\u0006\u001a\u00020\u0002\u0012\u0006\u0010\b\u001a\u00020\u0007¢\u0006\u0004\b\t\u0010\nBC\b\u0010\u0012\u0006\u0010\f\u001a\u00020\u000b\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0002\u0012\u0006\u0010\u0006\u001a\u00020\u0002\u0012\u0006\u0010\b\u001a\u00020\u0007\u0012\b\u0010\u000e\u001a\u0004\u0018\u00010\r¢\u0006\u0004\b\t\u0010\u000fJ'\u0010\u0016\u001a\u00020\u00152\u0006\u0010\u0010\u001a\u00020\u00002\u0006\u0010\u0012\u001a\u00020\u00112\u0006\u0010\u0014\u001a\u00020\u0013H\u0001¢\u0006\u0004\b\u0016\u0010\u0017J\r\u0010\b\u001a\u00020\u0018¢\u0006\u0004\b\b\u0010\u0019J\r\u0010\u0006\u001a\u00020\u0018¢\u0006\u0004\b\u0006\u0010\u0019J\u0010\u0010\u001b\u001a\u00020\u001aHÖ\u0001¢\u0006\u0004\b\u001b\u0010\u001cJ\u0010\u0010\u001d\u001a\u00020\u000bHÖ\u0001¢\u0006\u0004\b\u001d\u0010\u001eJ\u001a\u0010!\u001a\u00020 2\b\u0010\u001f\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b!\u0010\"R\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\b\u0010#\u001a\u0004\b$\u0010%R\u0017\u0010\u0004\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0006\u0010#\u001a\u0004\b&\u0010%R\u0017\u0010\u0005\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0016\u0010#\u001a\u0004\b'\u0010%R\u0017\u0010\u0006\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b(\u0010#\u001a\u0004\b)\u0010%R\"\u0010\b\u001a\u00020\u00078\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b*\u0010+\u001a\u0004\b,\u0010-\"\u0004\b.\u0010/¨\u00061"}, d2 = {"Lfd/a;", "", "", i.f32548i, StreamManagement.AckRequest.ELEMENT, "g", r0.b.f37717b, "", "a", "<init>", "(JJJJD)V", "", "seen0", "Lew/r2;", "serializationConstructorMarker", "(IJJJJDLew/r2;)V", "self", "Ldw/d;", "output", "Lcw/f;", "serialDesc", "", "c", "(Lfd/a;Ldw/d;Lcw/f;)V", "Lhd/a;", "()Lhd/a;", "", "toString", "()Ljava/lang/String;", "hashCode", "()I", "other", "", "equals", "(Ljava/lang/Object;)Z", "J", "getI", "()J", "getR", "getG", "d", "getB", "e", "D", "getA", "()D", "setA", "(D)V", "Companion", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
/* renamed from: fd.a, reason: from toString */
/* loaded from: classes4.dex */
public final /* data */ class LightColorDTO {

    /* renamed from: Companion, reason: from kotlin metadata */
    @NotNull
    public static final Companion INSTANCE = new Companion(null);

    /* renamed from: a, reason: from kotlin metadata and from toString */
    public final long i;

    /* renamed from: b, reason: from toString */
    public final long r;

    /* renamed from: c, reason: from kotlin metadata and from toString */
    public final long g;

    /* renamed from: d, reason: from kotlin metadata and from toString */
    public final long b;

    /* renamed from: e, reason: from kotlin metadata and from toString */
    public double a;

    /* compiled from: LightColorDTO.kt */
    @Metadata(d1 = {"\u0000:\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\bÇ\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0003\u0010\u0004J\u001d\u0010\t\u001a\u00020\b2\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u0002¢\u0006\u0004\b\t\u0010\nJ\u0015\u0010\r\u001a\u00020\u00022\u0006\u0010\f\u001a\u00020\u000b¢\u0006\u0004\b\r\u0010\u000eJ\u0017\u0010\u0011\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00100\u000f¢\u0006\u0004\b\u0011\u0010\u0012R\u0017\u0010\u0014\u001a\u00020\u00138\u0006¢\u0006\f\n\u0004\b\u0014\u0010\u0015\u001a\u0004\b\u0016\u0010\u0017¨\u0006\u0018"}, d2 = {"com/remotekmp/lightEffect/data/entities/LightColorDTO.$serializer", "Lew/n0;", "Lfd/a;", "<init>", "()V", "Ldw/f;", "encoder", "value", "", ProgramPattern.writePatternChar100, "(Ldw/f;Lfd/a;)V", "Ldw/e;", "decoder", "e", "(Ldw/e;)Lfd/a;", "", "Law/c;", "d", "()[Law/c;", "Lcw/f;", "descriptor", "Lcw/f;", "getDescriptor", "()Lcw/f;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    @Deprecated(level = DeprecationLevel.HIDDEN, message = "This synthesized declaration should not be used directly")
    /* renamed from: fd.a$a */
    public /* synthetic */ class C0529a implements n0<LightColorDTO> {

        /* renamed from: a */
        @NotNull
        public static final C0529a f30604a;

        @NotNull
        private static final cw.f descriptor;

        static {
            C0529a c0529a = new C0529a();
            f30604a = c0529a;
            h2 h2Var = new h2("com.remotekmp.lightEffect.data.entities.LightColorDTO", c0529a, 5);
            h2Var.o(i.f32548i, false);
            h2Var.o(StreamManagement.AckRequest.ELEMENT, false);
            h2Var.o("g", false);
            h2Var.o(r0.b.f37717b, false);
            h2Var.o("a", false);
            descriptor = h2Var;
        }

        @Override // ew.n0
        @NotNull
        public aw.c<?>[] b() {
            return n0.a.a(this);
        }

        @Override // ew.n0
        @NotNull
        public final aw.c<?>[] d() {
            h1 h1Var = h1.f30272a;
            return new aw.c[]{h1Var, h1Var, h1Var, h1Var, c0.f30233a};
        }

        @Override // aw.b
        @NotNull
        /* renamed from: e */
        public final LightColorDTO a(@NotNull dw.e decoder) {
            int i10;
            long j10;
            long j11;
            long j12;
            long j13;
            double d10;
            Intrinsics.checkNotNullParameter(decoder, "decoder");
            cw.f fVar = descriptor;
            dw.c b10 = decoder.b(fVar);
            if (b10.o()) {
                long p10 = b10.p(fVar, 0);
                long p11 = b10.p(fVar, 1);
                long p12 = b10.p(fVar, 2);
                j10 = b10.p(fVar, 3);
                j11 = p12;
                j12 = p10;
                j13 = p11;
                d10 = b10.A(fVar, 4);
                i10 = 31;
            } else {
                long j14 = 0;
                long j15 = 0;
                long j16 = 0;
                double d11 = 0.0d;
                int i11 = 0;
                boolean z10 = true;
                long j17 = 0;
                while (z10) {
                    int h10 = b10.h(fVar);
                    if (h10 == -1) {
                        z10 = false;
                    } else if (h10 == 0) {
                        j15 = b10.p(fVar, 0);
                        i11 |= 1;
                    } else if (h10 == 1) {
                        j16 = b10.p(fVar, 1);
                        i11 |= 2;
                    } else if (h10 == 2) {
                        j17 = b10.p(fVar, 2);
                        i11 |= 4;
                    } else if (h10 == 3) {
                        j14 = b10.p(fVar, 3);
                        i11 |= 8;
                    } else {
                        if (h10 != 4) {
                            throw new UnknownFieldException(h10);
                        }
                        d11 = b10.A(fVar, 4);
                        i11 |= 16;
                    }
                }
                i10 = i11;
                j10 = j14;
                j11 = j17;
                j12 = j15;
                j13 = j16;
                d10 = d11;
            }
            b10.c(fVar);
            return new LightColorDTO(i10, j12, j13, j11, j10, d10, null);
        }

        @Override // aw.o
        /* renamed from: f */
        public final void c(@NotNull dw.f encoder, @NotNull LightColorDTO value) {
            Intrinsics.checkNotNullParameter(encoder, "encoder");
            Intrinsics.checkNotNullParameter(value, "value");
            cw.f fVar = descriptor;
            dw.d b10 = encoder.b(fVar);
            LightColorDTO.c(value, b10, fVar);
            b10.c(fVar);
        }

        @Override // aw.c, aw.o, aw.b
        @NotNull
        public final cw.f getDescriptor() {
            return descriptor;
        }
    }

    /* compiled from: LightColorDTO.kt */
    @Metadata(d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J\u0013\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004¢\u0006\u0004\b\u0006\u0010\u0007¨\u0006\b"}, d2 = {"Lfd/a$b;", "", "<init>", "()V", "Law/c;", "Lfd/a;", "serializer", "()Law/c;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    /* renamed from: fd.a$b, reason: from kotlin metadata */
    public static final class Companion {
        public Companion() {
        }

        @NotNull
        public final aw.c<LightColorDTO> serializer() {
            return C0529a.f30604a;
        }

        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }
    }

    public /* synthetic */ LightColorDTO(int i10, long j10, long j11, long j12, long j13, double d10, r2 r2Var) {
        if (31 != (i10 & 31)) {
            c2.b(i10, 31, C0529a.f30604a.getDescriptor());
        }
        this.i = j10;
        this.r = j11;
        this.g = j12;
        this.b = j13;
        this.a = d10;
    }

    @JvmStatic
    public static final /* synthetic */ void c(LightColorDTO self, dw.d output, cw.f serialDesc) {
        output.E(serialDesc, 0, self.i);
        output.E(serialDesc, 1, self.r);
        output.E(serialDesc, 2, self.g);
        output.E(serialDesc, 3, self.b);
        output.A(serialDesc, 4, self.a);
    }

    @NotNull
    public final LightColor a() {
        return new LightColor(this.i, this.r, this.g, this.b, this.a);
    }

    @NotNull
    public final LightColor b() {
        return new LightColor(this.i, this.r, this.g, this.b, FirebaseRemoteConfig.DEFAULT_VALUE_FOR_DOUBLE);
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof LightColorDTO)) {
            return false;
        }
        LightColorDTO lightColorDTO = (LightColorDTO) other;
        return this.i == lightColorDTO.i && this.r == lightColorDTO.r && this.g == lightColorDTO.g && this.b == lightColorDTO.b && Double.compare(this.a, lightColorDTO.a) == 0;
    }

    public int hashCode() {
        return (((((((androidx.compose.animation.a.a(this.i) * 31) + androidx.compose.animation.a.a(this.r)) * 31) + androidx.compose.animation.a.a(this.g)) * 31) + androidx.compose.animation.a.a(this.b)) * 31) + androidx.compose.animation.core.b.a(this.a);
    }

    @NotNull
    public String toString() {
        return "LightColorDTO(i=" + this.i + ", r=" + this.r + ", g=" + this.g + ", b=" + this.b + ", a=" + this.a + ')';
    }

    public LightColorDTO(long j10, long j11, long j12, long j13, double d10) {
        this.i = j10;
        this.r = j11;
        this.g = j12;
        this.b = j13;
        this.a = d10;
    }
}
