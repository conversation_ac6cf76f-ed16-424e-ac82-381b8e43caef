<smack>
    <startupClasses>
        <className>org.jivesoftware.smackx.disco.ServiceDiscoveryManager</className>
        <className>org.jivesoftware.smackx.xhtmlim.XHTMLManager</className>
        <className>org.jivesoftware.smackx.muc.MultiUserChatManager</className>
        <className>org.jivesoftware.smackx.bytestreams.ibb.InBandBytestreamManager</className>
        <className>org.jivesoftware.smackx.bytestreams.socks5.Socks5BytestreamManager</className>
        <className>org.jivesoftware.smackx.filetransfer.FileTransferManager</className>
        <className>org.jivesoftware.smackx.iqlast.LastActivityManager</className>
        <className>org.jivesoftware.smackx.commands.AdHocCommandManager</className>
        <className>org.jivesoftware.smackx.ping.PingManager</className>
        <className>org.jivesoftware.smackx.privacy.PrivacyListManager</className>
        <className>org.jivesoftware.smackx.time.EntityTimeManager</className>
        <className>org.jivesoftware.smackx.vcardtemp.VCardManager</className>
        <className>org.jivesoftware.smackx.xdata.XDataManager</className>
        <className>org.jivesoftware.smackx.xdatalayout.XDataLayoutManager</className>
        <className>org.jivesoftware.smackx.xdatavalidation.XDataValidationManager</className>
		<className>org.jivesoftware.smackx.receipts.DeliveryReceiptManager</className>
		<className>org.jivesoftware.smackx.iqversion.VersionManager</className>
		<className>org.jivesoftware.smackx.caps.EntityCapsManager</className>
    </startupClasses>
</smack>

