package fd;

import aw.n;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.wear.bean.ProgramPattern;
import ew.c2;
import ew.h1;
import ew.h2;
import ew.n0;
import ew.r2;
import hd.LightCommand;
import java.util.List;
import kotlin.Deprecated;
import kotlin.DeprecationLevel;
import kotlin.Metadata;
import kotlin.jvm.JvmField;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlinx.serialization.UnknownFieldException;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* compiled from: LightCommandDTO.kt */
@n
@Metadata(d1 = {"\u0000P\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010 \n\u0002\b\u0005\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u0011\b\u0087\b\u0018\u0000 02\u00020\u0001:\u0002#\u0018B-\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\f\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00020\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0002\u0012\u0006\u0010\u0007\u001a\u00020\u0002¢\u0006\u0004\b\b\u0010\tBC\b\u0010\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u000e\u0010\u0005\u001a\n\u0012\u0004\u0012\u00020\u0002\u0018\u00010\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0002\u0012\u0006\u0010\u0007\u001a\u00020\u0002\u0012\b\u0010\r\u001a\u0004\u0018\u00010\f¢\u0006\u0004\b\b\u0010\u000eJ'\u0010\u0015\u001a\u00020\u00142\u0006\u0010\u000f\u001a\u00020\u00002\u0006\u0010\u0011\u001a\u00020\u00102\u0006\u0010\u0013\u001a\u00020\u0012H\u0001¢\u0006\u0004\b\u0015\u0010\u0016J\r\u0010\u0018\u001a\u00020\u0017¢\u0006\u0004\b\u0018\u0010\u0019J\u0010\u0010\u001b\u001a\u00020\u001aHÖ\u0001¢\u0006\u0004\b\u001b\u0010\u001cJ\u0010\u0010\u001d\u001a\u00020\nHÖ\u0001¢\u0006\u0004\b\u001d\u0010\u001eJ\u001a\u0010!\u001a\u00020 2\b\u0010\u001f\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b!\u0010\"R\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b#\u0010$\u001a\u0004\b%\u0010&R\u001d\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00020\u00048\u0006¢\u0006\f\n\u0004\b\u0018\u0010'\u001a\u0004\b(\u0010)R\"\u0010\u0006\u001a\u00020\u00028\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\u0015\u0010$\u001a\u0004\b*\u0010&\"\u0004\b+\u0010,R\"\u0010\u0007\u001a\u00020\u00028\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b-\u0010$\u001a\u0004\b.\u0010&\"\u0004\b/\u0010,¨\u00061"}, d2 = {"Lfd/c;", "", "", FirebaseAnalytics.Param.INDEX, "", "ledList", "mode", "freq", "<init>", "(JLjava/util/List;JJ)V", "", "seen0", "Lew/r2;", "serializationConstructorMarker", "(IJLjava/util/List;JJLew/r2;)V", "self", "Ldw/d;", "output", "Lcw/f;", "serialDesc", "", "c", "(Lfd/c;Ldw/d;Lcw/f;)V", "Lhd/c;", r0.b.f37717b, "()Lhd/c;", "", "toString", "()Ljava/lang/String;", "hashCode", "()I", "other", "", "equals", "(Ljava/lang/Object;)Z", "a", "J", "getIndex", "()J", "Ljava/util/List;", "getLedList", "()Ljava/util/List;", "getMode", "setMode", "(J)V", "d", "getFreq", "setFreq", "Companion", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
/* renamed from: fd.c, reason: from toString */
/* loaded from: classes4.dex */
public final /* data */ class LightCommandDTO {

    /* renamed from: Companion, reason: from kotlin metadata */
    @NotNull
    public static final Companion INSTANCE = new Companion(null);

    /* renamed from: e, reason: collision with root package name */
    @JvmField
    @NotNull
    public static final aw.c<Object>[] f30612e = {null, new ew.f(h1.f30272a), null, null};

    /* renamed from: a, reason: collision with root package name and from kotlin metadata and from toString */
    public final long index;

    /* renamed from: b, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final List<Long> ledList;

    /* renamed from: c, reason: collision with root package name and from kotlin metadata and from toString */
    public long mode;

    /* renamed from: d, reason: collision with root package name and from kotlin metadata and from toString */
    public long freq;

    /* compiled from: LightCommandDTO.kt */
    @Metadata(d1 = {"\u0000:\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\bÇ\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0003\u0010\u0004J\u001d\u0010\t\u001a\u00020\b2\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u0002¢\u0006\u0004\b\t\u0010\nJ\u0015\u0010\r\u001a\u00020\u00022\u0006\u0010\f\u001a\u00020\u000b¢\u0006\u0004\b\r\u0010\u000eJ\u0017\u0010\u0011\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00100\u000f¢\u0006\u0004\b\u0011\u0010\u0012R\u0017\u0010\u0014\u001a\u00020\u00138\u0006¢\u0006\f\n\u0004\b\u0014\u0010\u0015\u001a\u0004\b\u0016\u0010\u0017¨\u0006\u0018"}, d2 = {"com/remotekmp/lightEffect/data/entities/LightCommandDTO.$serializer", "Lew/n0;", "Lfd/c;", "<init>", "()V", "Ldw/f;", "encoder", "value", "", ProgramPattern.writePatternChar100, "(Ldw/f;Lfd/c;)V", "Ldw/e;", "decoder", "e", "(Ldw/e;)Lfd/c;", "", "Law/c;", "d", "()[Law/c;", "Lcw/f;", "descriptor", "Lcw/f;", "getDescriptor", "()Lcw/f;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    @Deprecated(level = DeprecationLevel.HIDDEN, message = "This synthesized declaration should not be used directly")
    /* renamed from: fd.c$a */
    public /* synthetic */ class a implements n0<LightCommandDTO> {

        /* renamed from: a, reason: collision with root package name */
        @NotNull
        public static final a f30617a;

        @NotNull
        private static final cw.f descriptor;

        static {
            a aVar = new a();
            f30617a = aVar;
            h2 h2Var = new h2("com.remotekmp.lightEffect.data.entities.LightCommandDTO", aVar, 4);
            h2Var.o(FirebaseAnalytics.Param.INDEX, false);
            h2Var.o("ledList", false);
            h2Var.o("mode", false);
            h2Var.o("freq", false);
            descriptor = h2Var;
        }

        @Override // ew.n0
        @NotNull
        public aw.c<?>[] b() {
            return n0.a.a(this);
        }

        @Override // ew.n0
        @NotNull
        public final aw.c<?>[] d() {
            aw.c<?> cVar = LightCommandDTO.f30612e[1];
            h1 h1Var = h1.f30272a;
            return new aw.c[]{h1Var, cVar, h1Var, h1Var};
        }

        @Override // aw.b
        @NotNull
        /* renamed from: e, reason: merged with bridge method [inline-methods] */
        public final LightCommandDTO a(@NotNull dw.e decoder) {
            int i10;
            List list;
            long j10;
            long j11;
            long j12;
            Intrinsics.checkNotNullParameter(decoder, "decoder");
            cw.f fVar = descriptor;
            dw.c b10 = decoder.b(fVar);
            aw.c[] cVarArr = LightCommandDTO.f30612e;
            List list2 = null;
            if (b10.o()) {
                long p10 = b10.p(fVar, 0);
                List list3 = (List) b10.H(fVar, 1, cVarArr[1], null);
                long p11 = b10.p(fVar, 2);
                list = list3;
                j10 = b10.p(fVar, 3);
                j11 = p11;
                j12 = p10;
                i10 = 15;
            } else {
                long j13 = 0;
                long j14 = 0;
                long j15 = 0;
                int i11 = 0;
                boolean z10 = true;
                while (z10) {
                    int h10 = b10.h(fVar);
                    if (h10 == -1) {
                        z10 = false;
                    } else if (h10 == 0) {
                        j15 = b10.p(fVar, 0);
                        i11 |= 1;
                    } else if (h10 == 1) {
                        list2 = (List) b10.H(fVar, 1, cVarArr[1], list2);
                        i11 |= 2;
                    } else if (h10 == 2) {
                        j14 = b10.p(fVar, 2);
                        i11 |= 4;
                    } else {
                        if (h10 != 3) {
                            throw new UnknownFieldException(h10);
                        }
                        j13 = b10.p(fVar, 3);
                        i11 |= 8;
                    }
                }
                i10 = i11;
                list = list2;
                j10 = j13;
                j11 = j14;
                j12 = j15;
            }
            b10.c(fVar);
            return new LightCommandDTO(i10, j12, list, j11, j10, null);
        }

        @Override // aw.o
        /* renamed from: f, reason: merged with bridge method [inline-methods] */
        public final void c(@NotNull dw.f encoder, @NotNull LightCommandDTO value) {
            Intrinsics.checkNotNullParameter(encoder, "encoder");
            Intrinsics.checkNotNullParameter(value, "value");
            cw.f fVar = descriptor;
            dw.d b10 = encoder.b(fVar);
            LightCommandDTO.c(value, b10, fVar);
            b10.c(fVar);
        }

        @Override // aw.c, aw.o, aw.b
        @NotNull
        public final cw.f getDescriptor() {
            return descriptor;
        }
    }

    /* compiled from: LightCommandDTO.kt */
    @Metadata(d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J\u0013\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004¢\u0006\u0004\b\u0006\u0010\u0007¨\u0006\b"}, d2 = {"Lfd/c$b;", "", "<init>", "()V", "Law/c;", "Lfd/c;", "serializer", "()Law/c;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    /* renamed from: fd.c$b, reason: from kotlin metadata */
    public static final class Companion {
        public Companion() {
        }

        @NotNull
        public final aw.c<LightCommandDTO> serializer() {
            return a.f30617a;
        }

        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }
    }

    public /* synthetic */ LightCommandDTO(int i10, long j10, List list, long j11, long j12, r2 r2Var) {
        if (15 != (i10 & 15)) {
            c2.b(i10, 15, a.f30617a.getDescriptor());
        }
        this.index = j10;
        this.ledList = list;
        this.mode = j11;
        this.freq = j12;
    }

    @JvmStatic
    public static final /* synthetic */ void c(LightCommandDTO self, dw.d output, cw.f serialDesc) {
        aw.c<Object>[] cVarArr = f30612e;
        output.E(serialDesc, 0, self.index);
        output.r(serialDesc, 1, cVarArr[1], self.ledList);
        output.E(serialDesc, 2, self.mode);
        output.E(serialDesc, 3, self.freq);
    }

    @NotNull
    public final LightCommand b() {
        return new LightCommand(this.index, this.ledList, this.mode, this.freq);
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof LightCommandDTO)) {
            return false;
        }
        LightCommandDTO lightCommandDTO = (LightCommandDTO) other;
        return this.index == lightCommandDTO.index && Intrinsics.areEqual(this.ledList, lightCommandDTO.ledList) && this.mode == lightCommandDTO.mode && this.freq == lightCommandDTO.freq;
    }

    public int hashCode() {
        return (((((androidx.compose.animation.a.a(this.index) * 31) + this.ledList.hashCode()) * 31) + androidx.compose.animation.a.a(this.mode)) * 31) + androidx.compose.animation.a.a(this.freq);
    }

    @NotNull
    public String toString() {
        return "LightCommandDTO(index=" + this.index + ", ledList=" + this.ledList + ", mode=" + this.mode + ", freq=" + this.freq + ')';
    }

    public LightCommandDTO(long j10, @NotNull List<Long> ledList, long j11, long j12) {
        Intrinsics.checkNotNullParameter(ledList, "ledList");
        this.index = j10;
        this.ledList = ledList;
        this.mode = j11;
        this.freq = j12;
    }
}
