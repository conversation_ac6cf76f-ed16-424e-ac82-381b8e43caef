package com.tencent.qgame.animplayer;

import com.wear.bean.ProgramPattern;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jivesoftware.smack.sm.packet.StreamManagement;
import org.jivesoftware.smackx.xhtmlim.XHTMLText;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import r0.t;

/* compiled from: AnimConfig.kt */
@Metadata(d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b$\n\u0002\u0018\u0002\n\u0002\b\u0012\n\u0002\u0018\u0002\n\u0002\b\u0006\u0018\u0000 <2\u00020\u0001:\u0001\rB\u0007¢\u0006\u0004\b\u0002\u0010\u0003J\u0015\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004¢\u0006\u0004\b\u0007\u0010\bJ\u000f\u0010\n\u001a\u00020\tH\u0016¢\u0006\u0004\b\n\u0010\u000bR\u001a\u0010\u0011\u001a\u00020\f8\u0006X\u0086D¢\u0006\f\n\u0004\b\r\u0010\u000e\u001a\u0004\b\u000f\u0010\u0010R\"\u0010\u0016\u001a\u00020\f8\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\u0012\u0010\u000e\u001a\u0004\b\u0013\u0010\u0010\"\u0004\b\u0014\u0010\u0015R\"\u0010\u001a\u001a\u00020\f8\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\u0017\u0010\u000e\u001a\u0004\b\u0018\u0010\u0010\"\u0004\b\u0019\u0010\u0015R\"\u0010\u001d\u001a\u00020\f8\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\u001b\u0010\u000e\u001a\u0004\b\u001b\u0010\u0010\"\u0004\b\u001c\u0010\u0015R\"\u0010!\u001a\u00020\f8\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\u001e\u0010\u000e\u001a\u0004\b\u001f\u0010\u0010\"\u0004\b \u0010\u0015R\"\u0010%\u001a\u00020\f8\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\"\u0010\u000e\u001a\u0004\b#\u0010\u0010\"\u0004\b$\u0010\u0015R\"\u0010(\u001a\u00020\f8\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b#\u0010\u000e\u001a\u0004\b&\u0010\u0010\"\u0004\b'\u0010\u0015R\"\u0010*\u001a\u00020\f8\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\u001f\u0010\u000e\u001a\u0004\b\u0017\u0010\u0010\"\u0004\b)\u0010\u0015R\"\u00100\u001a\u00020\u00068\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\u0018\u0010+\u001a\u0004\b,\u0010-\"\u0004\b.\u0010/R\"\u00107\u001a\u0002018\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b2\u00103\u001a\u0004\b\r\u00104\"\u0004\b5\u00106R\"\u00109\u001a\u0002018\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b,\u00103\u001a\u0004\b\"\u00104\"\u0004\b8\u00106R\"\u0010;\u001a\u00020\u00068\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\u0007\u0010+\u001a\u0004\b2\u0010-\"\u0004\b:\u0010/R\"\u0010=\u001a\u00020\f8\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b5\u0010\u000e\u001a\u0004\b\u0012\u0010\u0010\"\u0004\b<\u0010\u0015R$\u0010C\u001a\u0004\u0018\u00010\u00048\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b:\u0010>\u001a\u0004\b?\u0010@\"\u0004\bA\u0010BR$\u0010E\u001a\u0004\u0018\u00010D8\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\bE\u0010F\u001a\u0004\b\u001e\u0010G\"\u0004\bH\u0010I¨\u0006J"}, d2 = {"Lcom/tencent/qgame/animplayer/a;", "", "<init>", "()V", "Lorg/json/JSONObject;", "json", "", kl.l.f32926l, "(Lorg/json/JSONObject;)Z", "", "toString", "()Ljava/lang/String;", "", "a", "I", "getVersion", "()I", "version", r0.b.f37717b, "getTotalFrames", "setTotalFrames", "(I)V", "totalFrames", "c", jl.i.f32548i, "v", "width", "d", XHTMLText.Q, "height", "e", XHTMLText.H, "u", "videoWidth", ProgramPattern.writePatternChar100, "g", t.f37752s, "videoHeight", "getOrien", "setOrien", "orien", "p", "fps", "Z", jl.k.f32572f, "()Z", "setMix", "(Z)V", "isMix", "Lcom/tencent/qgame/animplayer/l;", "j", "Lcom/tencent/qgame/animplayer/l;", "()Lcom/tencent/qgame/animplayer/l;", "m", "(Lcom/tencent/qgame/animplayer/l;)V", "alphaPointRect", "s", "rgbPointRect", "n", "isDefaultConfig", "o", "defaultVideoMode", "Lorg/json/JSONObject;", "getJsonConfig", "()Lorg/json/JSONObject;", StreamManagement.AckRequest.ELEMENT, "(Lorg/json/JSONObject;)V", "jsonConfig", "Lef/b;", "maskConfig", "Lef/b;", "()Lef/b;", "setMaskConfig", "(Lef/b;)V", "animplayer_release"}, k = 1, mv = {1, 4, 0})
/* renamed from: com.tencent.qgame.animplayer.a, reason: from toString */
/* loaded from: classes4.dex */
public final class AnimConfig {

    /* renamed from: b, reason: collision with root package name and from kotlin metadata and from toString */
    public int totalFrames;

    /* renamed from: c, reason: collision with root package name and from kotlin metadata and from toString */
    public int width;

    /* renamed from: d, reason: collision with root package name and from kotlin metadata and from toString */
    public int height;

    /* renamed from: e, reason: collision with root package name and from kotlin metadata and from toString */
    public int videoWidth;

    /* renamed from: f, reason: collision with root package name and from kotlin metadata and from toString */
    public int videoHeight;

    /* renamed from: g, reason: collision with root package name and from kotlin metadata and from toString */
    public int orien;

    /* renamed from: h, reason: collision with root package name and from kotlin metadata and from toString */
    public int fps;

    /* renamed from: i, reason: collision with root package name and from kotlin metadata and from toString */
    public boolean isMix;

    /* renamed from: l, reason: collision with root package name and from kotlin metadata and from toString */
    public boolean isDefaultConfig;

    /* renamed from: n, reason: collision with root package name and from kotlin metadata */
    @Nullable
    public JSONObject jsonConfig;

    /* renamed from: a, reason: collision with root package name and from kotlin metadata and from toString */
    public final int version = 2;

    /* renamed from: j, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public PointRect alphaPointRect = new PointRect(0, 0, 0, 0);

    /* renamed from: k, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public PointRect rgbPointRect = new PointRect(0, 0, 0, 0);

    /* renamed from: m, reason: collision with root package name and from kotlin metadata */
    public int defaultVideoMode = 1;

    @NotNull
    /* renamed from: a, reason: from getter */
    public final PointRect getAlphaPointRect() {
        return this.alphaPointRect;
    }

    /* renamed from: b, reason: from getter */
    public final int getDefaultVideoMode() {
        return this.defaultVideoMode;
    }

    /* renamed from: c, reason: from getter */
    public final int getFps() {
        return this.fps;
    }

    /* renamed from: d, reason: from getter */
    public final int getHeight() {
        return this.height;
    }

    @Nullable
    public final ef.b e() {
        return null;
    }

    @NotNull
    /* renamed from: f, reason: from getter */
    public final PointRect getRgbPointRect() {
        return this.rgbPointRect;
    }

    /* renamed from: g, reason: from getter */
    public final int getVideoHeight() {
        return this.videoHeight;
    }

    /* renamed from: h, reason: from getter */
    public final int getVideoWidth() {
        return this.videoWidth;
    }

    /* renamed from: i, reason: from getter */
    public final int getWidth() {
        return this.width;
    }

    /* renamed from: j, reason: from getter */
    public final boolean getIsDefaultConfig() {
        return this.isDefaultConfig;
    }

    /* renamed from: k, reason: from getter */
    public final boolean getIsMix() {
        return this.isMix;
    }

    public final boolean l(@NotNull JSONObject json) {
        Intrinsics.checkParameterIsNotNull(json, "json");
        try {
            JSONObject jSONObject = json.getJSONObject("info");
            int i10 = jSONObject.getInt("v");
            if (this.version != i10) {
                gf.a.f31036a.b("AnimPlayer.AnimConfig", "current version=" + this.version + " target=" + i10);
                return false;
            }
            this.totalFrames = jSONObject.getInt(ProgramPattern.writePatternChar100);
            this.width = jSONObject.getInt("w");
            this.height = jSONObject.getInt(XHTMLText.H);
            this.videoWidth = jSONObject.getInt("videoW");
            this.videoHeight = jSONObject.getInt("videoH");
            this.orien = jSONObject.getInt("orien");
            this.fps = jSONObject.getInt("fps");
            this.isMix = jSONObject.getInt("isVapx") == 1;
            JSONArray jSONArray = jSONObject.getJSONArray("aFrame");
            if (jSONArray != null) {
                this.alphaPointRect = new PointRect(jSONArray.getInt(0), jSONArray.getInt(1), jSONArray.getInt(2), jSONArray.getInt(3));
                JSONArray jSONArray2 = jSONObject.getJSONArray("rgbFrame");
                if (jSONArray2 != null) {
                    this.rgbPointRect = new PointRect(jSONArray2.getInt(0), jSONArray2.getInt(1), jSONArray2.getInt(2), jSONArray2.getInt(3));
                    return true;
                }
            }
            return false;
        } catch (JSONException e10) {
            gf.a.f31036a.c("AnimPlayer.AnimConfig", "json parse fail " + e10, e10);
            return false;
        }
    }

    public final void m(@NotNull PointRect pointRect) {
        Intrinsics.checkParameterIsNotNull(pointRect, "<set-?>");
        this.alphaPointRect = pointRect;
    }

    public final void n(boolean z10) {
        this.isDefaultConfig = z10;
    }

    public final void o(int i10) {
        this.defaultVideoMode = i10;
    }

    public final void p(int i10) {
        this.fps = i10;
    }

    public final void q(int i10) {
        this.height = i10;
    }

    public final void r(@Nullable JSONObject jSONObject) {
        this.jsonConfig = jSONObject;
    }

    public final void s(@NotNull PointRect pointRect) {
        Intrinsics.checkParameterIsNotNull(pointRect, "<set-?>");
        this.rgbPointRect = pointRect;
    }

    public final void t(int i10) {
        this.videoHeight = i10;
    }

    @NotNull
    public String toString() {
        return "AnimConfig(version=" + this.version + ", totalFrames=" + this.totalFrames + ", width=" + this.width + ", height=" + this.height + ", videoWidth=" + this.videoWidth + ", videoHeight=" + this.videoHeight + ", orien=" + this.orien + ", fps=" + this.fps + ", isMix=" + this.isMix + ", alphaPointRect=" + this.alphaPointRect + ", rgbPointRect=" + this.rgbPointRect + ", isDefaultConfig=" + this.isDefaultConfig + ')';
    }

    public final void u(int i10) {
        this.videoWidth = i10;
    }

    public final void v(int i10) {
        this.width = i10;
    }
}
