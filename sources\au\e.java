package au;

import androidx.exifinterface.media.ExifInterface;
import com.google.android.gms.common.internal.ImagesContract;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.wear.bean.ProgramPattern;
import cu.HttpMethod;
import cu.e1;
import cu.p;
import java.util.Map;
import java.util.Set;
import kotlin.Metadata;
import kotlin.collections.SetsKt;
import kotlin.jvm.internal.Intrinsics;
import kotlinx.coroutines.d2;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jivesoftware.smackx.shim.packet.HeadersExtension;
import org.jivesoftware.smackx.xhtmlim.XHTMLText;

/* compiled from: HttpRequest.kt */
@Metadata(d1 = {"\u0000F\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0014\n\u0002\u0010\"\n\u0002\b\u0005\u0018\u00002\u00020\u0001B9\b\u0007\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\t\u001a\u00020\b\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\r\u001a\u00020\f¢\u0006\u0004\b\u000e\u0010\u000fJ#\u0010\u0013\u001a\u0004\u0018\u00018\u0000\"\u0004\b\u0000\u0010\u00102\f\u0010\u0012\u001a\b\u0012\u0004\u0012\u00028\u00000\u0011¢\u0006\u0004\b\u0013\u0010\u0014J\u000f\u0010\u0016\u001a\u00020\u0015H\u0016¢\u0006\u0004\b\u0016\u0010\u0017R\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0018\u0010\u0019\u001a\u0004\b\u001a\u0010\u001bR\u0017\u0010\u0005\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b\u001c\u0010\u001d\u001a\u0004\b\u001e\u0010\u001fR\u0017\u0010\u0007\u001a\u00020\u00068\u0006¢\u0006\f\n\u0004\b\u0013\u0010 \u001a\u0004\b!\u0010\"R\u0017\u0010\t\u001a\u00020\b8\u0006¢\u0006\f\n\u0004\b#\u0010$\u001a\u0004\b\u001c\u0010%R\u0017\u0010\u000b\u001a\u00020\n8\u0006¢\u0006\f\n\u0004\b!\u0010&\u001a\u0004\b#\u0010'R\u0017\u0010\r\u001a\u00020\f8\u0006¢\u0006\f\n\u0004\b\u001e\u0010(\u001a\u0004\b\u0018\u0010)R$\u0010.\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00110*8\u0000X\u0080\u0004¢\u0006\f\n\u0004\b+\u0010,\u001a\u0004\b+\u0010-¨\u0006/"}, d2 = {"Lau/e;", "", "Lcu/e1;", ImagesContract.URL, "Lcu/c0;", FirebaseAnalytics.Param.METHOD, "Lcu/p;", HeadersExtension.ELEMENT, "Ldu/d;", "body", "Lkotlinx/coroutines/d2;", "executionContext", "Leu/b;", "attributes", "<init>", "(Lcu/e1;Lcu/c0;Lcu/p;Ldu/d;Lkotlinx/coroutines/d2;Leu/b;)V", ExifInterface.GPS_DIRECTION_TRUE, "Lio/ktor/client/engine/g;", "key", "c", "(Lio/ktor/client/engine/g;)Ljava/lang/Object;", "", "toString", "()Ljava/lang/String;", "a", "Lcu/e1;", XHTMLText.H, "()Lcu/e1;", r0.b.f37717b, "Lcu/c0;", ProgramPattern.writePatternChar100, "()Lcu/c0;", "Lcu/p;", "e", "()Lcu/p;", "d", "Ldu/d;", "()Ldu/d;", "Lkotlinx/coroutines/d2;", "()Lkotlinx/coroutines/d2;", "Leu/b;", "()Leu/b;", "", "g", "Ljava/util/Set;", "()Ljava/util/Set;", "requiredCapabilities", "ktor-client-core"}, k = 1, mv = {2, 0, 0}, xi = 48)
/* renamed from: au.e, reason: from toString */
/* loaded from: classes5.dex */
public final class HttpRequestData {

    /* renamed from: a, reason: from kotlin metadata and from toString */
    @NotNull
    public final e1 url;

    /* renamed from: b, reason: from toString */
    @NotNull
    public final HttpMethod method;

    /* renamed from: c, reason: from kotlin metadata */
    @NotNull
    public final p org.jivesoftware.smackx.shim.packet.HeadersExtension.ELEMENT java.lang.String;

    /* renamed from: d, reason: from kotlin metadata */
    @NotNull
    public final du.d body;

    /* renamed from: e, reason: from kotlin metadata */
    @NotNull
    public final d2 executionContext;

    /* renamed from: f */
    @NotNull
    public final eu.b attributes;

    /* renamed from: g, reason: from kotlin metadata */
    @NotNull
    public final Set<io.ktor.client.engine.g<?>> requiredCapabilities;

    public HttpRequestData(@NotNull e1 url, @NotNull HttpMethod method, @NotNull p headers, @NotNull du.d body, @NotNull d2 executionContext, @NotNull eu.b attributes) {
        Set<io.ktor.client.engine.g<?>> keySet;
        Intrinsics.checkNotNullParameter(url, "url");
        Intrinsics.checkNotNullParameter(method, "method");
        Intrinsics.checkNotNullParameter(headers, "headers");
        Intrinsics.checkNotNullParameter(body, "body");
        Intrinsics.checkNotNullParameter(executionContext, "executionContext");
        Intrinsics.checkNotNullParameter(attributes, "attributes");
        this.url = url;
        this.method = method;
        this.org.jivesoftware.smackx.shim.packet.HeadersExtension.ELEMENT java.lang.String = headers;
        this.body = body;
        this.executionContext = executionContext;
        this.attributes = attributes;
        Map map = (Map) attributes.d(io.ktor.client.engine.h.a());
        this.requiredCapabilities = (map == null || (keySet = map.keySet()) == null) ? SetsKt.emptySet() : keySet;
    }

    @NotNull
    /* renamed from: a, reason: from getter */
    public final eu.b getAttributes() {
        return this.attributes;
    }

    @NotNull
    /* renamed from: b, reason: from getter */
    public final du.d getBody() {
        return this.body;
    }

    @Nullable
    public final <T> T c(@NotNull io.ktor.client.engine.g<T> key) {
        Intrinsics.checkNotNullParameter(key, "key");
        Map map = (Map) this.attributes.d(io.ktor.client.engine.h.a());
        if (map != null) {
            return (T) map.get(key);
        }
        return null;
    }

    @NotNull
    /* renamed from: d, reason: from getter */
    public final d2 getExecutionContext() {
        return this.executionContext;
    }

    @NotNull
    /* renamed from: e, reason: from getter */
    public final p getOrg.jivesoftware.smackx.shim.packet.HeadersExtension.ELEMENT java.lang.String() {
        return this.org.jivesoftware.smackx.shim.packet.HeadersExtension.ELEMENT java.lang.String;
    }

    @NotNull
    /* renamed from: f, reason: from getter */
    public final HttpMethod getMethod() {
        return this.method;
    }

    @NotNull
    public final Set<io.ktor.client.engine.g<?>> g() {
        return this.requiredCapabilities;
    }

    @NotNull
    /* renamed from: h, reason: from getter */
    public final e1 getUrl() {
        return this.url;
    }

    @NotNull
    public String toString() {
        return "HttpRequestData(url=" + this.url + ", method=" + this.method + ')';
    }
}
