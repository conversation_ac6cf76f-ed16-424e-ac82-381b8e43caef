{"v": "5.6.4", "fr": 30, "ip": 0, "op": 65, "w": 200, "h": 200, "nm": "tips_Mission2_animation", "ddd": 0, "assets": [{"id": "image_0", "w": 480, "h": 480, "u": "images/", "p": "mission2_image.png", "e": 0}, {"id": "comp_0", "layers": [{"ddd": 0, "ind": 2, "ty": 4, "nm": "Mission 2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 12, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 32, "s": [60]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 33, "s": [60]}, {"t": 39, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 12, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 24, "s": [-36]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 32, "s": [-36]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 33, "s": [-44]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 34, "s": [-28]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 35, "s": [-44]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 36, "s": [-28]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 37, "s": [-44]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 38, "s": [-28]}, {"t": 39, "s": [-36]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [100, 100.262, 0], "to": [3.547, 2.626, 0], "ti": [-3.547, -2.626, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [121.28, 116.015, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [121.28, 116.015, 0], "to": [22.333, 0, 0], "ti": [-11.12, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [255.28, 116.015, 0], "to": [11.12, 0, 0], "ti": [11.213, 0, 0]}, {"t": 32, "s": [188, 116.015, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 8, "s": [72, 72, 100]}, {"t": 33, "s": [72, 72, 100]}], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-96.257, -31.23], [-3.974, 35.2], [30.734, -13.759], [-60.265, -80.878]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "蒙版 1"}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.05, -0.065], [-0.142, -0.148], [0, 0], [-0.062, -0.081], [-0.182, -0.187], [-0.084, -0.111], [-0.405, -0.41], [-0.279, -0.266], [-0.358, -0.353], [-0.122, -0.099], [-0.161, -0.156], [-0.078, -0.064], [0, 0], [-0.045, -0.046], [-0.109, -0.104], [-0.063, -0.054], [-0.082, -0.077], [-0.063, -0.054], [0, 0], [-0.047, -0.045], [-0.174, -0.156], [-0.047, -0.045], [-0.455, -0.391], [-1.373, -1.059], [-2.163, -1.499], [-1.746, -1.105], [-0.963, -0.599], [-0.938, -0.577], [-0.046, -0.042], [-0.119, -0.063], [0.074, -0.073], [-0.078, -0.05], [-0.2, -0.119], [-0.063, 0.107], [-0.081, -0.052], [-0.252, -0.158], [0.059, -0.066], [-0.093, -0.058], [-0.213, -0.134], [-0.037, 0.067], [-0.123, -0.109], [-0.128, -0.076], [-0.078, 0.032], [-0.052, -0.03], [-0.806, -0.453], [-0.616, -0.328], [-0.21, -0.028], [-0.129, 0.058], [-0.099, 0.161], [-0.223, 0.343], [-0.123, -0.08], [-0.474, -0.315], [-0.068, 0.106], [-0.104, -0.068], [-0.547, -0.357], [-0.952, -0.622], [-0.136, -0.245], [-0.388, -0.305], [-0.618, -0.106], [-0.075, -0.046], [0, 0], [-0.069, -0.005], [-0.16, -0.039], [-0.058, -0.016], [-0.063, 0], [-0.406, -0.005], [-0.221, -0.039], [-0.058, -0.016], [-0.063, 0], [-0.432, -0.005], [-0.207, -0.039], [-0.058, -0.015], [-0.063, -0.001], [-0.008, -0.315], [-0.045, -0.388], [-0.123, 0.003], [-0.135, -0.047], [0, 0], [0, 0], [-0.136, -0.049], [-0.061, 0.05], [0, 0.075], [0, 2.549], [0.002, 0.052], [-0.073, -0.003], [-0.228, -0.038], [-0.129, 0.015], [0.004, -0.123], [0.001, -0.288], [0.003, -0.699], [-0.051, -0.188], [-0.002, -0.021], [-0.045, -0.093], [-0.002, -0.021], [-0.048, -0.08], [0, 0], [0, 0], [-0.046, -0.065], [-0.011, -0.083], [-0.147, -0.407], [-0.241, -0.41], [-0.424, -0.432], [-0.235, -0.037], [-0.095, -0.043], [-0.065, 0], [-1.017, -0.001], [-0.052, 0.148], [0, 0.06], [0.002, 8.295], [0.065, 0.051], [0.116, 0], [1.011, 0.027], [0.346, -0.433], [0.265, -1.511], [-0.002, -0.899], [0.002, -0.552], [0.113, -0.002], [0.381, -0.009], [-0.002, 0.096], [0, 0.328], [-0.006, 2.272], [0.197, -0.028], [0.255, -0.022], [0, -0.33], [0.001, -2.252], [0.235, -0.006], [0.263, -0.006], [0, -0.752], [-0.004, -1.668], [0.142, -0.002], [0.899, -0.018], [0.064, 0.041], [1.653, 1.057], [-0.073, 0.082], [0.14, 0.087], [0.454, 0.289], [-0.125, 0.196], [-0.209, 0.326], [0.398, 0.327], [2.043, 1.411], [0.051, 0.111], [0.303, 0.22], [0.582, 0.046], [0.089, 0.056], [0.59, 0.369], [0.076, 0.116], [0.098, 0.09], [0.223, 0.088], [0.094, -0.007], [0.185, 0.11], [1.502, 0.849], [1.045, 0.603], [0.946, 0.579], [0.289, 0.34], [0.076, 0.064], [0, 0], [0.08, 0.042], [0.168, 0.06], [0.434, 0.142], [0.19, 0.561], [0.212, 0.134], [0.3, 0.101], [0.342, 0.258], [0.058, 0.037], [0.105, 0.225], [0.214, 0.192], [0.345, 0.147], [0.316, 0.233], [0.056, 0.039], [0.1, 0.234], [0.144, 0.218], [0.501, 0.227], [0.233, 0.465], [0.055, 0.176], [0.293, 0.199], [0.193, 0.207], [0.168, 0.155], [0.328, 1.276], [0.346, 0.591], [0.343, 0.439], [0.06, 0.039], [0.071, 0.108], [0.084, 0.067], [0.14, 0.14], [0.121, 0.08], [0.465, 0.335], [1.366, 0.501], [0.39, 0.113], [0.063, 0.041], [0.009, 0.012], [0.08, 0.039], [0.021, 0.002], [0.162, 0.048], [0.038, 0.023], [0.189, -0.05], [0.021, -0.002], [0.08, -0.044], [0.057, -0.01], [0.517, -0.476], [0.083, -0.095], [0.21, -0.618], [0.026, -0.324], [0.038, -0.065], [0, 0], [0, 0], [0.042, -0.094], [0.002, -0.034], [-0.034, -0.328], [-0.001, -0.019], [-0.052, -0.107], [0, 0], [0, 0], [-0.049, -0.079], [0, 0], [0, 0], [-0.045, -0.066], [-0.15, -0.465], [-0.496, -0.657], [-0.734, -1.009], [-0.878, -0.996], [-0.04, -0.05], [-0.156, -0.174], [-0.04, -0.05], [-0.064, -0.069], [-0.042, -0.049], [-0.34, -0.371], [-0.043, -0.048], [0, 0], [-0.041, -0.049], [-0.13, -0.135]], "o": [[0.142, 0.148], [0, 0], [0.067, 0.076], [0.182, 0.187], [0.089, 0.107], [0.405, 0.41], [0.265, 0.279], [0.358, 0.353], [0.127, 0.094], [0.161, 0.156], [0.083, 0.059], [0, 0], [0.049, 0.041], [0.109, 0.104], [0.068, 0.048], [0.082, 0.077], [0.067, 0.049], [0, 0], [0.05, 0.041], [0.174, 0.156], [0.05, 0.04], [0.455, 0.392], [1.316, 1.13], [2.084, 1.607], [1.698, 1.177], [0.959, 0.606], [0.934, 0.581], [0.054, 0.032], [0.112, 0.077], [0.095, 0.051], [-0.098, 0.096], [0.196, 0.127], [0.066, 0.039], [0.061, -0.103], [0.25, 0.16], [0.069, 0.043], [-0.094, 0.107], [0.213, 0.134], [0.066, 0.032], [0.032, -0.093], [0.11, 0.097], [0.079, 0.014], [0.071, -0.048], [0.804, 0.458], [0.608, 0.342], [0.177, 0.094], [0.131, 0.024], [0.17, -0.073], [0.215, -0.348], [0.062, -0.095], [0.477, 0.31], [0.112, 0.074], [0.075, -0.116], [0.547, 0.358], [0.953, 0.622], [0.211, 0.138], [0.24, 0.433], [0.493, 0.386], [0.074, 0.054], [0, 0], [0.065, 0.045], [0.161, 0.012], [0.058, -0.028], [0.06, 0.041], [0.406, 0.002], [0.222, 0.003], [0.058, -0.028], [0.06, 0.041], [0.432, 0.002], [0.209, 0.002], [0.058, -0.027], [0.06, 0.04], [0.304, 0.007], [0.009, 0.388], [0.03, 0.162], [0.134, 0.054], [0, 0], [0, 0], [0.135, 0.058], [0.067, -0.02], [0.056, -0.07], [0.002, -2.549], [0, -0.052], [-0.003, -0.076], [0.228, 0.01], [0.129, -0.037], [0.119, -0.013], [-0.009, 0.288], [-0.002, 0.699], [0.049, 0.188], [0.002, 0.021], [0.041, 0.093], [0.002, 0.021], [0.039, 0.081], [0, 0], [0, 0], [0.037, 0.067], [0.005, 0.088], [0.056, 0.435], [0.16, 0.444], [0.306, 0.521], [0.159, 0.162], [0.095, 0.048], [0.065, -0.004], [1.017, -0.001], [0.127, 0], [0.07, -0.048], [0.002, -8.295], [0, -0.061], [-0.061, -0.114], [-1.011, 0.002], [-0.575, -0.015], [-0.957, 1.199], [-0.157, 0.892], [0.001, 0.552], [0, 0.057], [-0.381, 0.007], [-0.107, 0.003], [0.005, -0.328], [0.001, -2.272], [0, -0.19], [-0.253, 0.036], [-0.332, 0.029], [0, 2.252], [0, 0.235], [-0.263, 0.007], [-0.751, 0.015], [-0.001, 1.668], [0, 0.131], [-0.899, 0.01], [-0.075, 0.002], [-1.651, -1.06], [-0.084, -0.054], [0.11, -0.123], [-0.457, -0.284], [-0.183, -0.116], [0.209, -0.326], [0.276, -0.429], [-1.919, -1.576], [-0.097, -0.067], [-0.162, -0.354], [-0.453, -0.328], [-0.11, -0.009], [-0.59, -0.369], [-0.111, -0.069], [-0.069, -0.104], [-0.175, -0.162], [-0.085, -0.033], [-0.201, 0.015], [-1.482, -0.883], [-1.05, -0.593], [-0.96, -0.554], [-0.369, -0.226], [-0.065, -0.075], [0, 0], [-0.056, -0.069], [-0.161, -0.078], [-0.43, -0.154], [-0.547, -0.18], [-0.081, -0.239], [-0.272, -0.172], [-0.396, -0.133], [-0.039, -0.06], [-0.214, -0.142], [-0.114, -0.244], [-0.289, -0.259], [-0.355, -0.151], [-0.039, -0.059], [-0.223, -0.146], [-0.101, -0.236], [-0.322, -0.487], [-0.439, -0.199], [-0.084, -0.168], [-0.105, -0.338], [-0.229, -0.157], [-0.164, -0.159], [-0.914, -0.919], [-0.169, -0.657], [-0.284, -0.484], [-0.04, -0.055], [-0.096, -0.084], [-0.06, -0.089], [-0.14, -0.14], [-0.098, -0.104], [-0.442, -0.363], [-1.179, -0.847], [-0.382, -0.14], [-0.063, -0.043], [-0.011, 0.009], [-0.08, -0.043], [-0.021, -0.002], [-0.162, -0.05], [-0.038, 0.017], [-0.189, 0.049], [-0.021, 0.002], [-0.081, 0.038], [-0.056, 0.015], [-0.692, 0.125], [-0.095, 0.082], [-0.445, 0.477], [-0.103, 0.304], [-0.04, 0.065], [0, 0], [0, 0], [-0.049, 0.093], [-0.002, 0.034], [-0.028, 0.328], [0.001, 0.019], [0.045, 0.108], [0, 0], [0, 0], [0.04, 0.08], [0, 0], [0, 0], [0.036, 0.067], [0.076, 0.484], [0.247, 0.764], [0.75, 0.994], [0.782, 1.074], [0.045, 0.046], [0.156, 0.174], [0.045, 0.046], [0.064, 0.069], [0.045, 0.046], [0.34, 0.371], [0.047, 0.045], [0, 0], [0.045, 0.045], [0.13, 0.135], [0.056, 0.061]], "v": [[-28.63, -8.471], [-28.202, -8.026], [-28.203, -8.027], [-28, -7.802], [-27.454, -7.241], [-27.172, -6.935], [-25.957, -5.704], [-25.14, -4.887], [-24.065, -3.828], [-23.72, -3.508], [-23.237, -3.039], [-23.01, -2.838], [-22.646, -2.488], [-22.5, -2.365], [-22.173, -2.053], [-21.985, -1.892], [-21.74, -1.66], [-21.552, -1.498], [-21.306, -1.266], [-21.157, -1.143], [-20.636, -0.675], [-20.487, -0.552], [-19.123, 0.624], [-15.081, 3.897], [-8.709, 8.554], [-3.547, 11.982], [-0.667, 13.795], [2.145, 15.525], [2.291, 15.642], [2.629, 15.87], [2.678, 16.05], [2.717, 16.248], [3.307, 16.623], [3.524, 16.611], [3.736, 16.584], [4.49, 17.061], [4.517, 17.215], [4.581, 17.448], [5.22, 17.851], [5.376, 17.805], [5.551, 17.744], [5.93, 17.975], [6.166, 17.967], [6.339, 18.006], [8.749, 19.379], [10.586, 20.385], [11.132, 20.646], [11.522, 20.635], [11.92, 20.287], [12.586, 19.256], [12.837, 19.177], [14.271, 20.105], [14.527, 20.067], [14.788, 20.026], [16.439, 21.084], [19.305, 22.938], [19.897, 23.419], [20.858, 24.514], [22.522, 25.254], [22.764, 25.294], [22.76, 25.293], [22.968, 25.315], [23.452, 25.334], [23.626, 25.333], [23.816, 25.354], [25.033, 25.356], [25.698, 25.373], [25.872, 25.372], [26.063, 25.393], [27.358, 25.396], [27.984, 25.412], [28.158, 25.412], [28.348, 25.433], [28.613, 25.707], [28.632, 26.873], [28.907, 27.028], [29.321, 27.066], [29.331, 27.059], [29.341, 27.066], [29.758, 27.106], [29.963, 27.066], [29.99, 26.839], [29.991, 19.192], [29.991, 19.035], [30.098, 18.932], [30.783, 18.95], [31.173, 18.932], [31.328, 19.082], [31.324, 19.946], [31.315, 22.044], [31.352, 22.617], [31.356, 22.681], [31.39, 22.971], [31.396, 23.033], [31.432, 23.287], [31.442, 23.297], [31.435, 23.31], [31.472, 23.523], [31.52, 23.778], [31.876, 25.027], [32.479, 26.307], [33.533, 27.763], [34.111, 28.093], [34.409, 28.131], [34.604, 28.119], [37.654, 28.119], [37.957, 27.97], [37.991, 27.791], [37.991, 2.906], [37.958, 2.723], [37.679, 2.597], [34.645, 2.591], [33.303, 3.23], [31.468, 7.292], [31.325, 9.983], [31.324, 11.637], [31.246, 11.781], [30.105, 11.804], [29.979, 11.656], [29.981, 10.671], [29.986, 3.855], [29.741, 3.622], [28.976, 3.695], [28.639, 4.059], [28.637, 10.816], [28.378, 11.081], [27.591, 11.099], [26.459, 12.254], [26.461, 17.257], [26.292, 17.439], [23.593, 17.486], [23.39, 17.432], [18.433, 14.256], [18.392, 14.075], [18.332, 13.78], [16.971, 12.912], [16.901, 12.544], [17.529, 11.567], [17.358, 10.504], [11.409, 6.031], [11.152, 5.794], [10.4, 4.977], [8.895, 4.299], [8.61, 4.173], [6.842, 3.065], [6.573, 2.795], [6.371, 2.473], [5.782, 2.086], [5.519, 2.017], [4.938, 1.92], [0.454, -0.665], [-2.699, -2.441], [-5.557, -4.144], [-6.611, -4.907], [-6.821, -5.116], [-7.123, -5.419], [-7.321, -5.592], [-7.806, -5.826], [-9.106, -6.262], [-10.254, -7.329], [-10.696, -7.876], [-11.573, -8.233], [-12.719, -8.731], [-12.876, -8.863], [-13.312, -9.445], [-13.724, -10.138], [-14.711, -10.673], [-15.754, -11.173], [-15.909, -11.307], [-16.343, -11.912], [-16.649, -12.62], [-17.983, -13.55], [-19.088, -14.414], [-19.293, -14.933], [-19.89, -15.721], [-20.562, -16.219], [-21.049, -16.7], [-23.004, -19.934], [-23.747, -21.817], [-24.735, -23.166], [-24.868, -23.32], [-25.129, -23.599], [-25.345, -23.832], [-25.765, -24.252], [-26.078, -24.546], [-27.413, -25.623], [-31.211, -27.679], [-32.38, -28.016], [-32.586, -28.051], [-32.616, -28.055], [-32.869, -28.089], [-32.932, -28.095], [-33.425, -28.13], [-33.54, -28.131], [-34.114, -28.095], [-34.175, -28.089], [-34.43, -28.055], [-34.598, -28.011], [-36.41, -27.108], [-36.676, -26.842], [-37.657, -25.199], [-37.899, -24.271], [-37.933, -24.063], [-37.926, -24.048], [-37.936, -24.035], [-37.973, -23.741], [-37.977, -23.64], [-37.976, -22.655], [-37.973, -22.597], [-37.935, -22.261], [-37.925, -22.251], [-37.932, -22.238], [-37.896, -21.985], [-37.886, -21.975], [-37.893, -21.963], [-37.857, -21.749], [-37.491, -20.332], [-36.443, -18.187], [-34.317, -15.111], [-31.828, -12.007], [-31.705, -11.859], [-31.237, -11.337], [-31.114, -11.189], [-30.922, -10.983], [-30.798, -10.835], [-29.779, -9.721], [-29.654, -9.574], [-29.306, -9.208], [-29.183, -9.062], [-28.793, -8.657]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [200, 200], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Mission 4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 12, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 43, "s": [30]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 52, "s": [30]}, {"t": 53, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 12, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 24, "s": [-36]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 32, "s": [-36]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [-36]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 42, "s": [-24]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 43, "s": [-24]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 44, "s": [-42]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 45, "s": [-6]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 46, "s": [-42]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 47, "s": [-6]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 48, "s": [-42]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 49, "s": [-6]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [-42]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 51, "s": [-6]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 52, "s": [-42]}, {"t": 53, "s": [-24]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [100, 100.262, 0], "to": [3.547, 2.626, 0], "ti": [-3.547, -2.626, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [121.28, 116.015, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [121.28, 116.015, 0], "to": [22.333, 0, 0], "ti": [-11.12, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [255.28, 116.015, 0], "to": [11.12, 0, 0], "ti": [11.213, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [188, 116.015, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [188, 116.015, 0], "to": [-2.667, 0, 0], "ti": [2.667, 0, 0]}, {"t": 42, "s": [172, 116.015, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"t": 8, "s": [72, 72, 100]}], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-88.785, -129.988], [-124.609, -8.71], [-11.624, 27.579], [6.12, -23.092]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "蒙版 1"}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.05, -0.065], [-0.142, -0.148], [0, 0], [-0.062, -0.081], [-0.182, -0.187], [-0.084, -0.111], [-0.405, -0.41], [-0.279, -0.266], [-0.358, -0.353], [-0.122, -0.099], [-0.161, -0.156], [-0.078, -0.064], [0, 0], [-0.045, -0.046], [-0.109, -0.104], [-0.063, -0.054], [-0.082, -0.077], [-0.063, -0.054], [0, 0], [-0.047, -0.045], [-0.174, -0.156], [-0.047, -0.045], [-0.455, -0.391], [-1.373, -1.059], [-2.163, -1.499], [-1.746, -1.105], [-0.963, -0.599], [-0.938, -0.577], [-0.046, -0.042], [-0.119, -0.063], [0.074, -0.073], [-0.078, -0.05], [-0.2, -0.119], [-0.063, 0.107], [-0.081, -0.052], [-0.252, -0.158], [0.059, -0.066], [-0.093, -0.058], [-0.213, -0.134], [-0.037, 0.067], [-0.123, -0.109], [-0.128, -0.076], [-0.078, 0.032], [-0.052, -0.03], [-0.806, -0.453], [-0.616, -0.328], [-0.21, -0.028], [-0.129, 0.058], [-0.099, 0.161], [-0.223, 0.343], [-0.123, -0.08], [-0.474, -0.315], [-0.068, 0.106], [-0.104, -0.068], [-0.547, -0.357], [-0.952, -0.622], [-0.136, -0.245], [-0.388, -0.305], [-0.618, -0.106], [-0.075, -0.046], [0, 0], [-0.069, -0.005], [-0.16, -0.039], [-0.058, -0.016], [-0.063, 0], [-0.406, -0.005], [-0.221, -0.039], [-0.058, -0.016], [-0.063, 0], [-0.432, -0.005], [-0.207, -0.039], [-0.058, -0.015], [-0.063, -0.001], [-0.008, -0.315], [-0.045, -0.388], [-0.123, 0.003], [-0.135, -0.047], [0, 0], [0, 0], [-0.136, -0.049], [-0.061, 0.05], [0, 0.075], [0, 2.549], [0.002, 0.052], [-0.073, -0.003], [-0.228, -0.038], [-0.129, 0.015], [0.004, -0.123], [0.001, -0.288], [0.003, -0.699], [-0.051, -0.188], [-0.002, -0.021], [-0.045, -0.093], [-0.002, -0.021], [-0.048, -0.08], [0, 0], [0, 0], [-0.046, -0.065], [-0.011, -0.083], [-0.147, -0.407], [-0.241, -0.41], [-0.424, -0.432], [-0.235, -0.037], [-0.095, -0.043], [-0.065, 0], [-1.017, -0.001], [-0.052, 0.148], [0, 0.06], [0.002, 8.295], [0.065, 0.051], [0.116, 0], [1.011, 0.027], [0.346, -0.433], [0.265, -1.511], [-0.002, -0.899], [0.002, -0.552], [0.113, -0.002], [0.381, -0.009], [-0.002, 0.096], [0, 0.328], [-0.006, 2.272], [0.197, -0.028], [0.255, -0.022], [0, -0.33], [0.001, -2.252], [0.235, -0.006], [0.263, -0.006], [0, -0.752], [-0.004, -1.668], [0.142, -0.002], [0.899, -0.018], [0.064, 0.041], [1.653, 1.057], [-0.073, 0.082], [0.14, 0.087], [0.454, 0.289], [-0.125, 0.196], [-0.209, 0.326], [0.398, 0.327], [2.043, 1.411], [0.051, 0.111], [0.303, 0.22], [0.582, 0.046], [0.089, 0.056], [0.59, 0.369], [0.076, 0.116], [0.098, 0.09], [0.223, 0.088], [0.094, -0.007], [0.185, 0.11], [1.502, 0.849], [1.045, 0.603], [0.946, 0.579], [0.289, 0.34], [0.076, 0.064], [0, 0], [0.08, 0.042], [0.168, 0.06], [0.434, 0.142], [0.19, 0.561], [0.212, 0.134], [0.3, 0.101], [0.342, 0.258], [0.058, 0.037], [0.105, 0.225], [0.214, 0.192], [0.345, 0.147], [0.316, 0.233], [0.056, 0.039], [0.1, 0.234], [0.144, 0.218], [0.501, 0.227], [0.233, 0.465], [0.055, 0.176], [0.293, 0.199], [0.193, 0.207], [0.168, 0.155], [0.328, 1.276], [0.346, 0.591], [0.343, 0.439], [0.06, 0.039], [0.071, 0.108], [0.084, 0.067], [0.14, 0.14], [0.121, 0.08], [0.465, 0.335], [1.366, 0.501], [0.39, 0.113], [0.063, 0.041], [0.009, 0.012], [0.08, 0.039], [0.021, 0.002], [0.162, 0.048], [0.038, 0.023], [0.189, -0.05], [0.021, -0.002], [0.08, -0.044], [0.057, -0.01], [0.517, -0.476], [0.083, -0.095], [0.21, -0.618], [0.026, -0.324], [0.038, -0.065], [0, 0], [0, 0], [0.042, -0.094], [0.002, -0.034], [-0.034, -0.328], [-0.001, -0.019], [-0.052, -0.107], [0, 0], [0, 0], [-0.049, -0.079], [0, 0], [0, 0], [-0.045, -0.066], [-0.15, -0.465], [-0.496, -0.657], [-0.734, -1.009], [-0.878, -0.996], [-0.04, -0.05], [-0.156, -0.174], [-0.04, -0.05], [-0.064, -0.069], [-0.042, -0.049], [-0.34, -0.371], [-0.043, -0.048], [0, 0], [-0.041, -0.049], [-0.13, -0.135]], "o": [[0.142, 0.148], [0, 0], [0.067, 0.076], [0.182, 0.187], [0.089, 0.107], [0.405, 0.41], [0.265, 0.279], [0.358, 0.353], [0.127, 0.094], [0.161, 0.156], [0.083, 0.059], [0, 0], [0.049, 0.041], [0.109, 0.104], [0.068, 0.048], [0.082, 0.077], [0.067, 0.049], [0, 0], [0.05, 0.041], [0.174, 0.156], [0.05, 0.04], [0.455, 0.392], [1.316, 1.13], [2.084, 1.607], [1.698, 1.177], [0.959, 0.606], [0.934, 0.581], [0.054, 0.032], [0.112, 0.077], [0.095, 0.051], [-0.098, 0.096], [0.196, 0.127], [0.066, 0.039], [0.061, -0.103], [0.25, 0.16], [0.069, 0.043], [-0.094, 0.107], [0.213, 0.134], [0.066, 0.032], [0.032, -0.093], [0.11, 0.097], [0.079, 0.014], [0.071, -0.048], [0.804, 0.458], [0.608, 0.342], [0.177, 0.094], [0.131, 0.024], [0.17, -0.073], [0.215, -0.348], [0.062, -0.095], [0.477, 0.31], [0.112, 0.074], [0.075, -0.116], [0.547, 0.358], [0.953, 0.622], [0.211, 0.138], [0.24, 0.433], [0.493, 0.386], [0.074, 0.054], [0, 0], [0.065, 0.045], [0.161, 0.012], [0.058, -0.028], [0.06, 0.041], [0.406, 0.002], [0.222, 0.003], [0.058, -0.028], [0.06, 0.041], [0.432, 0.002], [0.209, 0.002], [0.058, -0.027], [0.06, 0.04], [0.304, 0.007], [0.009, 0.388], [0.03, 0.162], [0.134, 0.054], [0, 0], [0, 0], [0.135, 0.058], [0.067, -0.02], [0.056, -0.07], [0.002, -2.549], [0, -0.052], [-0.003, -0.076], [0.228, 0.01], [0.129, -0.037], [0.119, -0.013], [-0.009, 0.288], [-0.002, 0.699], [0.049, 0.188], [0.002, 0.021], [0.041, 0.093], [0.002, 0.021], [0.039, 0.081], [0, 0], [0, 0], [0.037, 0.067], [0.005, 0.088], [0.056, 0.435], [0.16, 0.444], [0.306, 0.521], [0.159, 0.162], [0.095, 0.048], [0.065, -0.004], [1.017, -0.001], [0.127, 0], [0.07, -0.048], [0.002, -8.295], [0, -0.061], [-0.061, -0.114], [-1.011, 0.002], [-0.575, -0.015], [-0.957, 1.199], [-0.157, 0.892], [0.001, 0.552], [0, 0.057], [-0.381, 0.007], [-0.107, 0.003], [0.005, -0.328], [0.001, -2.272], [0, -0.19], [-0.253, 0.036], [-0.332, 0.029], [0, 2.252], [0, 0.235], [-0.263, 0.007], [-0.751, 0.015], [-0.001, 1.668], [0, 0.131], [-0.899, 0.01], [-0.075, 0.002], [-1.651, -1.06], [-0.084, -0.054], [0.11, -0.123], [-0.457, -0.284], [-0.183, -0.116], [0.209, -0.326], [0.276, -0.429], [-1.919, -1.576], [-0.097, -0.067], [-0.162, -0.354], [-0.453, -0.328], [-0.11, -0.009], [-0.59, -0.369], [-0.111, -0.069], [-0.069, -0.104], [-0.175, -0.162], [-0.085, -0.033], [-0.201, 0.015], [-1.482, -0.883], [-1.05, -0.593], [-0.96, -0.554], [-0.369, -0.226], [-0.065, -0.075], [0, 0], [-0.056, -0.069], [-0.161, -0.078], [-0.43, -0.154], [-0.547, -0.18], [-0.081, -0.239], [-0.272, -0.172], [-0.396, -0.133], [-0.039, -0.06], [-0.214, -0.142], [-0.114, -0.244], [-0.289, -0.259], [-0.355, -0.151], [-0.039, -0.059], [-0.223, -0.146], [-0.101, -0.236], [-0.322, -0.487], [-0.439, -0.199], [-0.084, -0.168], [-0.105, -0.338], [-0.229, -0.157], [-0.164, -0.159], [-0.914, -0.919], [-0.169, -0.657], [-0.284, -0.484], [-0.04, -0.055], [-0.096, -0.084], [-0.06, -0.089], [-0.14, -0.14], [-0.098, -0.104], [-0.442, -0.363], [-1.179, -0.847], [-0.382, -0.14], [-0.063, -0.043], [-0.011, 0.009], [-0.08, -0.043], [-0.021, -0.002], [-0.162, -0.05], [-0.038, 0.017], [-0.189, 0.049], [-0.021, 0.002], [-0.081, 0.038], [-0.056, 0.015], [-0.692, 0.125], [-0.095, 0.082], [-0.445, 0.477], [-0.103, 0.304], [-0.04, 0.065], [0, 0], [0, 0], [-0.049, 0.093], [-0.002, 0.034], [-0.028, 0.328], [0.001, 0.019], [0.045, 0.108], [0, 0], [0, 0], [0.04, 0.08], [0, 0], [0, 0], [0.036, 0.067], [0.076, 0.484], [0.247, 0.764], [0.75, 0.994], [0.782, 1.074], [0.045, 0.046], [0.156, 0.174], [0.045, 0.046], [0.064, 0.069], [0.045, 0.046], [0.34, 0.371], [0.047, 0.045], [0, 0], [0.045, 0.045], [0.13, 0.135], [0.056, 0.061]], "v": [[-28.63, -8.471], [-28.202, -8.026], [-28.203, -8.027], [-28, -7.802], [-27.454, -7.241], [-27.172, -6.935], [-25.957, -5.704], [-25.14, -4.887], [-24.065, -3.828], [-23.72, -3.508], [-23.237, -3.039], [-23.01, -2.838], [-22.646, -2.488], [-22.5, -2.365], [-22.173, -2.053], [-21.985, -1.892], [-21.74, -1.66], [-21.552, -1.498], [-21.306, -1.266], [-21.157, -1.143], [-20.636, -0.675], [-20.487, -0.552], [-19.123, 0.624], [-15.081, 3.897], [-8.709, 8.554], [-3.547, 11.982], [-0.667, 13.795], [2.145, 15.525], [2.291, 15.642], [2.629, 15.87], [2.678, 16.05], [2.717, 16.248], [3.307, 16.623], [3.524, 16.611], [3.736, 16.584], [4.49, 17.061], [4.517, 17.215], [4.581, 17.448], [5.22, 17.851], [5.376, 17.805], [5.551, 17.744], [5.93, 17.975], [6.166, 17.967], [6.339, 18.006], [8.749, 19.379], [10.586, 20.385], [11.132, 20.646], [11.522, 20.635], [11.92, 20.287], [12.586, 19.256], [12.837, 19.177], [14.271, 20.105], [14.527, 20.067], [14.788, 20.026], [16.439, 21.084], [19.305, 22.938], [19.897, 23.419], [20.858, 24.514], [22.522, 25.254], [22.764, 25.294], [22.76, 25.293], [22.968, 25.315], [23.452, 25.334], [23.626, 25.333], [23.816, 25.354], [25.033, 25.356], [25.698, 25.373], [25.872, 25.372], [26.063, 25.393], [27.358, 25.396], [27.984, 25.412], [28.158, 25.412], [28.348, 25.433], [28.613, 25.707], [28.632, 26.873], [28.907, 27.028], [29.321, 27.066], [29.331, 27.059], [29.341, 27.066], [29.758, 27.106], [29.963, 27.066], [29.99, 26.839], [29.991, 19.192], [29.991, 19.035], [30.098, 18.932], [30.783, 18.95], [31.173, 18.932], [31.328, 19.082], [31.324, 19.946], [31.315, 22.044], [31.352, 22.617], [31.356, 22.681], [31.39, 22.971], [31.396, 23.033], [31.432, 23.287], [31.442, 23.297], [31.435, 23.31], [31.472, 23.523], [31.52, 23.778], [31.876, 25.027], [32.479, 26.307], [33.533, 27.763], [34.111, 28.093], [34.409, 28.131], [34.604, 28.119], [37.654, 28.119], [37.957, 27.97], [37.991, 27.791], [37.991, 2.906], [37.958, 2.723], [37.679, 2.597], [34.645, 2.591], [33.303, 3.23], [31.468, 7.292], [31.325, 9.983], [31.324, 11.637], [31.246, 11.781], [30.105, 11.804], [29.979, 11.656], [29.981, 10.671], [29.986, 3.855], [29.741, 3.622], [28.976, 3.695], [28.639, 4.059], [28.637, 10.816], [28.378, 11.081], [27.591, 11.099], [26.459, 12.254], [26.461, 17.257], [26.292, 17.439], [23.593, 17.486], [23.39, 17.432], [18.433, 14.256], [18.392, 14.075], [18.332, 13.78], [16.971, 12.912], [16.901, 12.544], [17.529, 11.567], [17.358, 10.504], [11.409, 6.031], [11.152, 5.794], [10.4, 4.977], [8.895, 4.299], [8.61, 4.173], [6.842, 3.065], [6.573, 2.795], [6.371, 2.473], [5.782, 2.086], [5.519, 2.017], [4.938, 1.92], [0.454, -0.665], [-2.699, -2.441], [-5.557, -4.144], [-6.611, -4.907], [-6.821, -5.116], [-7.123, -5.419], [-7.321, -5.592], [-7.806, -5.826], [-9.106, -6.262], [-10.254, -7.329], [-10.696, -7.876], [-11.573, -8.233], [-12.719, -8.731], [-12.876, -8.863], [-13.312, -9.445], [-13.724, -10.138], [-14.711, -10.673], [-15.754, -11.173], [-15.909, -11.307], [-16.343, -11.912], [-16.649, -12.62], [-17.983, -13.55], [-19.088, -14.414], [-19.293, -14.933], [-19.89, -15.721], [-20.562, -16.219], [-21.049, -16.7], [-23.004, -19.934], [-23.747, -21.817], [-24.735, -23.166], [-24.868, -23.32], [-25.129, -23.599], [-25.345, -23.832], [-25.765, -24.252], [-26.078, -24.546], [-27.413, -25.623], [-31.211, -27.679], [-32.38, -28.016], [-32.586, -28.051], [-32.616, -28.055], [-32.869, -28.089], [-32.932, -28.095], [-33.425, -28.13], [-33.54, -28.131], [-34.114, -28.095], [-34.175, -28.089], [-34.43, -28.055], [-34.598, -28.011], [-36.41, -27.108], [-36.676, -26.842], [-37.657, -25.199], [-37.899, -24.271], [-37.933, -24.063], [-37.926, -24.048], [-37.936, -24.035], [-37.973, -23.741], [-37.977, -23.64], [-37.976, -22.655], [-37.973, -22.597], [-37.935, -22.261], [-37.925, -22.251], [-37.932, -22.238], [-37.896, -21.985], [-37.886, -21.975], [-37.893, -21.963], [-37.857, -21.749], [-37.491, -20.332], [-36.443, -18.187], [-34.317, -15.111], [-31.828, -12.007], [-31.705, -11.859], [-31.237, -11.337], [-31.114, -11.189], [-30.922, -10.983], [-30.798, -10.835], [-29.779, -9.721], [-29.654, -9.574], [-29.306, -9.208], [-29.183, -9.062], [-28.793, -8.657]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [200, 200], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Mission 3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 12, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 43, "s": [60]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 52, "s": [60]}, {"t": 53, "s": [0]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 12, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 24, "s": [-36]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 32, "s": [-36]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [-36]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 42, "s": [-24]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 43, "s": [-24]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 44, "s": [-36]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 45, "s": [-12]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 46, "s": [-36]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 47, "s": [-12]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 48, "s": [-36]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 49, "s": [-12]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 50, "s": [-36]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 51, "s": [-12]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 52, "s": [-36]}, {"t": 53, "s": [-24]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [100, 100.262, 0], "to": [3.547, 2.626, 0], "ti": [-3.547, -2.626, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [121.28, 116.015, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [121.28, 116.015, 0], "to": [22.333, 0, 0], "ti": [-11.12, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [255.28, 116.015, 0], "to": [11.12, 0, 0], "ti": [11.213, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [188, 116.015, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [188, 116.015, 0], "to": [-2.667, 0, 0], "ti": [2.667, 0, 0]}, {"t": 42, "s": [172, 116.015, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"t": 8, "s": [72, 72, 100]}], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-88.785, -129.988], [-124.609, -8.71], [-11.624, 27.579], [6.12, -23.092]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "蒙版 1"}], "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.05, -0.065], [-0.142, -0.148], [0, 0], [-0.062, -0.081], [-0.182, -0.187], [-0.084, -0.111], [-0.405, -0.41], [-0.279, -0.266], [-0.358, -0.353], [-0.122, -0.099], [-0.161, -0.156], [-0.078, -0.064], [0, 0], [-0.045, -0.046], [-0.109, -0.104], [-0.063, -0.054], [-0.082, -0.077], [-0.063, -0.054], [0, 0], [-0.047, -0.045], [-0.174, -0.156], [-0.047, -0.045], [-0.455, -0.391], [-1.373, -1.059], [-2.163, -1.499], [-1.746, -1.105], [-0.963, -0.599], [-0.938, -0.577], [-0.046, -0.042], [-0.119, -0.063], [0.074, -0.073], [-0.078, -0.05], [-0.2, -0.119], [-0.063, 0.107], [-0.081, -0.052], [-0.252, -0.158], [0.059, -0.066], [-0.093, -0.058], [-0.213, -0.134], [-0.037, 0.067], [-0.123, -0.109], [-0.128, -0.076], [-0.078, 0.032], [-0.052, -0.03], [-0.806, -0.453], [-0.616, -0.328], [-0.21, -0.028], [-0.129, 0.058], [-0.099, 0.161], [-0.223, 0.343], [-0.123, -0.08], [-0.474, -0.315], [-0.068, 0.106], [-0.104, -0.068], [-0.547, -0.357], [-0.952, -0.622], [-0.136, -0.245], [-0.388, -0.305], [-0.618, -0.106], [-0.075, -0.046], [0, 0], [-0.069, -0.005], [-0.16, -0.039], [-0.058, -0.016], [-0.063, 0], [-0.406, -0.005], [-0.221, -0.039], [-0.058, -0.016], [-0.063, 0], [-0.432, -0.005], [-0.207, -0.039], [-0.058, -0.015], [-0.063, -0.001], [-0.008, -0.315], [-0.045, -0.388], [-0.123, 0.003], [-0.135, -0.047], [0, 0], [0, 0], [-0.136, -0.049], [-0.061, 0.05], [0, 0.075], [0, 2.549], [0.002, 0.052], [-0.073, -0.003], [-0.228, -0.038], [-0.129, 0.015], [0.004, -0.123], [0.001, -0.288], [0.003, -0.699], [-0.051, -0.188], [-0.002, -0.021], [-0.045, -0.093], [-0.002, -0.021], [-0.048, -0.08], [0, 0], [0, 0], [-0.046, -0.065], [-0.011, -0.083], [-0.147, -0.407], [-0.241, -0.41], [-0.424, -0.432], [-0.235, -0.037], [-0.095, -0.043], [-0.065, 0], [-1.017, -0.001], [-0.052, 0.148], [0, 0.06], [0.002, 8.295], [0.065, 0.051], [0.116, 0], [1.011, 0.027], [0.346, -0.433], [0.265, -1.511], [-0.002, -0.899], [0.002, -0.552], [0.113, -0.002], [0.381, -0.009], [-0.002, 0.096], [0, 0.328], [-0.006, 2.272], [0.197, -0.028], [0.255, -0.022], [0, -0.33], [0.001, -2.252], [0.235, -0.006], [0.263, -0.006], [0, -0.752], [-0.004, -1.668], [0.142, -0.002], [0.899, -0.018], [0.064, 0.041], [1.653, 1.057], [-0.073, 0.082], [0.14, 0.087], [0.454, 0.289], [-0.125, 0.196], [-0.209, 0.326], [0.398, 0.327], [2.043, 1.411], [0.051, 0.111], [0.303, 0.22], [0.582, 0.046], [0.089, 0.056], [0.59, 0.369], [0.076, 0.116], [0.098, 0.09], [0.223, 0.088], [0.094, -0.007], [0.185, 0.11], [1.502, 0.849], [1.045, 0.603], [0.946, 0.579], [0.289, 0.34], [0.076, 0.064], [0, 0], [0.08, 0.042], [0.168, 0.06], [0.434, 0.142], [0.19, 0.561], [0.212, 0.134], [0.3, 0.101], [0.342, 0.258], [0.058, 0.037], [0.105, 0.225], [0.214, 0.192], [0.345, 0.147], [0.316, 0.233], [0.056, 0.039], [0.1, 0.234], [0.144, 0.218], [0.501, 0.227], [0.233, 0.465], [0.055, 0.176], [0.293, 0.199], [0.193, 0.207], [0.168, 0.155], [0.328, 1.276], [0.346, 0.591], [0.343, 0.439], [0.06, 0.039], [0.071, 0.108], [0.084, 0.067], [0.14, 0.14], [0.121, 0.08], [0.465, 0.335], [1.366, 0.501], [0.39, 0.113], [0.063, 0.041], [0.009, 0.012], [0.08, 0.039], [0.021, 0.002], [0.162, 0.048], [0.038, 0.023], [0.189, -0.05], [0.021, -0.002], [0.08, -0.044], [0.057, -0.01], [0.517, -0.476], [0.083, -0.095], [0.21, -0.618], [0.026, -0.324], [0.038, -0.065], [0, 0], [0, 0], [0.042, -0.094], [0.002, -0.034], [-0.034, -0.328], [-0.001, -0.019], [-0.052, -0.107], [0, 0], [0, 0], [-0.049, -0.079], [0, 0], [0, 0], [-0.045, -0.066], [-0.15, -0.465], [-0.496, -0.657], [-0.734, -1.009], [-0.878, -0.996], [-0.04, -0.05], [-0.156, -0.174], [-0.04, -0.05], [-0.064, -0.069], [-0.042, -0.049], [-0.34, -0.371], [-0.043, -0.048], [0, 0], [-0.041, -0.049], [-0.13, -0.135]], "o": [[0.142, 0.148], [0, 0], [0.067, 0.076], [0.182, 0.187], [0.089, 0.107], [0.405, 0.41], [0.265, 0.279], [0.358, 0.353], [0.127, 0.094], [0.161, 0.156], [0.083, 0.059], [0, 0], [0.049, 0.041], [0.109, 0.104], [0.068, 0.048], [0.082, 0.077], [0.067, 0.049], [0, 0], [0.05, 0.041], [0.174, 0.156], [0.05, 0.04], [0.455, 0.392], [1.316, 1.13], [2.084, 1.607], [1.698, 1.177], [0.959, 0.606], [0.934, 0.581], [0.054, 0.032], [0.112, 0.077], [0.095, 0.051], [-0.098, 0.096], [0.196, 0.127], [0.066, 0.039], [0.061, -0.103], [0.25, 0.16], [0.069, 0.043], [-0.094, 0.107], [0.213, 0.134], [0.066, 0.032], [0.032, -0.093], [0.11, 0.097], [0.079, 0.014], [0.071, -0.048], [0.804, 0.458], [0.608, 0.342], [0.177, 0.094], [0.131, 0.024], [0.17, -0.073], [0.215, -0.348], [0.062, -0.095], [0.477, 0.31], [0.112, 0.074], [0.075, -0.116], [0.547, 0.358], [0.953, 0.622], [0.211, 0.138], [0.24, 0.433], [0.493, 0.386], [0.074, 0.054], [0, 0], [0.065, 0.045], [0.161, 0.012], [0.058, -0.028], [0.06, 0.041], [0.406, 0.002], [0.222, 0.003], [0.058, -0.028], [0.06, 0.041], [0.432, 0.002], [0.209, 0.002], [0.058, -0.027], [0.06, 0.04], [0.304, 0.007], [0.009, 0.388], [0.03, 0.162], [0.134, 0.054], [0, 0], [0, 0], [0.135, 0.058], [0.067, -0.02], [0.056, -0.07], [0.002, -2.549], [0, -0.052], [-0.003, -0.076], [0.228, 0.01], [0.129, -0.037], [0.119, -0.013], [-0.009, 0.288], [-0.002, 0.699], [0.049, 0.188], [0.002, 0.021], [0.041, 0.093], [0.002, 0.021], [0.039, 0.081], [0, 0], [0, 0], [0.037, 0.067], [0.005, 0.088], [0.056, 0.435], [0.16, 0.444], [0.306, 0.521], [0.159, 0.162], [0.095, 0.048], [0.065, -0.004], [1.017, -0.001], [0.127, 0], [0.07, -0.048], [0.002, -8.295], [0, -0.061], [-0.061, -0.114], [-1.011, 0.002], [-0.575, -0.015], [-0.957, 1.199], [-0.157, 0.892], [0.001, 0.552], [0, 0.057], [-0.381, 0.007], [-0.107, 0.003], [0.005, -0.328], [0.001, -2.272], [0, -0.19], [-0.253, 0.036], [-0.332, 0.029], [0, 2.252], [0, 0.235], [-0.263, 0.007], [-0.751, 0.015], [-0.001, 1.668], [0, 0.131], [-0.899, 0.01], [-0.075, 0.002], [-1.651, -1.06], [-0.084, -0.054], [0.11, -0.123], [-0.457, -0.284], [-0.183, -0.116], [0.209, -0.326], [0.276, -0.429], [-1.919, -1.576], [-0.097, -0.067], [-0.162, -0.354], [-0.453, -0.328], [-0.11, -0.009], [-0.59, -0.369], [-0.111, -0.069], [-0.069, -0.104], [-0.175, -0.162], [-0.085, -0.033], [-0.201, 0.015], [-1.482, -0.883], [-1.05, -0.593], [-0.96, -0.554], [-0.369, -0.226], [-0.065, -0.075], [0, 0], [-0.056, -0.069], [-0.161, -0.078], [-0.43, -0.154], [-0.547, -0.18], [-0.081, -0.239], [-0.272, -0.172], [-0.396, -0.133], [-0.039, -0.06], [-0.214, -0.142], [-0.114, -0.244], [-0.289, -0.259], [-0.355, -0.151], [-0.039, -0.059], [-0.223, -0.146], [-0.101, -0.236], [-0.322, -0.487], [-0.439, -0.199], [-0.084, -0.168], [-0.105, -0.338], [-0.229, -0.157], [-0.164, -0.159], [-0.914, -0.919], [-0.169, -0.657], [-0.284, -0.484], [-0.04, -0.055], [-0.096, -0.084], [-0.06, -0.089], [-0.14, -0.14], [-0.098, -0.104], [-0.442, -0.363], [-1.179, -0.847], [-0.382, -0.14], [-0.063, -0.043], [-0.011, 0.009], [-0.08, -0.043], [-0.021, -0.002], [-0.162, -0.05], [-0.038, 0.017], [-0.189, 0.049], [-0.021, 0.002], [-0.081, 0.038], [-0.056, 0.015], [-0.692, 0.125], [-0.095, 0.082], [-0.445, 0.477], [-0.103, 0.304], [-0.04, 0.065], [0, 0], [0, 0], [-0.049, 0.093], [-0.002, 0.034], [-0.028, 0.328], [0.001, 0.019], [0.045, 0.108], [0, 0], [0, 0], [0.04, 0.08], [0, 0], [0, 0], [0.036, 0.067], [0.076, 0.484], [0.247, 0.764], [0.75, 0.994], [0.782, 1.074], [0.045, 0.046], [0.156, 0.174], [0.045, 0.046], [0.064, 0.069], [0.045, 0.046], [0.34, 0.371], [0.047, 0.045], [0, 0], [0.045, 0.045], [0.13, 0.135], [0.056, 0.061]], "v": [[-28.63, -8.471], [-28.202, -8.026], [-28.203, -8.027], [-28, -7.802], [-27.454, -7.241], [-27.172, -6.935], [-25.957, -5.704], [-25.14, -4.887], [-24.065, -3.828], [-23.72, -3.508], [-23.237, -3.039], [-23.01, -2.838], [-22.646, -2.488], [-22.5, -2.365], [-22.173, -2.053], [-21.985, -1.892], [-21.74, -1.66], [-21.552, -1.498], [-21.306, -1.266], [-21.157, -1.143], [-20.636, -0.675], [-20.487, -0.552], [-19.123, 0.624], [-15.081, 3.897], [-8.709, 8.554], [-3.547, 11.982], [-0.667, 13.795], [2.145, 15.525], [2.291, 15.642], [2.629, 15.87], [2.678, 16.05], [2.717, 16.248], [3.307, 16.623], [3.524, 16.611], [3.736, 16.584], [4.49, 17.061], [4.517, 17.215], [4.581, 17.448], [5.22, 17.851], [5.376, 17.805], [5.551, 17.744], [5.93, 17.975], [6.166, 17.967], [6.339, 18.006], [8.749, 19.379], [10.586, 20.385], [11.132, 20.646], [11.522, 20.635], [11.92, 20.287], [12.586, 19.256], [12.837, 19.177], [14.271, 20.105], [14.527, 20.067], [14.788, 20.026], [16.439, 21.084], [19.305, 22.938], [19.897, 23.419], [20.858, 24.514], [22.522, 25.254], [22.764, 25.294], [22.76, 25.293], [22.968, 25.315], [23.452, 25.334], [23.626, 25.333], [23.816, 25.354], [25.033, 25.356], [25.698, 25.373], [25.872, 25.372], [26.063, 25.393], [27.358, 25.396], [27.984, 25.412], [28.158, 25.412], [28.348, 25.433], [28.613, 25.707], [28.632, 26.873], [28.907, 27.028], [29.321, 27.066], [29.331, 27.059], [29.341, 27.066], [29.758, 27.106], [29.963, 27.066], [29.99, 26.839], [29.991, 19.192], [29.991, 19.035], [30.098, 18.932], [30.783, 18.95], [31.173, 18.932], [31.328, 19.082], [31.324, 19.946], [31.315, 22.044], [31.352, 22.617], [31.356, 22.681], [31.39, 22.971], [31.396, 23.033], [31.432, 23.287], [31.442, 23.297], [31.435, 23.31], [31.472, 23.523], [31.52, 23.778], [31.876, 25.027], [32.479, 26.307], [33.533, 27.763], [34.111, 28.093], [34.409, 28.131], [34.604, 28.119], [37.654, 28.119], [37.957, 27.97], [37.991, 27.791], [37.991, 2.906], [37.958, 2.723], [37.679, 2.597], [34.645, 2.591], [33.303, 3.23], [31.468, 7.292], [31.325, 9.983], [31.324, 11.637], [31.246, 11.781], [30.105, 11.804], [29.979, 11.656], [29.981, 10.671], [29.986, 3.855], [29.741, 3.622], [28.976, 3.695], [28.639, 4.059], [28.637, 10.816], [28.378, 11.081], [27.591, 11.099], [26.459, 12.254], [26.461, 17.257], [26.292, 17.439], [23.593, 17.486], [23.39, 17.432], [18.433, 14.256], [18.392, 14.075], [18.332, 13.78], [16.971, 12.912], [16.901, 12.544], [17.529, 11.567], [17.358, 10.504], [11.409, 6.031], [11.152, 5.794], [10.4, 4.977], [8.895, 4.299], [8.61, 4.173], [6.842, 3.065], [6.573, 2.795], [6.371, 2.473], [5.782, 2.086], [5.519, 2.017], [4.938, 1.92], [0.454, -0.665], [-2.699, -2.441], [-5.557, -4.144], [-6.611, -4.907], [-6.821, -5.116], [-7.123, -5.419], [-7.321, -5.592], [-7.806, -5.826], [-9.106, -6.262], [-10.254, -7.329], [-10.696, -7.876], [-11.573, -8.233], [-12.719, -8.731], [-12.876, -8.863], [-13.312, -9.445], [-13.724, -10.138], [-14.711, -10.673], [-15.754, -11.173], [-15.909, -11.307], [-16.343, -11.912], [-16.649, -12.62], [-17.983, -13.55], [-19.088, -14.414], [-19.293, -14.933], [-19.89, -15.721], [-20.562, -16.219], [-21.049, -16.7], [-23.004, -19.934], [-23.747, -21.817], [-24.735, -23.166], [-24.868, -23.32], [-25.129, -23.599], [-25.345, -23.832], [-25.765, -24.252], [-26.078, -24.546], [-27.413, -25.623], [-31.211, -27.679], [-32.38, -28.016], [-32.586, -28.051], [-32.616, -28.055], [-32.869, -28.089], [-32.932, -28.095], [-33.425, -28.13], [-33.54, -28.131], [-34.114, -28.095], [-34.175, -28.089], [-34.43, -28.055], [-34.598, -28.011], [-36.41, -27.108], [-36.676, -26.842], [-37.657, -25.199], [-37.899, -24.271], [-37.933, -24.063], [-37.926, -24.048], [-37.936, -24.035], [-37.973, -23.741], [-37.977, -23.64], [-37.976, -22.655], [-37.973, -22.597], [-37.935, -22.261], [-37.925, -22.251], [-37.932, -22.238], [-37.896, -21.985], [-37.886, -21.975], [-37.893, -21.963], [-37.857, -21.749], [-37.491, -20.332], [-36.443, -18.187], [-34.317, -15.111], [-31.828, -12.007], [-31.705, -11.859], [-31.237, -11.337], [-31.114, -11.189], [-30.922, -10.983], [-30.798, -10.835], [-29.779, -9.721], [-29.654, -9.574], [-29.306, -9.208], [-29.183, -9.062], [-28.793, -8.657]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [200, 200], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Mission", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 8, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 12, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [100]}, {"t": 57, "s": [100]}], "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 12, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [0]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 24, "s": [-36]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 32, "s": [-36]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 39, "s": [-36]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 42, "s": [-24]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 53, "s": [-24]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 55, "s": [-24]}, {"t": 57, "s": [0]}], "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [100, 100.262, 0], "to": [3.547, 2.626, 0], "ti": [-3.547, -2.626, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 8, "s": [121.28, 116.015, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [121.28, 116.015, 0], "to": [22.333, 0, 0], "ti": [-11.12, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 18, "s": [255.28, 116.015, 0], "to": [11.12, 0, 0], "ti": [11.213, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [188, 116.015, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 39, "s": [188, 116.015, 0], "to": [-2.667, 0, 0], "ti": [2.667, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 42, "s": [172, 116.015, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 53, "s": [172, 116.015, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 55, "s": [172, 116.015, 0], "to": [-8.453, 0, 0], "ti": [12, 2.626, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 57, "s": [121.28, 116.015, 0], "to": [-12, -2.626, 0], "ti": [3.547, 2.626, 0]}, {"t": 60, "s": [100, 100.262, 0]}], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 8, "s": [72, 72, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 55, "s": [72, 72, 100]}, {"t": 60, "s": [100, 100, 100]}], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.05, -0.065], [-0.142, -0.148], [0, 0], [-0.062, -0.081], [-0.182, -0.187], [-0.084, -0.111], [-0.405, -0.41], [-0.279, -0.266], [-0.358, -0.353], [-0.122, -0.099], [-0.161, -0.156], [-0.078, -0.064], [0, 0], [-0.045, -0.046], [-0.109, -0.104], [-0.063, -0.054], [-0.082, -0.077], [-0.063, -0.054], [0, 0], [-0.047, -0.045], [-0.174, -0.156], [-0.047, -0.045], [-0.455, -0.391], [-1.373, -1.059], [-2.163, -1.499], [-1.746, -1.105], [-0.963, -0.599], [-0.938, -0.577], [-0.046, -0.042], [-0.119, -0.063], [0.074, -0.073], [-0.078, -0.05], [-0.2, -0.119], [-0.063, 0.107], [-0.081, -0.052], [-0.252, -0.158], [0.059, -0.066], [-0.093, -0.058], [-0.213, -0.134], [-0.037, 0.067], [-0.123, -0.109], [-0.128, -0.076], [-0.078, 0.032], [-0.052, -0.03], [-0.806, -0.453], [-0.616, -0.328], [-0.21, -0.028], [-0.129, 0.058], [-0.099, 0.161], [-0.223, 0.343], [-0.123, -0.08], [-0.474, -0.315], [-0.068, 0.106], [-0.104, -0.068], [-0.547, -0.357], [-0.952, -0.622], [-0.136, -0.245], [-0.388, -0.305], [-0.618, -0.106], [-0.075, -0.046], [0, 0], [-0.069, -0.005], [-0.16, -0.039], [-0.058, -0.016], [-0.063, 0], [-0.406, -0.005], [-0.221, -0.039], [-0.058, -0.016], [-0.063, 0], [-0.432, -0.005], [-0.207, -0.039], [-0.058, -0.015], [-0.063, -0.001], [-0.008, -0.315], [-0.045, -0.388], [-0.123, 0.003], [-0.135, -0.047], [0, 0], [0, 0], [-0.136, -0.049], [-0.061, 0.05], [0, 0.075], [0, 2.549], [0.002, 0.052], [-0.073, -0.003], [-0.228, -0.038], [-0.129, 0.015], [0.004, -0.123], [0.001, -0.288], [0.003, -0.699], [-0.051, -0.188], [-0.002, -0.021], [-0.045, -0.093], [-0.002, -0.021], [-0.048, -0.08], [0, 0], [0, 0], [-0.046, -0.065], [-0.011, -0.083], [-0.147, -0.407], [-0.241, -0.41], [-0.424, -0.432], [-0.235, -0.037], [-0.095, -0.043], [-0.065, 0], [-1.017, -0.001], [-0.052, 0.148], [0, 0.06], [0.002, 8.295], [0.065, 0.051], [0.116, 0], [1.011, 0.027], [0.346, -0.433], [0.265, -1.511], [-0.002, -0.899], [0.002, -0.552], [0.113, -0.002], [0.381, -0.009], [-0.002, 0.096], [0, 0.328], [-0.006, 2.272], [0.197, -0.028], [0.255, -0.022], [0, -0.33], [0.001, -2.252], [0.235, -0.006], [0.263, -0.006], [0, -0.752], [-0.004, -1.668], [0.142, -0.002], [0.899, -0.018], [0.064, 0.041], [1.653, 1.057], [-0.073, 0.082], [0.14, 0.087], [0.454, 0.289], [-0.125, 0.196], [-0.209, 0.326], [0.398, 0.327], [2.043, 1.411], [0.051, 0.111], [0.303, 0.22], [0.582, 0.046], [0.089, 0.056], [0.59, 0.369], [0.076, 0.116], [0.098, 0.09], [0.223, 0.088], [0.094, -0.007], [0.185, 0.11], [1.502, 0.849], [1.045, 0.603], [0.946, 0.579], [0.289, 0.34], [0.076, 0.064], [0, 0], [0.08, 0.042], [0.168, 0.06], [0.434, 0.142], [0.19, 0.561], [0.212, 0.134], [0.3, 0.101], [0.342, 0.258], [0.058, 0.037], [0.105, 0.225], [0.214, 0.192], [0.345, 0.147], [0.316, 0.233], [0.056, 0.039], [0.1, 0.234], [0.144, 0.218], [0.501, 0.227], [0.233, 0.465], [0.055, 0.176], [0.293, 0.199], [0.193, 0.207], [0.168, 0.155], [0.328, 1.276], [0.346, 0.591], [0.343, 0.439], [0.06, 0.039], [0.071, 0.108], [0.084, 0.067], [0.14, 0.14], [0.121, 0.08], [0.465, 0.335], [1.366, 0.501], [0.39, 0.113], [0.063, 0.041], [0.009, 0.012], [0.08, 0.039], [0.021, 0.002], [0.162, 0.048], [0.038, 0.023], [0.189, -0.05], [0.021, -0.002], [0.08, -0.044], [0.057, -0.01], [0.517, -0.476], [0.083, -0.095], [0.21, -0.618], [0.026, -0.324], [0.038, -0.065], [0, 0], [0, 0], [0.042, -0.094], [0.002, -0.034], [-0.034, -0.328], [-0.001, -0.019], [-0.052, -0.107], [0, 0], [0, 0], [-0.049, -0.079], [0, 0], [0, 0], [-0.045, -0.066], [-0.15, -0.465], [-0.496, -0.657], [-0.734, -1.009], [-0.878, -0.996], [-0.04, -0.05], [-0.156, -0.174], [-0.04, -0.05], [-0.064, -0.069], [-0.042, -0.049], [-0.34, -0.371], [-0.043, -0.048], [0, 0], [-0.041, -0.049], [-0.13, -0.135]], "o": [[0.142, 0.148], [0, 0], [0.067, 0.076], [0.182, 0.187], [0.089, 0.107], [0.405, 0.41], [0.265, 0.279], [0.358, 0.353], [0.127, 0.094], [0.161, 0.156], [0.083, 0.059], [0, 0], [0.049, 0.041], [0.109, 0.104], [0.068, 0.048], [0.082, 0.077], [0.067, 0.049], [0, 0], [0.05, 0.041], [0.174, 0.156], [0.05, 0.04], [0.455, 0.392], [1.316, 1.13], [2.084, 1.607], [1.698, 1.177], [0.959, 0.606], [0.934, 0.581], [0.054, 0.032], [0.112, 0.077], [0.095, 0.051], [-0.098, 0.096], [0.196, 0.127], [0.066, 0.039], [0.061, -0.103], [0.25, 0.16], [0.069, 0.043], [-0.094, 0.107], [0.213, 0.134], [0.066, 0.032], [0.032, -0.093], [0.11, 0.097], [0.079, 0.014], [0.071, -0.048], [0.804, 0.458], [0.608, 0.342], [0.177, 0.094], [0.131, 0.024], [0.17, -0.073], [0.215, -0.348], [0.062, -0.095], [0.477, 0.31], [0.112, 0.074], [0.075, -0.116], [0.547, 0.358], [0.953, 0.622], [0.211, 0.138], [0.24, 0.433], [0.493, 0.386], [0.074, 0.054], [0, 0], [0.065, 0.045], [0.161, 0.012], [0.058, -0.028], [0.06, 0.041], [0.406, 0.002], [0.222, 0.003], [0.058, -0.028], [0.06, 0.041], [0.432, 0.002], [0.209, 0.002], [0.058, -0.027], [0.06, 0.04], [0.304, 0.007], [0.009, 0.388], [0.03, 0.162], [0.134, 0.054], [0, 0], [0, 0], [0.135, 0.058], [0.067, -0.02], [0.056, -0.07], [0.002, -2.549], [0, -0.052], [-0.003, -0.076], [0.228, 0.01], [0.129, -0.037], [0.119, -0.013], [-0.009, 0.288], [-0.002, 0.699], [0.049, 0.188], [0.002, 0.021], [0.041, 0.093], [0.002, 0.021], [0.039, 0.081], [0, 0], [0, 0], [0.037, 0.067], [0.005, 0.088], [0.056, 0.435], [0.16, 0.444], [0.306, 0.521], [0.159, 0.162], [0.095, 0.048], [0.065, -0.004], [1.017, -0.001], [0.127, 0], [0.07, -0.048], [0.002, -8.295], [0, -0.061], [-0.061, -0.114], [-1.011, 0.002], [-0.575, -0.015], [-0.957, 1.199], [-0.157, 0.892], [0.001, 0.552], [0, 0.057], [-0.381, 0.007], [-0.107, 0.003], [0.005, -0.328], [0.001, -2.272], [0, -0.19], [-0.253, 0.036], [-0.332, 0.029], [0, 2.252], [0, 0.235], [-0.263, 0.007], [-0.751, 0.015], [-0.001, 1.668], [0, 0.131], [-0.899, 0.01], [-0.075, 0.002], [-1.651, -1.06], [-0.084, -0.054], [0.11, -0.123], [-0.457, -0.284], [-0.183, -0.116], [0.209, -0.326], [0.276, -0.429], [-1.919, -1.576], [-0.097, -0.067], [-0.162, -0.354], [-0.453, -0.328], [-0.11, -0.009], [-0.59, -0.369], [-0.111, -0.069], [-0.069, -0.104], [-0.175, -0.162], [-0.085, -0.033], [-0.201, 0.015], [-1.482, -0.883], [-1.05, -0.593], [-0.96, -0.554], [-0.369, -0.226], [-0.065, -0.075], [0, 0], [-0.056, -0.069], [-0.161, -0.078], [-0.43, -0.154], [-0.547, -0.18], [-0.081, -0.239], [-0.272, -0.172], [-0.396, -0.133], [-0.039, -0.06], [-0.214, -0.142], [-0.114, -0.244], [-0.289, -0.259], [-0.355, -0.151], [-0.039, -0.059], [-0.223, -0.146], [-0.101, -0.236], [-0.322, -0.487], [-0.439, -0.199], [-0.084, -0.168], [-0.105, -0.338], [-0.229, -0.157], [-0.164, -0.159], [-0.914, -0.919], [-0.169, -0.657], [-0.284, -0.484], [-0.04, -0.055], [-0.096, -0.084], [-0.06, -0.089], [-0.14, -0.14], [-0.098, -0.104], [-0.442, -0.363], [-1.179, -0.847], [-0.382, -0.14], [-0.063, -0.043], [-0.011, 0.009], [-0.08, -0.043], [-0.021, -0.002], [-0.162, -0.05], [-0.038, 0.017], [-0.189, 0.049], [-0.021, 0.002], [-0.081, 0.038], [-0.056, 0.015], [-0.692, 0.125], [-0.095, 0.082], [-0.445, 0.477], [-0.103, 0.304], [-0.04, 0.065], [0, 0], [0, 0], [-0.049, 0.093], [-0.002, 0.034], [-0.028, 0.328], [0.001, 0.019], [0.045, 0.108], [0, 0], [0, 0], [0.04, 0.08], [0, 0], [0, 0], [0.036, 0.067], [0.076, 0.484], [0.247, 0.764], [0.75, 0.994], [0.782, 1.074], [0.045, 0.046], [0.156, 0.174], [0.045, 0.046], [0.064, 0.069], [0.045, 0.046], [0.34, 0.371], [0.047, 0.045], [0, 0], [0.045, 0.045], [0.13, 0.135], [0.056, 0.061]], "v": [[-28.63, -8.471], [-28.202, -8.026], [-28.203, -8.027], [-28, -7.802], [-27.454, -7.241], [-27.172, -6.935], [-25.957, -5.704], [-25.14, -4.887], [-24.065, -3.828], [-23.72, -3.508], [-23.237, -3.039], [-23.01, -2.838], [-22.646, -2.488], [-22.5, -2.365], [-22.173, -2.053], [-21.985, -1.892], [-21.74, -1.66], [-21.552, -1.498], [-21.306, -1.266], [-21.157, -1.143], [-20.636, -0.675], [-20.487, -0.552], [-19.123, 0.624], [-15.081, 3.897], [-8.709, 8.554], [-3.547, 11.982], [-0.667, 13.795], [2.145, 15.525], [2.291, 15.642], [2.629, 15.87], [2.678, 16.05], [2.717, 16.248], [3.307, 16.623], [3.524, 16.611], [3.736, 16.584], [4.49, 17.061], [4.517, 17.215], [4.581, 17.448], [5.22, 17.851], [5.376, 17.805], [5.551, 17.744], [5.93, 17.975], [6.166, 17.967], [6.339, 18.006], [8.749, 19.379], [10.586, 20.385], [11.132, 20.646], [11.522, 20.635], [11.92, 20.287], [12.586, 19.256], [12.837, 19.177], [14.271, 20.105], [14.527, 20.067], [14.788, 20.026], [16.439, 21.084], [19.305, 22.938], [19.897, 23.419], [20.858, 24.514], [22.522, 25.254], [22.764, 25.294], [22.76, 25.293], [22.968, 25.315], [23.452, 25.334], [23.626, 25.333], [23.816, 25.354], [25.033, 25.356], [25.698, 25.373], [25.872, 25.372], [26.063, 25.393], [27.358, 25.396], [27.984, 25.412], [28.158, 25.412], [28.348, 25.433], [28.613, 25.707], [28.632, 26.873], [28.907, 27.028], [29.321, 27.066], [29.331, 27.059], [29.341, 27.066], [29.758, 27.106], [29.963, 27.066], [29.99, 26.839], [29.991, 19.192], [29.991, 19.035], [30.098, 18.932], [30.783, 18.95], [31.173, 18.932], [31.328, 19.082], [31.324, 19.946], [31.315, 22.044], [31.352, 22.617], [31.356, 22.681], [31.39, 22.971], [31.396, 23.033], [31.432, 23.287], [31.442, 23.297], [31.435, 23.31], [31.472, 23.523], [31.52, 23.778], [31.876, 25.027], [32.479, 26.307], [33.533, 27.763], [34.111, 28.093], [34.409, 28.131], [34.604, 28.119], [37.654, 28.119], [37.957, 27.97], [37.991, 27.791], [37.991, 2.906], [37.958, 2.723], [37.679, 2.597], [34.645, 2.591], [33.303, 3.23], [31.468, 7.292], [31.325, 9.983], [31.324, 11.637], [31.246, 11.781], [30.105, 11.804], [29.979, 11.656], [29.981, 10.671], [29.986, 3.855], [29.741, 3.622], [28.976, 3.695], [28.639, 4.059], [28.637, 10.816], [28.378, 11.081], [27.591, 11.099], [26.459, 12.254], [26.461, 17.257], [26.292, 17.439], [23.593, 17.486], [23.39, 17.432], [18.433, 14.256], [18.392, 14.075], [18.332, 13.78], [16.971, 12.912], [16.901, 12.544], [17.529, 11.567], [17.358, 10.504], [11.409, 6.031], [11.152, 5.794], [10.4, 4.977], [8.895, 4.299], [8.61, 4.173], [6.842, 3.065], [6.573, 2.795], [6.371, 2.473], [5.782, 2.086], [5.519, 2.017], [4.938, 1.92], [0.454, -0.665], [-2.699, -2.441], [-5.557, -4.144], [-6.611, -4.907], [-6.821, -5.116], [-7.123, -5.419], [-7.321, -5.592], [-7.806, -5.826], [-9.106, -6.262], [-10.254, -7.329], [-10.696, -7.876], [-11.573, -8.233], [-12.719, -8.731], [-12.876, -8.863], [-13.312, -9.445], [-13.724, -10.138], [-14.711, -10.673], [-15.754, -11.173], [-15.909, -11.307], [-16.343, -11.912], [-16.649, -12.62], [-17.983, -13.55], [-19.088, -14.414], [-19.293, -14.933], [-19.89, -15.721], [-20.562, -16.219], [-21.049, -16.7], [-23.004, -19.934], [-23.747, -21.817], [-24.735, -23.166], [-24.868, -23.32], [-25.129, -23.599], [-25.345, -23.832], [-25.765, -24.252], [-26.078, -24.546], [-27.413, -25.623], [-31.211, -27.679], [-32.38, -28.016], [-32.586, -28.051], [-32.616, -28.055], [-32.869, -28.089], [-32.932, -28.095], [-33.425, -28.13], [-33.54, -28.131], [-34.114, -28.095], [-34.175, -28.089], [-34.43, -28.055], [-34.598, -28.011], [-36.41, -27.108], [-36.676, -26.842], [-37.657, -25.199], [-37.899, -24.271], [-37.933, -24.063], [-37.926, -24.048], [-37.936, -24.035], [-37.973, -23.741], [-37.977, -23.64], [-37.976, -22.655], [-37.973, -22.597], [-37.935, -22.261], [-37.925, -22.251], [-37.932, -22.238], [-37.896, -21.985], [-37.886, -21.975], [-37.893, -21.963], [-37.857, -21.749], [-37.491, -20.332], [-36.443, -18.187], [-34.317, -15.111], [-31.828, -12.007], [-31.705, -11.859], [-31.237, -11.337], [-31.114, -11.189], [-30.922, -10.983], [-30.798, -10.835], [-29.779, -9.721], [-29.654, -9.574], [-29.306, -9.208], [-29.183, -9.062], [-28.793, -8.657]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [200, 200], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 2, "nm": "20240614更新", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 22, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 53, "s": [72]}, {"t": 60, "s": [0]}], "ix": 11}, "r": {"a": 0, "k": -90, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 12, "s": [-100.803, 100, 0], "to": [33.467, 0, 0], "ti": [-33.467, 0, 0]}, {"t": 18, "s": [100, 100, 0]}], "ix": 2}, "a": {"a": 0, "k": [240, 240, 0], "ix": 1}, "s": {"a": 0, "k": [-46.875, 46.875, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "椭圆形 2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [100, 100, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [1, 0.176470588235, 0.537254901961, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [200, 200], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "椭圆形", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}]}], "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "内容", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [100, 100, 0], "ix": 2}, "a": {"a": 0, "k": [100, 100, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "hasMask": true, "masksProperties": [{"inv": false, "mode": "a", "pt": {"a": 0, "k": {"i": [[55.228, 0], [0, -55.228], [-55.228, 0], [0, 55.228]], "o": [[-55.228, 0], [0, 55.228], [55.228, 0], [0, -55.228]], "v": [[100, 0], [0, 100], [100, 200], [200, 100]], "c": true}, "ix": 1}, "o": {"a": 0, "k": 100, "ix": 3}, "x": {"a": 0, "k": 0, "ix": 4}, "nm": "蒙版 1"}], "w": 200, "h": 200, "ip": 0, "op": 150, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Frame 1321315411", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [100, 100, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [100, 100], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [200, 200], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Frame 1321315411", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 150, "st": 0, "bm": 0}], "markers": []}