package com.remotekmp.lightEffect.data.impl;

import kotlin.Metadata;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* compiled from: ToyLightEffectIterator.kt */
@Metadata(d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\t\b\u0086\b\u0018\u00002\u00020\u0001B\u001f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0004¢\u0006\u0004\b\u0007\u0010\bJ\u0010\u0010\n\u001a\u00020\tHÖ\u0001¢\u0006\u0004\b\n\u0010\u000bJ\u0010\u0010\f\u001a\u00020\u0002HÖ\u0001¢\u0006\u0004\b\f\u0010\rJ\u001a\u0010\u0010\u001a\u00020\u000f2\b\u0010\u000e\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\u0010\u0010\u0011R\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0012\u0010\u0013\u001a\u0004\b\u0014\u0010\rR\u0017\u0010\u0005\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b\u0014\u0010\u0015\u001a\u0004\b\u0012\u0010\u0016R\u0017\u0010\u0006\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b\u0017\u0010\u0015\u001a\u0004\b\u0017\u0010\u0016¨\u0006\u0018"}, d2 = {"Lcom/remotekmp/lightEffect/data/impl/h;", "", "", "gearIndex", "", "durationMs", "intervalMs", "<init>", "(IJJ)V", "", "toString", "()Ljava/lang/String;", "hashCode", "()I", "other", "", "equals", "(Ljava/lang/Object;)Z", "a", "I", r0.b.f37717b, "J", "()J", "c", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
/* renamed from: com.remotekmp.lightEffect.data.impl.h, reason: from toString */
/* loaded from: classes4.dex */
public final /* data */ class LightGearConfig {

    /* renamed from: a, reason: collision with root package name and from kotlin metadata and from toString */
    public final int gearIndex;

    /* renamed from: b, reason: collision with root package name and from kotlin metadata and from toString */
    public final long durationMs;

    /* renamed from: c, reason: collision with root package name and from kotlin metadata and from toString */
    public final long intervalMs;

    public LightGearConfig(int i10, long j10, long j11) {
        this.gearIndex = i10;
        this.durationMs = j10;
        this.intervalMs = j11;
    }

    /* renamed from: a, reason: from getter */
    public final long getDurationMs() {
        return this.durationMs;
    }

    /* renamed from: b, reason: from getter */
    public final int getGearIndex() {
        return this.gearIndex;
    }

    /* renamed from: c, reason: from getter */
    public final long getIntervalMs() {
        return this.intervalMs;
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof LightGearConfig)) {
            return false;
        }
        LightGearConfig lightGearConfig = (LightGearConfig) other;
        return this.gearIndex == lightGearConfig.gearIndex && this.durationMs == lightGearConfig.durationMs && this.intervalMs == lightGearConfig.intervalMs;
    }

    public int hashCode() {
        return (((this.gearIndex * 31) + androidx.compose.animation.a.a(this.durationMs)) * 31) + androidx.compose.animation.a.a(this.intervalMs);
    }

    @NotNull
    public String toString() {
        return "LightGearConfig(gearIndex=" + this.gearIndex + ", durationMs=" + this.durationMs + ", intervalMs=" + this.intervalMs + ')';
    }
}
