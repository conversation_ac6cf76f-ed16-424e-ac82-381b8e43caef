package hd;

import aw.n;
import com.wear.bean.ProgramPattern;
import ew.c2;
import ew.h2;
import ew.n0;
import ew.r2;
import ew.w0;
import hd.LightGear;
import java.util.List;
import kotlin.Deprecated;
import kotlin.DeprecationLevel;
import kotlin.Metadata;
import kotlin.jvm.JvmField;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlinx.serialization.UnknownFieldException;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* compiled from: LightMode.kt */
@n
@Metadata(d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\u000b\b\u0087\b\u0018\u0000 &2\u00020\u0001:\u0002\u001f!B%\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0002\u0012\f\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u0005¢\u0006\u0004\b\b\u0010\tB;\b\u0010\u0012\u0006\u0010\n\u001a\u00020\u0002\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0002\u0012\u000e\u0010\u0007\u001a\n\u0012\u0004\u0012\u00020\u0006\u0018\u00010\u0005\u0012\b\u0010\f\u001a\u0004\u0018\u00010\u000b¢\u0006\u0004\b\b\u0010\rJ'\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u000e\u001a\u00020\u00002\u0006\u0010\u0010\u001a\u00020\u000f2\u0006\u0010\u0012\u001a\u00020\u0011H\u0001¢\u0006\u0004\b\u0014\u0010\u0015J\u0010\u0010\u0017\u001a\u00020\u0016HÖ\u0001¢\u0006\u0004\b\u0017\u0010\u0018J\u0010\u0010\u0019\u001a\u00020\u0002HÖ\u0001¢\u0006\u0004\b\u0019\u0010\u001aJ\u001a\u0010\u001d\u001a\u00020\u001c2\b\u0010\u001b\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\u001d\u0010\u001eR\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u001f\u0010 \u001a\u0004\b!\u0010\u001aR\u0017\u0010\u0004\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b!\u0010 \u001a\u0004\b\"\u0010\u001aR\u001d\u0010\u0007\u001a\b\u0012\u0004\u0012\u00020\u00060\u00058\u0006¢\u0006\f\n\u0004\b#\u0010$\u001a\u0004\b#\u0010%¨\u0006'"}, d2 = {"Lhd/f;", "", "", "colorType", "modeCode", "", "Lhd/e;", "gears", "<init>", "(IILjava/util/List;)V", "seen0", "Lew/r2;", "serializationConstructorMarker", "(IIILjava/util/List;Lew/r2;)V", "self", "Ldw/d;", "output", "Lcw/f;", "serialDesc", "", "e", "(Lhd/f;Ldw/d;Lcw/f;)V", "", "toString", "()Ljava/lang/String;", "hashCode", "()I", "other", "", "equals", "(Ljava/lang/Object;)Z", "a", "I", r0.b.f37717b, "d", "c", "Ljava/util/List;", "()Ljava/util/List;", "Companion", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
/* renamed from: hd.f, reason: from toString */
/* loaded from: classes4.dex */
public final /* data */ class LightMode {

    /* renamed from: Companion, reason: from kotlin metadata */
    @NotNull
    public static final Companion INSTANCE = new Companion(null);

    /* renamed from: d, reason: collision with root package name */
    @JvmField
    @NotNull
    public static final aw.c<Object>[] f31336d = {null, null, new ew.f(LightGear.a.f31335a)};

    /* renamed from: a, reason: collision with root package name and from kotlin metadata and from toString */
    public final int colorType;

    /* renamed from: b, reason: collision with root package name and from kotlin metadata and from toString */
    public final int modeCode;

    /* renamed from: c, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final List<LightGear> gears;

    /* compiled from: LightMode.kt */
    @Metadata(d1 = {"\u0000:\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\bÇ\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0003\u0010\u0004J\u001d\u0010\t\u001a\u00020\b2\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u0002¢\u0006\u0004\b\t\u0010\nJ\u0015\u0010\r\u001a\u00020\u00022\u0006\u0010\f\u001a\u00020\u000b¢\u0006\u0004\b\r\u0010\u000eJ\u0017\u0010\u0011\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00100\u000f¢\u0006\u0004\b\u0011\u0010\u0012R\u0017\u0010\u0014\u001a\u00020\u00138\u0006¢\u0006\f\n\u0004\b\u0014\u0010\u0015\u001a\u0004\b\u0016\u0010\u0017¨\u0006\u0018"}, d2 = {"com/remotekmp/lightEffect/domain/entities/LightMode.$serializer", "Lew/n0;", "Lhd/f;", "<init>", "()V", "Ldw/f;", "encoder", "value", "", ProgramPattern.writePatternChar100, "(Ldw/f;Lhd/f;)V", "Ldw/e;", "decoder", "e", "(Ldw/e;)Lhd/f;", "", "Law/c;", "d", "()[Law/c;", "Lcw/f;", "descriptor", "Lcw/f;", "getDescriptor", "()Lcw/f;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    @Deprecated(level = DeprecationLevel.HIDDEN, message = "This synthesized declaration should not be used directly")
    /* renamed from: hd.f$a */
    public /* synthetic */ class a implements n0<LightMode> {

        /* renamed from: a, reason: collision with root package name */
        @NotNull
        public static final a f31340a;

        @NotNull
        private static final cw.f descriptor;

        static {
            a aVar = new a();
            f31340a = aVar;
            h2 h2Var = new h2("com.remotekmp.lightEffect.domain.entities.LightMode", aVar, 3);
            h2Var.o("colorType", false);
            h2Var.o("modeCode", false);
            h2Var.o("gears", false);
            descriptor = h2Var;
        }

        @Override // ew.n0
        @NotNull
        public aw.c<?>[] b() {
            return n0.a.a(this);
        }

        @Override // ew.n0
        @NotNull
        public final aw.c<?>[] d() {
            aw.c<?> cVar = LightMode.f31336d[2];
            w0 w0Var = w0.f30382a;
            return new aw.c[]{w0Var, w0Var, cVar};
        }

        @Override // aw.b
        @NotNull
        /* renamed from: e, reason: merged with bridge method [inline-methods] */
        public final LightMode a(@NotNull dw.e decoder) {
            int i10;
            int i11;
            int i12;
            List list;
            Intrinsics.checkNotNullParameter(decoder, "decoder");
            cw.f fVar = descriptor;
            dw.c b10 = decoder.b(fVar);
            aw.c[] cVarArr = LightMode.f31336d;
            if (b10.o()) {
                int f10 = b10.f(fVar, 0);
                int f11 = b10.f(fVar, 1);
                list = (List) b10.H(fVar, 2, cVarArr[2], null);
                i10 = f10;
                i12 = f11;
                i11 = 7;
            } else {
                List list2 = null;
                int i13 = 0;
                int i14 = 0;
                int i15 = 0;
                boolean z10 = true;
                while (z10) {
                    int h10 = b10.h(fVar);
                    if (h10 == -1) {
                        z10 = false;
                    } else if (h10 == 0) {
                        i13 = b10.f(fVar, 0);
                        i14 |= 1;
                    } else if (h10 == 1) {
                        i15 = b10.f(fVar, 1);
                        i14 |= 2;
                    } else {
                        if (h10 != 2) {
                            throw new UnknownFieldException(h10);
                        }
                        list2 = (List) b10.H(fVar, 2, cVarArr[2], list2);
                        i14 |= 4;
                    }
                }
                i10 = i13;
                i11 = i14;
                i12 = i15;
                list = list2;
            }
            b10.c(fVar);
            return new LightMode(i11, i10, i12, list, null);
        }

        @Override // aw.o
        /* renamed from: f, reason: merged with bridge method [inline-methods] */
        public final void c(@NotNull dw.f encoder, @NotNull LightMode value) {
            Intrinsics.checkNotNullParameter(encoder, "encoder");
            Intrinsics.checkNotNullParameter(value, "value");
            cw.f fVar = descriptor;
            dw.d b10 = encoder.b(fVar);
            LightMode.e(value, b10, fVar);
            b10.c(fVar);
        }

        @Override // aw.c, aw.o, aw.b
        @NotNull
        public final cw.f getDescriptor() {
            return descriptor;
        }
    }

    /* compiled from: LightMode.kt */
    @Metadata(d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J\u0013\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004¢\u0006\u0004\b\u0006\u0010\u0007¨\u0006\b"}, d2 = {"Lhd/f$b;", "", "<init>", "()V", "Law/c;", "Lhd/f;", "serializer", "()Law/c;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    /* renamed from: hd.f$b, reason: from kotlin metadata */
    public static final class Companion {
        public Companion() {
        }

        @NotNull
        public final aw.c<LightMode> serializer() {
            return a.f31340a;
        }

        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }
    }

    public /* synthetic */ LightMode(int i10, int i11, int i12, List list, r2 r2Var) {
        if (7 != (i10 & 7)) {
            c2.b(i10, 7, a.f31340a.getDescriptor());
        }
        this.colorType = i11;
        this.modeCode = i12;
        this.gears = list;
    }

    @JvmStatic
    public static final /* synthetic */ void e(LightMode self, dw.d output, cw.f serialDesc) {
        aw.c<Object>[] cVarArr = f31336d;
        output.z(serialDesc, 0, self.colorType);
        output.z(serialDesc, 1, self.modeCode);
        output.r(serialDesc, 2, cVarArr[2], self.gears);
    }

    /* renamed from: b, reason: from getter */
    public final int getColorType() {
        return this.colorType;
    }

    @NotNull
    public final List<LightGear> c() {
        return this.gears;
    }

    /* renamed from: d, reason: from getter */
    public final int getModeCode() {
        return this.modeCode;
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof LightMode)) {
            return false;
        }
        LightMode lightMode = (LightMode) other;
        return this.colorType == lightMode.colorType && this.modeCode == lightMode.modeCode && Intrinsics.areEqual(this.gears, lightMode.gears);
    }

    public int hashCode() {
        return (((this.colorType * 31) + this.modeCode) * 31) + this.gears.hashCode();
    }

    @NotNull
    public String toString() {
        return "LightMode(colorType=" + this.colorType + ", modeCode=" + this.modeCode + ", gears=" + this.gears + ')';
    }

    public LightMode(int i10, int i11, @NotNull List<LightGear> gears) {
        Intrinsics.checkNotNullParameter(gears, "gears");
        this.colorType = i10;
        this.modeCode = i11;
        this.gears = gears;
    }
}
