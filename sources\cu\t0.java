package cu;

import androidx.webkit.ProxyConfig;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import kotlin.Metadata;
import kotlin.collections.CollectionsKt;
import kotlin.collections.MapsKt;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.SourceDebugExtension;
import kotlin.ranges.RangesKt;
import org.java_websocket.WebSocketImpl;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* compiled from: URLProtocol.kt */
@Metadata(d1 = {"\u0000\u001e\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\b\n\u0002\b\b\n\u0002\u0010\u000b\n\u0002\b\t\b\u0086\b\u0018\u0000 \u00152\u00020\u0001:\u0001\u0010B\u0017\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004¢\u0006\u0004\b\u0006\u0010\u0007J\u0010\u0010\b\u001a\u00020\u0002HÖ\u0001¢\u0006\u0004\b\b\u0010\tJ\u0010\u0010\n\u001a\u00020\u0004HÖ\u0001¢\u0006\u0004\b\n\u0010\u000bJ\u001a\u0010\u000e\u001a\u00020\r2\b\u0010\f\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\u000e\u0010\u000fR\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0010\u0010\u0011\u001a\u0004\b\u0012\u0010\tR\u0017\u0010\u0005\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b\u0013\u0010\u0014\u001a\u0004\b\u0015\u0010\u000b¨\u0006\u0016"}, d2 = {"Lcu/t0;", "", "", "name", "", "defaultPort", "<init>", "(Ljava/lang/String;I)V", "toString", "()Ljava/lang/String;", "hashCode", "()I", "other", "", "equals", "(Ljava/lang/Object;)Z", "a", "Ljava/lang/String;", "d", r0.b.f37717b, "I", "c", "ktor-http"}, k = 1, mv = {2, 0, 0}, xi = 48)
@SourceDebugExtension({"SMAP\nURLProtocol.kt\nKotlin\n*S Kotlin\n*F\n+ 1 URLProtocol.kt\nio/ktor/http/URLProtocol\n+ 2 _Strings.kt\nkotlin/text/StringsKt___StringsKt\n+ 3 fake.kt\nkotlin/jvm/internal/FakeKt\n+ 4 _Collections.kt\nkotlin/collections/CollectionsKt___CollectionsKt\n*L\n1#1,69:1\n1069#2,2:70\n1#3:72\n1202#4,2:73\n1230#4,4:75\n*S KotlinDebug\n*F\n+ 1 URLProtocol.kt\nio/ktor/http/URLProtocol\n*L\n16#1:70,2\n49#1:73,2\n49#1:75,4\n*E\n"})
/* renamed from: cu.t0, reason: from toString */
/* loaded from: classes5.dex */
public final /* data */ class URLProtocol {

    /* renamed from: c, reason: collision with root package name and from kotlin metadata */
    @NotNull
    public static final Companion INSTANCE = new Companion(null);

    /* renamed from: d, reason: collision with root package name */
    @NotNull
    public static final URLProtocol f29415d;

    /* renamed from: e, reason: collision with root package name */
    @NotNull
    public static final URLProtocol f29416e;

    /* renamed from: f, reason: collision with root package name */
    @NotNull
    public static final URLProtocol f29417f;

    /* renamed from: g, reason: collision with root package name */
    @NotNull
    public static final URLProtocol f29418g;

    /* renamed from: h, reason: collision with root package name */
    @NotNull
    public static final URLProtocol f29419h;

    /* renamed from: i, reason: collision with root package name */
    @NotNull
    public static final Map<String, URLProtocol> f29420i;

    /* renamed from: a, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final String name;

    /* renamed from: b, reason: collision with root package name and from kotlin metadata and from toString */
    public final int defaultPort;

    /* compiled from: URLProtocol.kt */
    @Metadata(d1 = {"\u0000\"\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010$\n\u0002\b\u0005\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J\u0015\u0010\u0007\u001a\u00020\u00062\u0006\u0010\u0005\u001a\u00020\u0004¢\u0006\u0004\b\u0007\u0010\bR\u0017\u0010\t\u001a\u00020\u00068\u0006¢\u0006\f\n\u0004\b\t\u0010\n\u001a\u0004\b\u000b\u0010\fR#\u0010\u000e\u001a\u000e\u0012\u0004\u0012\u00020\u0004\u0012\u0004\u0012\u00020\u00060\r8\u0006¢\u0006\f\n\u0004\b\u000e\u0010\u000f\u001a\u0004\b\u0010\u0010\u0011¨\u0006\u0012"}, d2 = {"Lcu/t0$a;", "", "<init>", "()V", "", "name", "Lcu/t0;", "a", "(Ljava/lang/String;)Lcu/t0;", "HTTP", "Lcu/t0;", "c", "()Lcu/t0;", "", "byName", "Ljava/util/Map;", r0.b.f37717b, "()Ljava/util/Map;", "ktor-http"}, k = 1, mv = {2, 0, 0}, xi = 48)
    /* renamed from: cu.t0$a, reason: from kotlin metadata */
    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        @NotNull
        public final URLProtocol a(@NotNull String name) {
            Intrinsics.checkNotNullParameter(name, "name");
            String c10 = eu.e0.c(name);
            URLProtocol uRLProtocol = URLProtocol.INSTANCE.b().get(c10);
            return uRLProtocol == null ? new URLProtocol(c10, 0) : uRLProtocol;
        }

        @NotNull
        public final Map<String, URLProtocol> b() {
            return URLProtocol.f29420i;
        }

        @NotNull
        public final URLProtocol c() {
            return URLProtocol.f29415d;
        }

        public Companion() {
        }
    }

    static {
        URLProtocol uRLProtocol = new URLProtocol(ProxyConfig.MATCH_HTTP, 80);
        f29415d = uRLProtocol;
        URLProtocol uRLProtocol2 = new URLProtocol(ProxyConfig.MATCH_HTTPS, WebSocketImpl.DEFAULT_WSS_PORT);
        f29416e = uRLProtocol2;
        URLProtocol uRLProtocol3 = new URLProtocol("ws", 80);
        f29417f = uRLProtocol3;
        URLProtocol uRLProtocol4 = new URLProtocol("wss", WebSocketImpl.DEFAULT_WSS_PORT);
        f29418g = uRLProtocol4;
        URLProtocol uRLProtocol5 = new URLProtocol("socks", 1080);
        f29419h = uRLProtocol5;
        List listOf = CollectionsKt.listOf((Object[]) new URLProtocol[]{uRLProtocol, uRLProtocol2, uRLProtocol3, uRLProtocol4, uRLProtocol5});
        LinkedHashMap linkedHashMap = new LinkedHashMap(RangesKt.coerceAtLeast(MapsKt.mapCapacity(CollectionsKt.collectionSizeOrDefault(listOf, 10)), 16));
        for (Object obj : listOf) {
            linkedHashMap.put(((URLProtocol) obj).name, obj);
        }
        f29420i = linkedHashMap;
    }

    public URLProtocol(@NotNull String name, int i10) {
        Intrinsics.checkNotNullParameter(name, "name");
        this.name = name;
        this.defaultPort = i10;
        for (int i11 = 0; i11 < name.length(); i11++) {
            if (!eu.l.a(name.charAt(i11))) {
                throw new IllegalArgumentException("All characters should be lower case");
            }
        }
    }

    /* renamed from: c, reason: from getter */
    public final int getDefaultPort() {
        return this.defaultPort;
    }

    @NotNull
    /* renamed from: d, reason: from getter */
    public final String getName() {
        return this.name;
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof URLProtocol)) {
            return false;
        }
        URLProtocol uRLProtocol = (URLProtocol) other;
        return Intrinsics.areEqual(this.name, uRLProtocol.name) && this.defaultPort == uRLProtocol.defaultPort;
    }

    public int hashCode() {
        return (this.name.hashCode() * 31) + this.defaultPort;
    }

    @NotNull
    public String toString() {
        return "URLProtocol(name=" + this.name + ", defaultPort=" + this.defaultPort + ')';
    }
}
