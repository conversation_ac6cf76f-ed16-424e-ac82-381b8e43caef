package ae;

import com.google.android.exoplayer2.text.ttml.TtmlNode;
import com.wear.bean.ProgramPattern;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jivesoftware.smackx.xhtmlim.XHTMLText;

/* compiled from: PatternNoticeEntity.kt */
@Metadata(d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0010\u000b\n\u0002\b\b\n\u0002\u0010\b\n\u0002\b\u0016\b\u0086\b\u0018\u00002\u00020\u0001Bs\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\b\u0010\u0004\u001a\u0004\u0018\u00010\u0002\u0012\b\u0010\u0005\u001a\u0004\u0018\u00010\u0002\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\u0006\u0012\b\u0010\b\u001a\u0004\u0018\u00010\u0002\u0012\b\u0010\t\u001a\u0004\u0018\u00010\u0002\u0012\b\u0010\n\u001a\u0004\u0018\u00010\u0002\u0012\b\u0010\f\u001a\u0004\u0018\u00010\u000b\u0012\b\u0010\r\u001a\u0004\u0018\u00010\u000b\u0012\b\u0010\u000e\u001a\u0004\u0018\u00010\u000b\u0012\b\u0010\u000f\u001a\u0004\u0018\u00010\u0002¢\u0006\u0004\b\u0010\u0010\u0011J\u0010\u0010\u0012\u001a\u00020\u0002HÖ\u0001¢\u0006\u0004\b\u0012\u0010\u0013J\u0010\u0010\u0015\u001a\u00020\u0014HÖ\u0001¢\u0006\u0004\b\u0015\u0010\u0016J\u001a\u0010\u0018\u001a\u00020\u000b2\b\u0010\u0017\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\u0018\u0010\u0019R\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u001a\u0010\u001b\u001a\u0004\b\u001c\u0010\u0013R\u0019\u0010\u0004\u001a\u0004\u0018\u00010\u00028\u0006¢\u0006\f\n\u0004\b\u001c\u0010\u001b\u001a\u0004\b\u001d\u0010\u0013R\u0019\u0010\u0005\u001a\u0004\u0018\u00010\u00028\u0006¢\u0006\f\n\u0004\b\u001e\u0010\u001b\u001a\u0004\b\u001a\u0010\u0013R\u0019\u0010\u0007\u001a\u0004\u0018\u00010\u00068\u0006¢\u0006\f\n\u0004\b\u001d\u0010\u001f\u001a\u0004\b\u001e\u0010 R\u0019\u0010\b\u001a\u0004\u0018\u00010\u00028\u0006¢\u0006\f\n\u0004\b!\u0010\u001b\u001a\u0004\b\"\u0010\u0013R\u0019\u0010\t\u001a\u0004\u0018\u00010\u00028\u0006¢\u0006\f\n\u0004\b#\u0010\u001b\u001a\u0004\b$\u0010\u0013R\u0019\u0010\n\u001a\u0004\u0018\u00010\u00028\u0006¢\u0006\f\n\u0004\b%\u0010\u001b\u001a\u0004\b!\u0010\u0013R\u0019\u0010\f\u001a\u0004\u0018\u00010\u000b8\u0006¢\u0006\f\n\u0004\b&\u0010'\u001a\u0004\b#\u0010(R\u0019\u0010\r\u001a\u0004\u0018\u00010\u000b8\u0006¢\u0006\f\n\u0004\b)\u0010'\u001a\u0004\b)\u0010(R\u0019\u0010\u000e\u001a\u0004\u0018\u00010\u000b8\u0006¢\u0006\f\n\u0004\b$\u0010'\u001a\u0004\b%\u0010(R\u0019\u0010\u000f\u001a\u0004\u0018\u00010\u00028\u0006¢\u0006\f\n\u0004\b\"\u0010\u001b\u001a\u0004\b&\u0010\u0013¨\u0006*"}, d2 = {"Lae/f;", "", "", TtmlNode.ATTR_ID, "noticeType", "contentBody", "", "noticeTime", "userName", "userAvatar", "patternUserId", "", "privateUser", "sensitiveAvatar", "readStatus", "remoteAccountId", "<init>", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Long;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/Boolean;Ljava/lang/String;)V", "toString", "()Ljava/lang/String;", "", "hashCode", "()I", "other", "equals", "(Ljava/lang/Object;)Z", "a", "Ljava/lang/String;", r0.b.f37717b, "d", "c", "Ljava/lang/Long;", "()Ljava/lang/Long;", "e", jl.k.f32572f, ProgramPattern.writePatternChar100, "j", "g", XHTMLText.H, "Ljava/lang/Boolean;", "()Ljava/lang/Boolean;", jl.i.f32548i, "sqliteDelight"}, k = 1, mv = {2, 0, 0}, xi = 48)
/* renamed from: ae.f, reason: from toString */
/* loaded from: classes4.dex */
public final /* data */ class PatternNoticeEntity {

    /* renamed from: a, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final String id;

    /* renamed from: b, reason: collision with root package name and from kotlin metadata and from toString */
    @Nullable
    public final String noticeType;

    /* renamed from: c, reason: collision with root package name and from kotlin metadata and from toString */
    @Nullable
    public final String contentBody;

    /* renamed from: d, reason: collision with root package name and from kotlin metadata and from toString */
    @Nullable
    public final Long noticeTime;

    /* renamed from: e, reason: collision with root package name and from kotlin metadata and from toString */
    @Nullable
    public final String userName;

    /* renamed from: f, reason: collision with root package name and from kotlin metadata and from toString */
    @Nullable
    public final String userAvatar;

    /* renamed from: g, reason: collision with root package name and from kotlin metadata and from toString */
    @Nullable
    public final String patternUserId;

    /* renamed from: h, reason: collision with root package name and from kotlin metadata and from toString */
    @Nullable
    public final Boolean privateUser;

    /* renamed from: i, reason: collision with root package name and from kotlin metadata and from toString */
    @Nullable
    public final Boolean sensitiveAvatar;

    /* renamed from: j, reason: collision with root package name and from kotlin metadata and from toString */
    @Nullable
    public final Boolean readStatus;

    /* renamed from: k, reason: collision with root package name and from kotlin metadata and from toString */
    @Nullable
    public final String remoteAccountId;

    public PatternNoticeEntity(@NotNull String id2, @Nullable String str, @Nullable String str2, @Nullable Long l10, @Nullable String str3, @Nullable String str4, @Nullable String str5, @Nullable Boolean bool, @Nullable Boolean bool2, @Nullable Boolean bool3, @Nullable String str6) {
        Intrinsics.checkNotNullParameter(id2, "id");
        this.id = id2;
        this.noticeType = str;
        this.contentBody = str2;
        this.noticeTime = l10;
        this.userName = str3;
        this.userAvatar = str4;
        this.patternUserId = str5;
        this.privateUser = bool;
        this.sensitiveAvatar = bool2;
        this.readStatus = bool3;
        this.remoteAccountId = str6;
    }

    @Nullable
    /* renamed from: a, reason: from getter */
    public final String getContentBody() {
        return this.contentBody;
    }

    @NotNull
    /* renamed from: b, reason: from getter */
    public final String getId() {
        return this.id;
    }

    @Nullable
    /* renamed from: c, reason: from getter */
    public final Long getNoticeTime() {
        return this.noticeTime;
    }

    @Nullable
    /* renamed from: d, reason: from getter */
    public final String getNoticeType() {
        return this.noticeType;
    }

    @Nullable
    /* renamed from: e, reason: from getter */
    public final String getPatternUserId() {
        return this.patternUserId;
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof PatternNoticeEntity)) {
            return false;
        }
        PatternNoticeEntity patternNoticeEntity = (PatternNoticeEntity) other;
        return Intrinsics.areEqual(this.id, patternNoticeEntity.id) && Intrinsics.areEqual(this.noticeType, patternNoticeEntity.noticeType) && Intrinsics.areEqual(this.contentBody, patternNoticeEntity.contentBody) && Intrinsics.areEqual(this.noticeTime, patternNoticeEntity.noticeTime) && Intrinsics.areEqual(this.userName, patternNoticeEntity.userName) && Intrinsics.areEqual(this.userAvatar, patternNoticeEntity.userAvatar) && Intrinsics.areEqual(this.patternUserId, patternNoticeEntity.patternUserId) && Intrinsics.areEqual(this.privateUser, patternNoticeEntity.privateUser) && Intrinsics.areEqual(this.sensitiveAvatar, patternNoticeEntity.sensitiveAvatar) && Intrinsics.areEqual(this.readStatus, patternNoticeEntity.readStatus) && Intrinsics.areEqual(this.remoteAccountId, patternNoticeEntity.remoteAccountId);
    }

    @Nullable
    /* renamed from: f, reason: from getter */
    public final Boolean getPrivateUser() {
        return this.privateUser;
    }

    @Nullable
    /* renamed from: g, reason: from getter */
    public final Boolean getReadStatus() {
        return this.readStatus;
    }

    @Nullable
    /* renamed from: h, reason: from getter */
    public final String getRemoteAccountId() {
        return this.remoteAccountId;
    }

    public int hashCode() {
        int hashCode = this.id.hashCode() * 31;
        String str = this.noticeType;
        int hashCode2 = (hashCode + (str == null ? 0 : str.hashCode())) * 31;
        String str2 = this.contentBody;
        int hashCode3 = (hashCode2 + (str2 == null ? 0 : str2.hashCode())) * 31;
        Long l10 = this.noticeTime;
        int hashCode4 = (hashCode3 + (l10 == null ? 0 : l10.hashCode())) * 31;
        String str3 = this.userName;
        int hashCode5 = (hashCode4 + (str3 == null ? 0 : str3.hashCode())) * 31;
        String str4 = this.userAvatar;
        int hashCode6 = (hashCode5 + (str4 == null ? 0 : str4.hashCode())) * 31;
        String str5 = this.patternUserId;
        int hashCode7 = (hashCode6 + (str5 == null ? 0 : str5.hashCode())) * 31;
        Boolean bool = this.privateUser;
        int hashCode8 = (hashCode7 + (bool == null ? 0 : bool.hashCode())) * 31;
        Boolean bool2 = this.sensitiveAvatar;
        int hashCode9 = (hashCode8 + (bool2 == null ? 0 : bool2.hashCode())) * 31;
        Boolean bool3 = this.readStatus;
        int hashCode10 = (hashCode9 + (bool3 == null ? 0 : bool3.hashCode())) * 31;
        String str6 = this.remoteAccountId;
        return hashCode10 + (str6 != null ? str6.hashCode() : 0);
    }

    @Nullable
    /* renamed from: i, reason: from getter */
    public final Boolean getSensitiveAvatar() {
        return this.sensitiveAvatar;
    }

    @Nullable
    /* renamed from: j, reason: from getter */
    public final String getUserAvatar() {
        return this.userAvatar;
    }

    @Nullable
    /* renamed from: k, reason: from getter */
    public final String getUserName() {
        return this.userName;
    }

    @NotNull
    public String toString() {
        return "PatternNoticeEntity(id=" + this.id + ", noticeType=" + this.noticeType + ", contentBody=" + this.contentBody + ", noticeTime=" + this.noticeTime + ", userName=" + this.userName + ", userAvatar=" + this.userAvatar + ", patternUserId=" + this.patternUserId + ", privateUser=" + this.privateUser + ", sensitiveAvatar=" + this.sensitiveAvatar + ", readStatus=" + this.readStatus + ", remoteAccountId=" + this.remoteAccountId + ')';
    }
}
