<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="privacy_policy">Privacy Policy</string>
    <string name="terms_and_conditions">Terms &amp; Conditions</string>
    <string name="account_clipimage">ClipImage</string>
    <string name="account_license_privacy">App data is used to help us improve the way our products
        work. This data is in anonymous form and is not connected with individual app users. You can
        learn more in our &lt;a href=\"/app/privacy-policy\"&gt;privacy policy&lt;/a&gt; and choose
        whether data from your app is collected for these purposes below.
    </string>
    <string name="account_logout">Log out</string>
    <string name="account_nickname_change">Edit Username</string>
    <string name="account_password_change_confirm_hint">Please confirm the new password</string>
    <string name="account_password_change_new">New</string>
    <string name="account_password_change_old">Old</string>
    <string name="account_password_change_old_hint">Please enter the old password</string>
    <string name="account_password_empty">Please enter your password</string>
    <string name="account_password_notEq">The passwords you\'ve entered don\'t match</string>
    <string name="account_privacy_accept_logs">Help us create better products by sharing anonymous
        data about our app.
    </string>
    <string name="account_privacy_anonymous">Anonymous Data</string>
    <string name="account_toy">My Toys</string>
    <string name="account_username_empty">Please enter username</string>
    <string name="activity_forward">Choose chat</string>
    <string name="activity_mute_message_notice">[%1$s message] %2$s</string>
    <string name="activity_mute_messages_notice">[%1$s messages] %2$s</string>
    <string name="activity_unread_message_notice">%1$s new messages</string>
    <string name="add_favorite_faile_availmemory">Not enough space.</string>
    <string name="add_favorite_faile_maxcount">GIF limit reached.</string>
    <string name="add_favorite_faile_maxzie">GIF too big. Max size 1MB</string>
    <string name="send_gif_faile_maxzie">GIF too big. Max size 1MB</string>
    <string name="add_friend_pop">Add People</string>
    <string name="add_friend_tip1">Please make sure you and your partner are both using the same app
        and have registered for an account.
    </string>
    <string name="add_friend_tip2">If you or your partner cannot see the request, try again.
    </string>
    <string name="add_friend_tip3">If the error persists, both sides should restart the app.
    </string>
    <string name="add_friend_tip4">You are not able to search people by email anymore. Please search
        by username. You can find/change your username in the Account tab.
    </string>
    <string name="add_friend_user_check">Usernames can only contain letters, numbers, underscores
        and dashes.
    </string>
    <string name="add_friend_user_exist">Failed to add - you are already friends with this user.
    </string>
    <string name="alarm_audio_file_error">Please choose alarm type.</string>
    <string name="alarm_audio_recording">Record</string>
    <string name="alarm_audio_recording_over">Maximum time 60</string>
    <string name="alarm_audio_recording_over_notice">The maximum time is 60 seconds.</string>
    <string name="alarm_defind_record_audio">Sound</string>
    <string name="alarm_defind_repeat_None">None</string>
    <string name="alarm_defind_repeat_notice">Never</string>
    <string name="alarm_defind_times_notice">The alarm time must be later than the current time
    </string>
    <string name="alarm_delete_notices">Remove this alarm?</string>
    <string name="alarm_list_create">New alarm</string>
    <string name="alarm_list_create_my_time">My Time</string>
    <string name="alarm_list_create_notification">Alarm Type</string>
    <string name="alarm_list_create_partner_time">Partner Time</string>
    <string name="alarm_list_create_repeat">Repeat</string>
    <string name="alarm_list_gtm">(GMT%1$s)</string>
    <string name="alarm_list_repeat_Fri">Fri</string>
    <string name="alarm_list_repeat_Sat">Sat</string>
    <string name="alarm_list_repeat_Sun">Sun</string>
    <string name="alarm_list_repeat_Thur">Thu</string>
    <string name="alarm_list_repeat_Tue">Tue</string>
    <string name="alarm_list_repeat_Wed">Wed</string>
    <string name="alarm_list_repeat_mon">Mon</string>
    <string name="alarm_name">Alarm name</string>
    <string name="alarm_new_pattern">New Pattern</string>
    <string name="alarm_notice_dialog_title">Alarm Notice</string>
    <string name="alarm_notice_state_Missed">Missed alarm</string>
    <string name="alarm_notice_state_play">Played</string>
    <string name="alarm_notice_state_received">Received</string>
    <string name="alarm_notice_title">Remote Alarm</string>
    <string name="alarm_ring_notice">Alarm from %1$s</string>
    <string name="alarm_status_accept">You\'ve accepted the alarm</string>
    <string name="alarm_status_automatic">Auto-save is enabled</string>
    <string name="alarm_status_expired">This alarm has expired</string>
    <string name="alarm_status_rejected">You\'ve rejected the alarm</string>
    <string name="album_pictures">picture</string>
    <string name="alerm_setting_autoplay_notice_receive">You have received an alarm from %1$s, but
        you set the alarm to not play automatically and you can not play the alarm. You can turn on
        the auto-play alarm function for %2$s in the setting page
    </string>
    <string name="alerm_setting_autoplay_notice_sender">The other party missed your alarm clock
    </string>
    <string name="app_company_name">Lovense</string>
    <string name="app_hourformat_12_am">AM</string>
    <string name="app_hourformat_12_pm">PM</string>
    <string name="app_open_bluetooth_permission">\"Remote\" doesn\'t have permission to use
        Bluetooth. You can change the app permissions in your phone’s security permissions.
    </string>
    <string name="app_open_camera_permission">\"Remote\" doesn\'t have permission to use the camera.
        You can change the app permissions in your phone’s security permissions.
    </string>
    <string name="app_open_must_permission">\"Remote\" doesn\'t have permission. You can change the
        app permissions in your phone’s security permissions.
    </string>
    <string name="app_request_config_error">Internet error. [%1$s]</string>
    <string name="common_internet_error">Internet error.</string>
    <string name="app_trun_on_bluetooth_gps">If you\'re using Android 6.0+, your GPS must be enabled
        to connect to Bluetooth devices.
    </string>
    <string name="app_trun_on_gps_notice">Please turn on your phone\'s GPS.</string>
    <string name="authorized_spotify_action">Link My Account</string>
    <string name="authorized_spotify_notice">Authorize us to log into your Spotify account and
        retrieve your music
    </string>
    <string name="black_contacts_no_block_friends">You have no blocked friends</string>
    <string name="black_contacts_no_rejected_request">You have no rejected request.</string>
    <string name="black_contacts_select">Select Contacts</string>
    <string name="black_contacts_select_unblock">Unblock</string>
    <string name="ble_not_support">Your device does not support Bluetooth 4.0</string>
    <string name="block_frends_message_tip">The %1$s was successfully sent but rejected by the
        receiver.
    </string>
    <string name="chat_alarm_execute_no_toy">You\'ve missed the alarm because you had no toy
        connected.
    </string>
    <string name="chat_alarm_execute_success">The alarm set for %1$s went off successfuly at
        (%2$s)
    </string>
    <string name="chat_alarm_info_name">Name:</string>
    <string name="chat_alarm_info_repeat">Repeat:</string>
    <string name="chat_alarm_info_time">Time:</string>
    <string name="chat_alarm_info_title">Alarm Info</string>
    <string name="chat_alarm_info_type">Type:</string>
    <string name="chat_alarm_message">Message</string>
    <string name="chat_alarm_miss">Send personal message to partners when they miss an alarm
    </string>
    <string name="chat_alarm_missed_local">%1$s missed the alarm set for %2$s</string>
    <string name="chat_alarm_missed_receive">You\'ve missed %1$s\'s alarm set for %2$s</string>
    <string name="chat_alarm_notifyme">Notify me (within chat) when my partner misses an alarm
    </string>
    <string name="chat_alarm_offline">%1$s will receive the alarm after logging in.</string>
    <string name="chat_alarm_receive">You\'ve received %1$s\'s alarm set for %2$s</string>
    <string name="chat_alarm_receive_notice">%1$s just received the alarm set for %2$s</string>
    <string name="chat_alart_miss_notice">You missed an alarm from Lovense at %1$s. Please make sure
        your toy(s) is connected and turn on the \"auto-play\" settings by tapping the top right.
    </string>
    <string name="chat_alart_notify_notice">Your partner didn\'t receive your %1$s alarm</string>
    <string name="chat_control_ldr_ways">Interactive</string>
    <string name="chat_control_tochange_ldr">Long Distance Sex</string>
    <string name="chat_control_tochange_remote">Remote Control</string>
    <string name="chat_dialog">Playback</string>
    <string name="chat_favorite_meun_add">Add to favorites</string>
    <string name="chat_friend_accept">%1$s has accepted your request</string>
    <string name="chat_friend_noToy">Your partner has no toy connected</string>
    <string name="chat_friend_reject">%1$s has rejected the request.</string>
    <string name="chat_liveControl">Live</string>
    <string name="chat_login">You need to re-login to use these features.</string>
    <string name="chat_message_empty">Please enter your message</string>
    <string name="chat_message_item_action_copy">Copy</string>
    <string name="chat_message_item_action_delete">Delete</string>
    <string name="chat_message_item_action_save_phone">Save</string>
    <string name="chat_message_item_copy_notice">Copy successfully</string>
    <string name="chat_message_item_save_error">File error</string>
    <string name="chat_message_item_save_path">Save to %1$s</string>
    <string name="chat_mic">Hold to talk</string>
    <string name="chat_mic_slide">Slide up to cancel.</string>
    <string name="chat_pattern_by_create_default_name">Pattern</string>
    <string name="chat_pattern_command_send_by_create">Send this pattern</string>
    <string name="chat_pattern_resend">Pattern was sent</string>
    <string name="chat_pattern_resend_auto_play">Always play patterns automatically for this user
    </string>
    <string name="chat_pattern_resend_dont_play">Do not play</string>
    <string name="chat_pattern_resend_notices">Do you want to resend this pattern?</string>
    <string name="chat_pattern_resend_play_title">sent you a pattern</string>
    <string name="chat_pattern_sync_notice">You partner chose to automatically play your patterns
    </string>
    <string name="chat_pattern_sync_own_notice">You chose to automatically play this person\'s
        patterns.You can tap the setting icon in the top right to change this setting.
    </string>
    <string name="chat_pattern_timeShort">Pattern is too short</string>
    <string name="chat_pic_album">Choose a photo</string>
    <string name="chat_recall_fail">Failed to recall the massage. Check network.</string>
    <string name="chat_recall_morethen_2min">You cannot recall a message sent more than two minutes
        ago.
    </string>
    <string name="chat_record_failure">Failed to record voice message</string>
    <string name="chat_save_pattern_failed">Save failed.</string>
    <string name="chat_sendPattern">Patterns</string>
    <string name="chat_sendPic">Photo</string>
    <string name="chat_sync">Sync</string>
    <string name="chat_sync_recall_owner">You have recalled a message</string>
    <string name="chat_sync_recall_pattern">%1$s recalled a message</string>
    <string name="chat_toy_reconnecting">( &lt;font color=\'#FF2D89\'&gt;Reconnecting &lt;/font&gt;
        )
    </string>
    <string name="chat_video">Video</string>
    <string name="chat_video_busy">Line busy, please try later!</string>
    <string name="chat_video_cancel">%1$s cancelled the request.</string>
    <string name="chat_video_connecting">connecting....</string>
    <string name="chat_video_finish">%1$s ended the call.</string>
    <string name="chat_voice">Voice</string>
    <string name="chat_voice_timeShort">Voice message is too short</string>
    <string name="chat_waitAcceptance">Waiting for %1$s to accept your request.</string>
    <!--<string name="chat_wait_video_accept">Waiting for approval...</string>-->
    <string name="chat_waiting_accep_tance_auto">Establishing connection</string>
    <string name="choose_toys_title">Choose Toy(s)</string>
    <string name="clear_chat_message">Clear chat history</string>
    <string name="voice_message_expired">This voice message has expired.</string>
    <string name="closeRange_music">Music</string>
    <string name="closeRange_patterns">My Patterns</string>
    <string name="closeRange_remoteControl">Remote</string>
    <string name="closeRange_sound">Sound</string>
    <string name="comman_forward">Forward</string>
    <string name="comman_hide">Hide</string>
    <string name="comman_max_characters_20">Max 20 characters</string>
    <string name="comman_max_characters_30">Max 30 characters</string>
    <string name="comman_recall">Recall</string>
    <string name="comman_save_failed">Save failed</string>
    <string name="comman_saved_successfully">Saved successfully</string>
    <string name="comman_unhide">Unhide</string>
    <string name="command_edit">Edit</string>
    <string name="common_about">About</string>
    <string name="common_accept">Accept</string>
    <string name="common_all">All</string>
    <string name="common_anonymous">Anonymous</string>
    <string name="common_back">Back</string>
    <string name="common_beta">Beta</string>
    <string name="common_cancel">Cancel</string>
    <string name="common_chage">Change now</string>
    <string name="common_check_update">Check for Updates</string>
    <string name="common_clean_background">Reset to default</string>
    <string name="common_confirm">Confirm</string>
    <string name="common_decline">Decline</string>
    <string name="common_delete">Delete</string>
    <string name="common_dialog_delete">Are you sure you want to delete?</string>
    <string name="common_disable">Disable</string>
    <string name="common_done">Done</string>
    <string name="common_email">Email</string>
    <string name="common_enable">Enable</string>
    <string name="common_exit">Exit</string>
    <string name="common_female">Female</string>
    <string name="common_gender">Gender</string>
    <string name="common_generate">Generate</string>
    <string name="common_help">Help</string>
    <string name="common_loading">Loading...</string>
    <string name="common_login_first">Please login first</string>
    <string name="common_loop">Loop</string>
    <string name="common_male">Male</string>
    <string name="common_manage">Manage</string>
    <string name="common_netError">Connection Error</string>
    <string name="common_newVersionTip">A new version of Lovense Remote is available, please update
        now.
    </string>
    <string name="common_no">No</string>
    <string name="common_not_pic">This is not the correct file type. Please upload a .jpg, .gif,
        .bmp or .png file.
    </string>
    <string name="common_ok">OK</string>
    <string name="common_other">Other</string>
    <string name="common_password">Password</string>
    <string name="common_pic_camera">Take a photo</string>
    <string name="common_play">Play</string>
    <string name="common_privacy">Privacy</string>
    <string name="common_random">Random</string>
    <string name="common_recent">Recent</string>
    <string name="common_reject">Decline</string>
    <string name="common_rename">Rename</string>
    <string name="common_resend">Resend</string>
    <string name="common_restartApp">Restart App</string>
    <string name="common_save">Save</string>
    <string name="common_saved">Saved</string>
    <string name="common_send">Send</string>
    <string name="common_sensitivity">Sensitivity</string>
    <string name="common_serverError">Unable to connect to the server</string>
    <string name="common_timeout_error">Network issue. Check or switch networks and retry.</string>
    <string name="common_setting">Settings</string>
    <string name="common_settingTip">Not connected to the internet. Please connect to wifi or your
        mobile network.
    </string>
    <string name="common_share">Share</string>
    <string name="common_shared">Shared</string>
    <string name="common_stop">Stop</string>
    <string name="common_submit">Submitting...</string>
    <string name="common_unknown">Unknown</string>
    <string name="common_update">Update</string>
    <string name="common_uploading">Transferring, please wait...</string>
    <string name="common_version">Version</string>
    <string name="common_yes">Yes</string>
    <string name="conflict_notice_live_control">You received a live control request from %1$s
    </string>
    <string name="conflict_notice_sync_control">You received a sync control request from %1$s
    </string>
    <string name="conflict_notice_video_control">You received a call request from %1$s</string>
    <string name="conflict_notice_voice_control">you receive a voice request from %1$s</string>
    <string name="conflict_notice_group_sync_control">You received a sync control from group :%1$s
    </string>
    <string name="conflict_notice_group_ds_control">You received a D&amp;S control from group
        :%1$s
    </string>
    <string name="control_link_nonsupport">Synchronization failed, you need Max, Nora, Calor, or
        XMachine to use this feature
    </string>
    <string name="control_link_startsuccess">Synchronization successful.</string>
    <string name="control_linked_choose_notice_1">Your partner</string>
    <string name="control_linked_choose_notice_2">has</string>
    <string name="control_linked_choose_notice_3">sent a request to sync your toys.</string>
    <string name="control_linked_choose_notice_4">Which toy do you want to sync?</string>
    <string name="control_linked_choose_notice_5">Note: only Max/Nora can be synced.</string>
    <string name="control_linked_choose_notice_time">The first synchronization control toy will be
        automatically selected in %1$s seconds.
    </string>
    <string name="crash_tip">A bug has caused the app to crash. This error was auto-logged and sent
        to our developers so they can find and fix the bug. If this continues, please contact our
        support at www.lovense.com/contact so we can get more info and get it resolved faster.
    </string>
    <string name="create_link_pop">Control link</string>
    <string name="data_distance_just">now</string>
    <string name="data_distance_minute">%1$s min ago</string>
    <string name="data_distance_minutes">%1$s mins ago</string>
    <string name="data_distance_second">%1$s seconds ago</string>
    <string name="delete_music_playlist">Delete %1$s?</string>

    <string name="dfu_file_status_invalid">The upgrade was aborted. Please try again.</string>
    <string name="dfu_status_uploading">The toy is upgrading, do not exit or it will fail. Please
        wait for the upgrade to be complete.
    </string>
    <string name="dfu_status_uploading_title">Uploading…</string>
    <string name="dfu_success_finish">Done</string>
    <string name="dfu_toy_status_invalid">Network connection issue. Unable to download the firmware
        update. Please reconnect.
    </string>
    <string name="dfu_uploading_percentage">%d%%</string>

    <string name="firmwar_toy_low_battery">The toy is low on battery. To make sure the upgrade is
        successful, please charge the toy to at least 50% battery.
    </string>
    <string name="firmwar_phone_low_battery">The phone is low on battery. To make sure the upgrade
        is successful, please charge the phone to at least 40% battery.
    </string>
    <string name="firmwar_toy_and_phone_low_battery" formatted="false">The phone and toy are both on
        low battery. To make sure the upgrade is successful, please charge your toy to 50% and your
        phone to 40%.
    </string>
    <string name="firmwar_upgrading_title_download">Downloading firmware file…</string>
    <string name="firmwar_upgrading_subtitle_download">Prepare to Upgrade. Please do not leave the
        current screen until the firmware update is complete.
    </string>
    <string name="firmwar_upgrading_title_reboot">Rebooting your toy…</string>
    <string name="firmwar_upgrading_subtitle_reboot">Upgrade will start after rebooting. Please keep
        the toy close to your phone.
    </string>
    <string name="firmwar_upgrading_title_start">Upgrading firmware…</string>
    <string name="firmwar_upgrading_subtitle_start">The light will quick-flash during the upgrade.
        Please do not leave the current screen and do not run the app in the background.
    </string>
    <string name="firmwar_upgrading_title_failed">Upgrade failed</string>
    <string name="firmwar_upgrading_subtitle_third_party_error">Please reboot both your toy and
        phone and try again.
    </string>
    <string name="firmwar_upgrading_subtitle_toy_disconnect">Your toy has disconnected. Please
        reboot your toy and keep it close to your phone during the upgrade to ensure a stable
        connection.
    </string>
    <string name="firmwar_upgrading_subtitle_sever_error">Failed to download the firmware package.
        Please try again later
    </string>
    <string name="firmwar_upgrading_subtitle_network_error">Failed to download the firmware package.
        Please check your network and ensure a stable connection during the upgrade.
    </string>
    <string name="firmwar_upgrading_subtitle_no_toy">No toy is detected. Please reboot your toy,
        keep it close to your phone and try again.
    </string>
    <string name="firmwar_upgrading_title_success">Upgrade successful</string>
    <string name="firmwar_upgrading_subtitle_success">Please power on your toy to use the new
        firmware.
    </string>

    <string name="dialog_choose_linked_toy">Choose Your Features:</string>
    <string name="dialog_choose_toy_type_1">1 Vibrator</string>
    <string name="dialog_choose_toy_type_2">2 Vibrators</string>
    <string name="dialog_choose_toy_type_3">Vibration + Rotation</string>
    <string name="dialog_choose_toy_type_4">Vibration + Contractions</string>
    <string name="dialog_receiveRequest_live">Give up control?</string>
    <string name="dialog_receiveRequest_sync">Your partner has requested to sync together...
    </string>
    <string name="dialog_receiveRequest_video">Invites you to a video call.</string>
    <string name="enquires_default_notice">Please review the images I submitted</string>
    <string name="enquires_error_upload_notice">Upload failed. Please try again.</string>
    <string name="enquires_progress_notice">Transferring, please wait...</string>
    <string name="favorite_dialog_delete">Deleted GIFs can\'t be recovered.</string>
    <string name="favorite_emojis_delete_notic">Delete(%1$s)</string>
    <string name="favorite_emojis_move_notic">Sticky</string>
    <string name="favorite_emojis_title">Favorite emojis</string>
    <string name="favorite_emojis_total_notic">%1$s Emojis</string>
    <string name="file_notfound">Could not find the file</string>
    <string name="fingerprint_hint">Touch sensor</string>
    <string name="fingerprint_not_recognized">Fingerprint not recognized. Try again.</string>
    <string name="forward_failed">Forward failed</string>
    <string name="forward_successfully">Forward successfully</string>
    <string name="guide_features_skin">Skip</string>
    <string name="guide_features_title">What\'s new</string>
    <string name="guide_features_welcome">Welcome</string>
    <string name="has_linked_2_create_live_notice">You have an active control link that must be
        ended before using this feature. Would you like to end control link?
    </string>
    <string name="help_faq">FAQ</string>
    <string name="help_faq_not_data">No FAQS</string>
    <string name="help_feedback">Feedback</string>
    <string name="help_feedback_content_error">Content should be between 15-300 characters.</string>
    <string name="help_feedback_notice">Let us know what you think or tell us about a broken
        feature.
    </string>
    <string name="help_feedback_send_success">Your feedback was sent successfully. Thanks.</string>
    <string name="help_guide">User Guide</string>
    <string name="help_submit">Submit</string>
    <string name="input_email_hint">Username or Email</string>
    <string name="link_create_success">Control link copied to clipboard for you to paste it</string>
    <string name="link_delete_notice">Are you sure that you want to end the session?</string>
    <string name="link_end_control">End</string>
    <string name="link_end_control_notice">This will make the previous links invalid. Are you sure
        you want to generate this control link?
    </string>
    <string name="link_generate_action">Generate a Link</string>
    <string name="link_generate_control_notice">Being controlled:</string>
    <string name="link_generate_notice">Send this link to your partner</string>
    <string name="link_notice_times">Give Control for your toy</string>
    <string name="link_notice_times_minutes">Minutes</string>
    <string name="link_notice_times_seconds">Seconds</string>
    <string name="link_notice_tip_1">Enter a value between 0 and 60 (minutes) or between 0 and 120
        (seconds).
    </string>
    <string name="link_notice_tip_2">Use 0 minutes for an unlimited amount of time.</string>
    <string name="link_notice_tip_3">Link will automatically expire after 30 minutes (if no one
        visits the URL)
    </string>
    <string name="link_notice_tip_4">Every link is unique and for one-time use. Do not visit the URL
        before sending it.
    </string>
    <string name="link_notice_title">Notes</string>
    <string name="link_overtime_minutes">Enter a value, smaller than 60.</string>
    <string name="link_overtime_seconds">Enter a value, smaller than 120.</string>
    <string name="link_toys_for">For</string>
    <string name="lock_enter_title">Enter Passcode</string>
    <string name="login_forget_emailSuccess">We have sent an e-mail to %1$s. Please check your
        e-mail to re-set your password.
    </string>
    <string name="login_forget_tip">Please enter the email address you used to register</string>
    <string name="login_forget_title">Forgot Password</string>
    <string name="login_success">Logged in successfully</string>
    <string name="main_account">Edit Profile</string>
    <string name="main_closeRange">Close Range</string>
    <string name="main_partnerPlay">Long Distance</string>
    <string name="main_patterns">Patterns</string>
    <string name="main_playlist">Playlist</string>
    <string name="message_control_end">Are you sure that you want to end this session?</string>
    <string name="message_notification_type_alarm">Alarm</string>
    <string name="message_notification_type_audio">Audio</string>
    <string name="message_notification_type_chat">Message</string>
    <string name="message_notification_type_live">Live Control</string>
    <string name="message_notification_type_pattern">Pattern</string>
    <string name="message_notification_type_picture">Picture</string>
    <string name="message_notification_type_sync">Sync Control</string>
    <string name="message_notification_type_video">Video call</string>
    <string name="message_notification_type_voice">Voice call</string>
    <string name="message_recalled">The message has been recalled</string>
    <string name="message_send_error">Failed to send message</string>
    <string name="music_add_playlist_success">Added successfully</string>
    <string name="music_add_to_playlist_hint">Add to playlists</string>
    <string name="music_album_list">Album</string>
    <string name="music_albums">Albums</string>
    <string name="music_all_no">No songs</string>
    <string name="music_all_no_tip">No songs were found on your device</string>
    <string name="music_already_exist_hint">This song is already in this playlist.</string>
    <string name="music_artist_list">Artist</string>
    <string name="music_artists">Artists</string>
    <string name="music_create_playlist">Create a new playlist</string>
    <string name="music_create_playlist_edt_hint">Playlist Name</string>
    <string name="music_create_playlist_hint">Please enter a playlist name</string>
    <string name="music_create_playlist_title">New Playlist</string>
    <string name="music_first">No previous</string>
    <string name="music_last">No next</string>
    <string name="music_login_google">Google Music</string>
    <string name="music_login_linked">Linked</string>
    <string name="music_login_linked_accounts">Linked Accounts</string>
    <string name="music_login_linked_accounts_notice">Do you want to unlink your Spotify Account?
    </string>
    <string name="music_login_not_linked">Not linked</string>
    <string name="music_login_pandora">Pandora</string>
    <string name="music_login_spotifiy">Spotify</string>
    <string name="music_my_favorite">My Favorite Songs</string>
    <string name="music_notExist">File not found, please refresh.</string>
    <string name="music_play_all">Play all</string>
    <string name="music_play_all_songs">(%1$s songs)</string>
    <string name="music_playing">Music Playing</string>
    <string name="music_search_clear_searches">Clear recent searches</string>
    <string name="music_search_hint">Input a song or artist</string>
    <string name="music_search_no_tip">No songs were found</string>
    <string name="music_search_value_null">Please enter a song, artist or album name.</string>
    <string name="music_song_list">Song</string>
    <string name="music_stream_login_error">Failed to log in. Please try again.</string>
    <string name="music_tab_playlist">Playlists</string>
    <string name="music_tracks">Songs</string>
    <string name="net_connect_error_tip">Network not available, please check your internet
        connection.
    </string>
    <string name="no_ble">Device doesn\'t have BLE support!</string>
    <string name="partner_profile">Chat Info</string>
    <string name="partner_profile_autoAccept">Allow control without requests</string>
    <string name="partner_profile_autoAlarm">Auto-save alarm from this person</string>
    <string name="partner_profile_autoPlay">Auto-play patterns that this person sends</string>
    <string name="partner_profile_black_setting">Block this contact</string>
    <string name="partner_profile_black_setting_success">Settings changed successfully</string>
    <string name="partner_profile_mute_notifications_setting">Mute notifications</string>
    <string name="partner_profile_nick_name_title">Nick Name</string>
    <string name="partner_profile_notifyLog">Notify when this person logs on</string>
    <string name="partner_profile_recorded_files">Previously recorded sessions</string>
    <string name="partner_profile_to_top_setting">Sticky to top</string>
    <string name="partner_profile_vibrate">Vibrate when any message is received</string>
    <string name="partner_profile_vibrate_by_audio">Vibrate during audio messages</string>
    <string name="pattern_choose">Choose Pattern</string>
    <string name="pattern_create">Create Pattern</string>
    <string name="pattern_createing">Creating pattern</string>
    <string name="pattern_description">Description</string>
    <string name="pattern_format_error">Pattern format error</string>
    <string name="pattern_music_empty">No songs were found on your device.</string>
    <string name="pattern_name">Pattern Name</string>
    <string name="pattern_name_empty">Please fill out the pattern name</string>
    <string name="pattern_no">No Patterns</string>
    <string name="pattern_no_tip">You can create a new pattern or download one</string>
    <string name="pattern_play_loop">Loop All</string>
    <string name="pattern_play_speed">Playback Speed</string>
    <string name="pattern_playlist_name">Pattern playlist name</string>
    <string name="pattern_receive_control_link_not_play">Cancel</string>
    <string name="pattern_receive_control_link_play">Play</string>
    <string name="pattern_recording">Recording</string>
    <string name="pattern_save">Save Pattern</string>
    <string name="pattern_selectOne">Please select a pattern</string>
    <string name="pattern_shareAnonymous">Share anonymously</string>
    <string name="pattern_share_alart_message">Due to app regulations, we\'ve had to change the way
        shared patterns work. All future pattern names will have to be approved by our team before
        they show up publicly.
    </string>
    <string name="pattern_share_notice">Please do not post any words or phrases that can be
        interpreted as sexually explicit. If you do, we must remove it, or you put our app at risk
        of being removed from the app stores.
    </string>
    <string name="pattern_shared_title">Edit Shared</string>
    <string name="pattern_touchMsg">Tap or slide to control</string>
    <string name="pattern_touchMsg_loop">Slide to record and release to start loop</string>
    <string name="patterns_begin_control">Controlled</string>
    <string name="patterns_drag_notice">Press and drag the pattern to change the order</string>
    <string name="patterns_end_control">End control</string>
    <string name="patterns_horizontal_bar_my_favorites">My favorites</string>
    <string name="patterns_in_control">Controlling</string>
    <string name="patterns_menu_favorites">Add to favorites</string>
    <string name="patterns_menu_report">Report</string>
    <string name="patterns_menu_report_hint">Your reason for reporting this pattern:</string>
    <string name="patterns_menu_report_reason_1">Relates to minors</string>
    <string name="patterns_menu_report_reason_2">Personal attack</string>
    <string name="patterns_menu_report_reason_3">Hateful or abusive</string>
    <string name="patterns_menu_report_reason_4">Sexually Explicit</string>
    <string name="patterns_menu_report_reason_5">Spamming</string>
    <string name="patterns_menu_report_reason_6">Other</string>
    <string name="patterns_no_available">This pattern is no longer available</string>
    <string name="patterns_preset_earthquake">Earthquake</string>
    <string name="patterns_preset_fireworks">Fireworks</string>
    <string name="patterns_preset_pulse">Pulse</string>
    <string name="patterns_preset_wave">Wave</string>
    <string name="patterns_reported">Reported</string>
    <string name="patterns_reported_notice">This pattern has been removed from the pattern store
        because it violates our language policy.
    </string>
    <string name="patterns_result_add_failed">Add failed</string>
    <string name="patterns_result_added_success">Added successfully</string>
    <string name="patterns_result_remove_failed">Remove failed</string>
    <string name="patterns_result_removed">Pattern removed</string>
    <string name="patterns_result_report_failed">Report failed</string>
    <string name="patterns_result_report_summitted">Report submitted</string>
    <string name="patterns_result_update_name">Change successfully</string>
    <string name="patterns_result_update_name_failed">Change failed</string>
    <string name="patterns_share_no_tip">You haven\'t shared any patterns</string>
    <string name="people_add">Add People</string>
    <string name="people_no">No Contacts</string>
    <string name="people_no_tip">Please invite your partner by tapping \"+\" in the top right hand
        corner
    </string>
    <string name="picture_add">Add Picture</string>
    <string name="playlist_create">Create Playlist</string>
    <string name="playlist_name_empty">Please fill out the playlist name</string>
    <string name="profile_avatar_picture">Change Profile Picture</string>
    <string name="profile_name_error">Name cannot contain special characters</string>
    <string name="program_title">Program</string>
    <string name="programs_add">Add Pattern</string>
    <string name="programs_add_exist_notice">(You can add %1$s patterns.)</string>
    <string name="programs_add_notice">You can add 10 patterns.</string>
    <string name="programs_ambi_levels">Adjust Levels</string>
    <string name="programs_change_save">You\'re trying to leave without updating. Would you like to
        save your settings?
    </string>
    <string name="programs_choose">Choose patterns</string>
    <string name="programs_choose_no_more">You can only add up to 10 patterns.</string>
    <string name="programs_choose_notice">Patterns longer than 50 seconds are not displayed. \n The
        limit for this feature is 50 seconds.
    </string>
    <string name="programs_domi_lights">Enable/Disable Lights</string>
    <string name="programs_record">Record a pattern</string>
    <string name="received_alarm_to_notice">Alarm received, please connect a toy to save the alarm
    </string>
    <string name="received_pattern_to_notice">Pattern received, please connect a toy to play the
        pattern
    </string>
    <string name="register_name_error">Username must be between 6-20 characters</string>
    <string name="remote_alexa">Alexa</string>
    <string name="remote_alexa_get_pin">Get PIN number</string>
    <string name="remote_alexa_launch_notice">in Two easy steps.</string>
    <string name="remote_alexa_launch_open">Alexa, open Lovense</string>
    <string name="remote_alexa_launch_pin">My PIN number is</string>
    <string name="remote_alexa_launch_tell">Please tell Alexa</string>
    <string name="remote_alexa_more">More Commands</string>
    <string name="remote_alexa_pin_expire">PIN number will expire in %1$s</string>
    <string name="remote_alexa_say_notice">Control your toy with Alexa</string>
    <string name="remove_music_item">Remove %1$s?</string>
    <string name="remove_music_item_not_local">Can\'t remove- it\'s not stored locally</string>
    <string name="remove_music_item_not_owner">You cannot change someone else’s playlist.</string>
    <string name="remove_music_item_success">Removed %1$s successfully!</string>
    <string name="send_failure">Send Failure</string>
    <string name="set_alarm_customer_week_friday">Friday</string>
    <string name="set_alarm_customer_week_monday">Monday</string>
    <string name="set_alarm_customer_week_saturday">Saturday</string>
    <string name="set_alarm_customer_week_sunday">Sunday</string>
    <string name="set_alarm_customer_week_thursday">Thursday</string>
    <string name="set_alarm_customer_week_tuesday">Tuesday</string>
    <string name="set_alarm_customer_week_wednesday">Wednesday</string>
    <string name="set_alarm_date">Date</string>
    <string name="set_alarm_frequency">Frequency</string>
    <string name="set_alarm_frequency_customer_week">Custom</string>
    <string name="set_alarm_frequency_everyday">Every day</string>
    <string name="set_alarm_frequency_once">Only send once</string>
    <string name="set_alarm_frequency_weekday">Monday to Friday</string>
    <string name="set_alarm_time">Time</string>
    <string name="set_alarm_title">Set Alarm</string>
    <string name="alarms_reminders_permission_required">Alarms &amp; reminders permission must be enabled to make alarms work.</string>
    <string name="setting_auto_swith">Stop vibrating when there is an accidental Bluetooth disconnection</string>
    <string name="setting_black_contacts">Blocked Contacts</string>
    <string name="setting_black_contacts_chat_background">Set the current chat background</string>
    <string name="setting_black_contacts_friends">Blocked Friends</string>
    <string name="setting_black_contacts_requests">Rejected Requests</string>
    <string name="setting_black_image_not_exist">The image you chose doesn\'t exist in this
        location
    </string>
    <string name="setting_passcode_change">Passcode Settings</string>
    <string name="setting_passcode_not_match_tip">Passcode doesn\'t match. Please re-enter your
        passcode.
    </string>
    <string name="setting_passcode_not_match_tip_lock">Passcode doesn\'t match. Please re-enter your
        passcode.
    </string>
    <string name="setting_passcode_notice_bottom">NOTE: Our server will not save your passcodes.
        Please take care of this.
    </string>
    <string name="setting_passcode_notice_top">Only the correct passcode or your phone’s
        fingerprint/face recognition can unlock your app.
    </string>
    <string name="setting_passcode_reenter_tip">Please re-enter your 4 digit passcode</string>
    <string name="setting_passcode_settings_disable_hint">Do you want to disable your passcode ?
    </string>
    <string name="setting_passcode_tip">Please enter your 4-digit passcode</string>
    <string name="setting_password_change">Change your password</string>
    <string name="setting_record_permission">\"Lovense Remote\" doesn\'t have permission to record
        audio. You can change the app permissions in your phone’s security permissions.
    </string>
    <string name="setting_run_lastLevel">Toy will go to last level when it reconnects</string>
    <string name="settings_message_notifications_link">Notifications</string>
    <string name="settings_message_show_preview_text">Show message content</string>
    <string name="settings_message_sound">Sound</string>
    <string name="settings_message_vide_call">Call Notifications</string>
    <string name="settings_my_account">My Account</string>
    <string name="signup_email_notice">Please enter a vaild email address.</string>
    <string name="signup_license_privacy">By tapping Sign Up, you agree to our %1$s and %2$s.
    </string>
    <string name="signup_license_privacy_agree">Accept Terms</string>
    <string name="signup_name_hint">Username</string>
    <string name="signup_name_notice">Must be 6-20 characters</string>
    <string name="signup_password_notice">Use 8-99 characters with a mix of letters, numbers &amp;
        symbols
    </string>
    <string name="signup_privacy_hint">Please click and read our privacy policy before ticking this
        box.
    </string>
    <string name="sound_recorder_start_error">Failed to start the Sound Mode. Please try again.
    </string>
    <string name="spotify_premium_supported">We currently only support Spotify premium accounts
    </string>
    <string name="system_account_single">Account logged in with another device. If this was not you,
        please reset your password.
    </string>
    <string name="system_bluetooth_off">Please turn on your phone\'s Bluetooth.</string>
    <string name="system_email_error">Please enter a valid E-mail</string>
    <string name="system_update_notices">Update Available\nV %1$s</string>
    <string name="system_update_notices_latest">You have the latest version</string>
    <string name="system_upload_error">Failed to upload</string>
    <string name="tip_friend_login">%1$s is online now</string>
    <string name="tip_toy_notConnect">Your toy is not currently connected</string>
    <string name="title_commit_rule">You are not allowed to put an email address or website in the
        pattern title.
    </string>
    <string name="toy_connect_not_exist">You have not connected a toy. Please connect a toy to use
        this feature.
    </string>
    <string name="toy_connected">Connected</string>
    <string name="toy_connected_connected">(%1$s) connected</string>
    <string name="toy_connected_disconnected">(%1$s) disconnected</string>
    <string name="toy_connecting">Connecting...</string>
    <string name="toy_control_float">Float</string>
    <string name="toy_control_manual_panel">Traditional</string>
    <string name="toy_control_pump">Contract</string>
    <string name="toy_control_rotate">Rotate</string>
    <string name="toy_control_slide_panel">Slide</string>
    <string name="toy_control_tip_max_level">MAX LEVEL</string>
    <string name="toy_control_traditional_panel">Traditional\nPanel</string>
    <string name="control_panel_toy_connected">%1$s toy connected</string>
    <string name="control_panel_toys_connected">%1$s toys connected</string>
    <string name="toy_control_vibrate">Vibrate</string>
    <string name="toy_defind_name_title">Toy Name</string>
    <string name="toy_empty_connected_notice">There is no toy currently connected</string>
    <string name="toy_low_battery_notices">%1$s battery is lower than %2$s</string>
    <string name="toy_no">No Toy</string>
    <string name="toy_not_found">Your toy is disconnected, reconnect the toy to continue
        upgrading.
    </string>
    <string name="toy_playlist_delete">Do you want to delete this playlist?</string>
    <string name="toy_program_delete_pattern">Are you sure you want to delete?</string>
    <string name="toy_program_disconnected">This toy has been disconnected, please reconnect.
    </string>
    <string name="toy_remove_tip">Tap and hold to fully disconnect the toy from this phone.</string>
    <string name="toy_search_manual_action">Search Manually</string>
    <string name="toy_search_manual_title">Can\'t find your toys?</string>
    <string name="toy_search_tip">Searching for toy...</string>
    <string name="toy_selectOne">Please select a toy.</string>
    <string name="toy_select_tip">You can only choose two toys.</string>
    <string name="toy_update_bnt">Upgrade</string>
    <string name="toy_update_exit_notice">The toy is upgrading, do not exit or it will fail. Please
        wait for the upgrade to be complete.
    </string>
    <string name="toy_update_notice" formatted="false">Before starting the firmware update, enable
        your device\'s Bluetooth function. Ensure your toy has at least 50% battery and your phone
        has at least 40% battery.
    </string>
    <string name="toy_update_title">Firmware Upgrade</string>
    <string name="toys_bind_notice_401">The toy does not exist in our system. Please contact our
        customer service!\n%1$s(%2$s)\n
    </string>
    <string name="toys_bind_notice_402">The toy has been disabled. Please contact our customer
        service!\n%1$s(%2$s)\n
    </string>
    <string name="toys_bind_notice_403">The toy has been attached to too many Lovense
        accounts!\n%1$s(%2$s)\n
    </string>
    <string name="update_alarm_notice">Do you want to transfer existing alarms linked to this toy?
        If not, the alarms will be deleted.
    </string>
    <string name="user_add_addSelf">You cannot add yourself as friend.</string>
    <string name="user_add_email_empty">Please enter a username</string>
    <string name="user_add_success">Request has been sent successfully.</string>
    <string name="user_manager_delete_friend">Remove %1$s from your friend list?</string>
    <string name="user_profile_mood_message">Mood Message</string>
    <string name="user_profile_status_available">Available</string>
    <string name="user_profile_status_busy">Busy</string>
    <string name="user_profile_status_invisible">Invisible</string>
    <string name="user_profile_status_offline">Offline</string>
    <string name="welcome_explore">Offline Control</string>
    <string name="welcome_login">Log In</string>
    <string name="welcome_login_forget">Forgot?</string>
    <string name="welcome_signup">Sign Up</string>
    <string name="passcode_forget">Forgot?</string>
    <!--分割线-->


    <string name="common_exist_line">Your toy is being controlled. Would you like to end control
        session?
    </string>
    <string name="chat_nonsupport_recall">Your friend is using an old version of Lovense Remote. The
        recall function is not available.
    </string>
    <string name="system_new_message">You have received a new message.</string>
    <string name="notifications_tip_notices">Remote not activated</string>
    <string name="pattern_dialog_title_format">Playback %1$s</string>
    <string name="pattern_dialog_title">Playback</string>
    <string name="common_select">Select</string>
    <string name="request_from_type_title">%1$s from %2$s</string>
    <string name="request_from_type_notice">You have a control link session opened. If you accept
        the %1$s, the control link session will end.
    </string>
    <string name="play_pattern_exist_controllink_notice">You have a control link session opened. If
        you want to play the pattern, the control link session will end.
    </string>
    <string name="controllink_notoy_text">There is no currently connected toy</string>
    <string name="controllink_choose_text">Please choose toys first</string>
    <string name="record_max_size_notice">Maximum recording time for a pattern is one hour</string>
    <string name="pattern_shareddelete_warning">The pattern you shared will also be deleted.
    </string>
    <string name="patternslist_drag_notice">Press and drag the pattern/playlist to change the
        order.
    </string>
    <string name="video_accept_notice">%1$s has accepted your request.</string>
    <string name="common_reshared">Reshare</string>
    <string name="dialog_receiveRequest_voice">Voice Call From</string>
    <string name="add_friend_user_requested">%1$s already sent you the request. Please accept it.
    </string>
    <string name="program_max_size_notice">Maximum recording time for a pattern is 50 seconds.
    </string>
    <string name="send_program_max_size_notice">If you don\'t save the pattern, it will be deleted.
        Are you sure you want to continue?
    </string>
    <string name="send_program_record_title">Send Pattern</string>
    <string name="toy_program_notice">Do you want to restore the toy\'s default vibration
        settings?
    </string>
    <string name="toy_program_success_notice">Restore successfully</string>
    <string name="toy_program_failed_notice">Restore failed</string>
    <string name="toy_program_restore">Restore</string>
    <string name="voice_friend_noToy">%1$s does not have a toy connected</string>
    <string name="long_chat_picturres">Pictures</string>
    <string name="long_chat_picturres_notices">Images will not appear in your chat history after you
        delete them.
    </string>
    <string name="setting_keep_screen">Keep the screen awake</string>
    <string name="media_pattern_no_tip">Patterns you have saved during video and voice calls will
        show up here.
    </string>
    <string name="message_user_voice_offline">Network issues. Call has ended.</string>
    <string name="message_poor_network">Unable to establish %1$s call connection with your friend.
    </string>
    <string name="common_inprocessing">Processing...</string>
    <string name="offline_unlock_account">Please login in to use this feature.</string>

    <string name="chat_waitAcceptance_notice">Waiting to accept your request.</string>
    <string name="dialog_receiveRequest_notice">Invites you to a voice call.</string>
    <string name="dialog_receiveRequest_notice_longtime">May not be available now.</string>
    <string name="chat_voice_supported_upgrade">%1$s needs to the update to the latest version of
        the app to use the %2$s call feature.
    </string>
    <string name="chat_voice_unsupported">%1$s call feature is not yet supported for the platform
        that %2$s is using.
    </string>
    <string name="chat_change_model_pasivity">Switched to voice call.</string>
    <string name="chat_change_model">%1$s switched to voice call.</string>
    <string name="chat_media_video_busy">Unavailable dring video calls.</string>
    <string name="chat_media_voice_busy">Unavailable dring voice calls.</string>
    <string name="toy_program_delete_pattern_by_name">Are you sure you want to delete %1$s?</string>
    <string name="message_poor_network_webrtc">poor network connection</string>
    <string name="delete_pattern_fail">can not delete the pattern being played</string>

    <string name="event_ends_in_banner">Ends in: %1$s</string>
    <string name="event_starts_in_banner">Starts in: %1$s</string>
    <string name="event_day_in_banner">days</string>
    <string name="orgy_event_timer_top_part">YOU ARE CURRENTLY PARTICIPATING</string>
    <string name="orgy_event_timer_format">%1$sh%2$sm</string>

    <string name="orgy_event_close">Are you sure you want to close this notification?</string>
    <string name="orgy_event_close_now">Close for now</string>
    <string name="orgy_event_close_forever">Close forever</string>
    <string name="orgy_event_join_notice">You have successfully joined this event. Please ensure
        your toy or toys are linked to this account to take part.
    </string>
    <string name="orgy_event_unable_join_notice">Unable to join at this time, please try again
        later.
    </string>
    <string name="orgy_event_end_notice">Sorry, this event has ended.</string>
    <string name="block_fail_tip_video_call">Unable to block during video call.</string>
    <string name="block_fail_tip_sync_control">Unable to block during sync control.</string>
    <string name="block_fail_tip_live_control">Unable to block during live control.</string>
    <string name="block_fail_tip_voice_call">Unable to block during voice call.</string>
    <string name="delete_failure">Delete failure</string>
    <string name="cancel_live_control_and_play_music">Music cannot be played during Live Control.
    </string>
    <string name="cancel_syn_control_and_play_music">Music cannot be played during Sync Control.
    </string>
    <string name="cancel_voice_control_and_play_music">Music cannot be played during Voice Calls.
    </string>
    <string name="cancel_video_control_and_play_music">Music cannot be played during Video Calls.
    </string>
    <string name="cancel_pattem_control_and_play_music">Music cannot be played during patterns.
    </string>
    <string name="cancel_create_pattem_and_play_music">Music cannot be played when creating
        patterns.
    </string>
    <string name="cancel_sound_control_and_play_music">Music cannot be played during Sound
        Control.
    </string>
    <string name="cancel_remote_control_and_play_music">Music cannot be played during Remote
        control.
    </string>
    <string name="ninja_mode_record_music_conflict">Music cannot be played when you are recording a
        pattern.
    </string>
    <string name="cancel_control_and_play_music">Music cannot be played when toys are being
        controlled.
    </string>
    <string name="language">Language</string>
    <string name="common_draft">[Draft]</string>
    <string name="yesterday">Yesterday</string>
    <string name="music_record_title">Recording a pattern</string>
    <string name="music_preview">Preview</string>
    <string name="musci_pattern_recording">Re-recording</string>
    <string name="musci_pattern_save">Save</string>
    <string name="musci_pattern_pause_normal">Pause</string>
    <string name="music_record_tip">• When the song is over, the pattern will be saved
        automatically.
    </string>
    <string name="music_record_pattern_saved_successfully">The pattern has been saved
        successfully!
    </string>
    <string name="music_record_fail_to_load">Fail to load, click to retry</string>
    <string name="delete_success">Delete successfully</string>
    <string name="music_record_net_connect_error_tip">Please check your network connection</string>
    <string name="alarm_sound_min_time">the minimum time is 5 seconds</string>

    <string name="system_setnotification_disabled_tips">Lovense Remote notifications are disabled on
        your device. Please change your notification settings for this app.
    </string>
    <string name="add_toy_tip">Add a toy</string>

    <string name="empty">Empty</string>
    <string name="str_alarm_duration">Alarm Duration</string>
    <string name="str_alarm_snooze">Snooze</string>
    <string name="str_alarm_snooze_count">Snooze count</string>
    <string name="str_alarm_snooze_duration">Snooze duration (minutes)</string>
    <string name="str_alarm_never">Never</string>
    <string name="str_alarm_count">Count</string>
    <string name="str_duration">Duration</string>
    <string name="str_alarm_turn_off">Swipe up to turn off</string>
    <string name="str_alarm_never_snooze">Never snooze</string>
    <string name="str_voice_unavlidate">Unable to initiate an audio call. Either the microphone is
        in use by another app, or you must change permissions to allow this app to use the
        microphone.
    </string>
    <string name="str_video_unavlidate">Unable to initiate a video call. Either the camera or
        microphone is in use by another app, or you must change permissions to allow this app to use
        the camera and microphone.
    </string>
    <string name="str_block">Block</string>
    <string name="str_title_new_request">Newest requests</string>
    <string name="str_title_old_request">Other requests</string>
    <string name="str_add_friends_suc">You have accepted %1$s\’s request.</string>
    <string name="str_decline_add_friends_suc">You have declined %1$s\'s request.</string>
    <string name="str_block_add_friends_suc">%1$s has been blocked.</string>
    <string name="str_send_message">Send Message</string>
    <string name="str_unblock_tip">Unblock contact before sending message.</string>
    <string name="str_nomore_data">No more data</string>
    <string name="input_string_error">Text cannot contain special characters.</string>

    <string name="refresh_token_expired">Login expired. Please login again.</string>
    <string name="delete_frends_message_tip_1">%1$s is no longer your friend.</string>
    <string name="delete_frends_message_tip_2">Send a friend request</string>
    <string name="delete_frends_message_tip_3">&#160;to chat again.</string>
    <string name="str_music_permission_tip">Microphone permissions must be allowed for the toy to
        vibrate according to the music. Go to settings.
    </string>
    <string name="app_open_must_permission_new">The app does not have permissions to the microphone.
        The toy cannot vibrate according to the music.
    </string>
    <string name="app_open_must_permission_new_1">For the toy to vibrate according to the music,
        please allow the app permissions to the microphone.
    </string>
    <string name="unblock_frends_message_tip">%1$s is no longer your friend.You can send a friend
        request to chat again.
    </string>
    <string name="add_blocke_friends_fail">%1$s has been blocked. Do you want to unblock this user
        and send a friend request?
    </string>
    <string name="str_alarm_minute">m</string>
    <string name="str_alarm_second">s</string>
    <string name="add_blocke_friends_fail_1">%1$s has been blocked. Do you want to unblock this
        user?
    </string>
    <string name="common_go_setting">Go to settings</string>
    <string name="str_webrtc_old_android_video">Lovense Remote requires an update to better support
        the newest mobile operating systems. To ensure proper functionality during video calls,
        please inform %1$s to update the app as soon as possible.
    </string>
    <string name="str_webrtc_old_android_voice">Lovense Remote requires an update to better support
        the newest mobile operating systems. To ensure proper functionality during voice calls,
        please inform %1$s to update the app as soon as possible.
    </string>
    <string name="str_webrtc_old_ios_video">Lovense Remote requires an update to better support the
        newest mobile operating systems. To ensure proper functionality during video calls, please
        update the app as soon as possible.
    </string>
    <string name="str_webrtc_old_ios_voice">Lovense Remote requires an update to better support the
        newest mobile operating systems. To ensure proper functionality during voice calls, please
        update the app as soon as possible.
    </string>
    <string name="str_led_color_title">LED Color</string>
    <string name="red">Red</string>
    <string name="green">Green</string>
    <string name="blue">Blue</string>
    <string name="yellow">Yellow</string>
    <string name="pink">Pink</string>
    <string name="turquoise">Turquoise</string>
    <string name="multicolor">Multicolor</string>
    <string name="str_video_old_tip">Switching modes is unavailable. %1$s must use the most recent
        mobile version of Lovense Remote.
    </string>
    <string name="str_video_old_pc_tip">Switching modes is unavailable.</string>
    <string name="str_end_dating">End Dating</string>
    <string name="str_invite_date_tip">%1$s invites you to join live-dating, would you like to
        accept their invitation?
    </string>
    <string name="str_date_end_invite_add_friend_tip">Would you like to become friend with %1$s?
    </string>
    <string name="str_invite_date_cancle">%1$s has cancelled their live-dating request.</string>
    <string name="str_login_diffenct_account">To begin live-dating with %1$s, please sign in to
        Lovense Remote using your forum account %2$s.
    </string>
    <string name="str_wait_date_tip">%1$s has not yet joined this chat session. Would you like to
        continue waiting for them?
    </string>
    <string name="str_is_from_forum">This is %1$s from Lovense Life</string>
    <string name="str_unknown">unknown</string>
    <string name="str_rece_dating_tip">The message from Lovense Life</string>
    <string name="str_dating_end">%1$s has ended their live-dating session with you.</string>

    <string name="str_wait_date_tip_ok">Wait</string>
    <string name="str_wait_date_tip_no">Leave</string>
    <string name="str_invite_date_decline">%1$s has declined your request.</string>
    <string name="str_dating_has_joined">%1$s has joined this chat session.</string>

    <string name="userdata_error_text">Please log in again.</string>
    <string name="str_pattern_name_error">Text cannot contain special characters \&#060; \&#062; \&#042;
        \&#039; \&#034;
    </string>
    <string name="str_pattern_name_long_string">More than 30 characters</string>
    <string name="lvs_I_dont_support">You must connect a Max, Nora, Calor, or XMachine to be in
        control.
    </string>
    <string name="lvs_partner_dont_support">Your partner must connect a Max, Calor, Nora, or
        XMachine to be in control.
    </string>
    <string name="toast_lds_switch_control1">Use your %1$s to control your partner\'s toy.</string>
    <string name="toast_lds_switch_control2">Control your partner\'s toys with the control panel.
    </string>
    <string name="toast_lds_switch_control3">Your partner has control.</string>
    <string name="str_me">Me</string>
    <string name="str_select_fail_by_toy_disconnected">%1$s disconnected</string>
    <string name="str_choose_toy_control_other_toys_1">Tap your toy icon to choose which toy
        controls your partner\'s toys.
    </string>
    <string name="str_choose_toy_control_other_toys_2">Tap your toy icon to choose which toy
        controls all other toys.
    </string>

    <string name="retry">Retry</string>
    <string name="apk_update_download_fail">Update failed. Please update it later.</string>
    <string name="apk_update_request_android_o_permission">We need the permission to install unknown
        source apps, please give us this permission.
    </string>
    <string name="apk_update_fail_android_o_permission">Update failed. Please grant Lovense Remote
        permission to install apps from unknown sources or update the app through Google Play.
    </string>

    <string name="message_resend_button">Resend this message</string>

    <string name="str_chatroom_voicecall">voice call</string>
    <string name="str_chatroom_videocall">video call</string>
    <string name="str_chatroom_live">live control</string>
    <string name="str_chatroom_sync">sync control</string>

    <string name="str_call_back">Call back</string>
    <string name="str_call_again">Call again</string>

    <string name="str_you_decline_control_request">You declined the %1$s</string>
    <string name="str_partner_cancel_control_request">Missed a %1$s from %2$s</string>
    <string name="str_partner_started_control_session">%1$s started the %2$s</string>

    <string name="str_you_cancel_control_request">You canceled the %1$s</string>
    <string name="str_partner_decline_control_request">%1$s declined the %2$s</string>
    <string name="str_you_started_control_session">You started the %1$s</string>

    <string name="str_chatroom_connection_error">Connection error</string>
    <string name="str_control_request_timeout">%1$s wasn’t answered</string>

    <string name="str_chatroom_partner_offline">%1$s is offline, please try again later.</string>
    <string name="str_chatroom_partner_no_toy">Your partner does not have a toy connected, please
        try again later.
    </string>

    <string name="toy_reconnect_time">Reconnecting in %1$s</string>
    <string name="toy_reconnect_now">Reconnect now</string>
    <string name="toy_connect_error_b0011">There is a connection error. Please reboot %1$s and
        reconnect.
    </string>
    <string name="toy_connect_error_b0011_connect">Connect</string>

    <string name="str_game_stop">Stop</string>
    <string name="str_game_toy_tip">Max 2 is in use by Mirror Life.</string>

    <string name="app_close_battery_optimization">To ensure a stable connection with the toy, we
        recommend disabling battery optimization for Lovense Remote.
    </string>

    <string name="sign_up_password">Password: 8-99 characters</string>

    <string name="old_password_not_correct">Old password is not correct.</string>
    <string name="new_password_same_as_old_password">Your new password cannot be the same as your
        current password!
    </string>

    <string name="lvs_login_password_error">Username or password incorrect! [5007]</string>
    <string name="login_username_or_password_error_more">Username or password incorrect, reset your
        password? [5007]
    </string>
    <string name="live_control_panel_partner_no_toy">You can control %1$s once they connect a toy.
    </string>
    <string name="str_chatroom_no_toy">At least one user needs to have a toy connected.</string>
    <string name="left_view_sync_pattern">Auto-Sync Patterns</string>
    <string name="pattern_sync_enable">To sync patterns, you must agree to save patterns on Lovense
        servers. You can delete this data at any time by disabling sync.
    </string>
    <string name="common_agree">Agree</string>
    <string name="pattern_sync_disable">Do you also want to delete all saved patterns from the
        server? \n Note: Patterns you have shared to the pattern store will remain there.
    </string>
    <string name="pattern_sync_success">Patterns are synced.</string>
    <string name="pattern_in_sync">Syncing patterns...</string>
    <string name="pattern_sync_failed_server_error">Sync failed. Patterns will automatically attempt
        to sync again later on.
    </string>
    <string name="pattern_sync_failed_network_error">Sync failed. Patterns will automatically
        attempt to sync again when you connect to a network.
    </string>
    <string name="pattern_support_sync">You can now sync your saved patterns across devices. Do you
        want to sync patterns now?
    </string>
    <string name="pattern_play_failed">%1$s has not finished downloading from the server. The next
        pattern will be played automatically.
    </string>
    <string name="pattern_sync_on_share_failed">%1$s has not finished downloading from the server.
        Do you want to download it now?
    </string>
    <string name="pattern_sync_no_del">Keep</string>
    <string name="pattern_delete_local_sever">This pattern will be permanently deleted from this
        device and the server. Continue?
    </string>
    <string name="pattern_is_broken">This pattern cannot be found. Do you want to delete it?
    </string>
    <string name="pattern_format_error_delete">Pattern format error. Do you want to delete it?
    </string>
    <string name="pattern_havent_download">%1$s has not finished downloading from the server. Do you
        want to download it now?
    </string>
    <string name="updating">Updating</string>
    <string name="qrcode_not_lovense_qrcode">The QR code is not a Lovense QR code. Please scan a
        Lovense QR code.
    </string>
    <string name="qrcode_scan_with_connect">Please scan this QR code with Lovense Connect.</string>
    <string name="qrcode_dont_connect_max">Connect Max/Nora/Calor to Lovense Remote first, then scan
        the QR code.
    </string>
    <string name="display_panel_wait_panel">Please proceed to the model’s room within %1$s and click
        the start button to begin.
    </string>
    <string name="display_panel_sync_on">Proceed to the model’s room and enjoy!</string>
    <string name="qrcode_menu">Scan QR</string>
    <string name="qrcode_upload_toy_successfully">Connection established with the model. Keep the
        toy connected and prepare to control.
    </string>
    <string name="qrcode_server_error">Server error. Unable to establish a connection with the
        model. Please try again（%1$s)
    </string>
    <string name="qrcode_no_network">Unable to establish a connection with the model. Please check
        your network connection and try again.
    </string>
    <string name="display_panel_stop_sync">End sync</string>
    <string name="qrcode_scan_two_toy">QR scan successful. Choose a toy to sync with the model’s
        toy.
    </string>
    <string name="display_panel_mouse_controlling">Controlling with mouse.</string>
    <string name="display_panel_sync_not_stable">Unstable sync connection detected. Consider
        switching to mouse control.
    </string>
    <string name="display_panel_toy_disconnect_title">You may continue to wait for toy reconnection
        or switch to mouse control.
    </string>
    <string name="display_panel_mouse_panel">Please proceed to the model’s room to use slider
        control or switch back to sync control with %1$s.
    </string>
    <string name="display_panel_stop_sync_inquiry">End sync control? The remainder of the control
        session will be switched to mouse control.
    </string>
    <string name="display_panel_end_of_queue">You have missed your turn and have been placed at the
        back of the queue.
    </string>
    <string name="display_panel_toy_reconnecting">Your toy is reconnecting.</string>
    <string name="display_panel_toy_reconnected">Your toy is connected.</string>

    <string name="qrcode_browser_dont_connect_max">Connect Max/Nora/Calor to Lovense Remote and try
        again.
    </string>
    <string name="qrcode_browser_upload_toy_successfully">Connection established with the model.
        Keep the toy connected and go back to broadcating page and prepare to control.
    </string>
    <string name="display_browser_panel_stop_sync_inquiry">End sync control? The remainder of the
        control session will be switched to slider control.
    </string>
    <string name="display_browser_panel_toy_disconnect_title">You may continue to wait for toy
        reconnection or switch to slider control.
    </string>
    <string name="display_browser_panel_slider_controlling">Controlling with slider</string>
    <string name="display_browser_panel_slider_panel">Please proceed to the model’s room to use
        slider control or switch back to sync control with %1$s
    </string>
    <string name="display_browser_panel_sync_not_stable">Unstable sync connection detected. Consider
        switching to slider control
    </string>


    <string name="ninja_unlock_screen">>>> Swipe to return</string>
    <string name="ninja_mode_enable_setting">Ninja Mode</string>
    <string name="ninja_mode_permission">To fully support Ninja Mode’s features, we recommend
        enabling the display over other apps option.
    </string>
    <string name="display_panel_toy_connected">%1$s is synced for control.</string>
    <string name="display_panel_sync_over">Sync control with the model has ended.</string>
    <string name="display_panel_mouse_control_toast">Switched to mouse control. Please proceed to
        the model’s room.
    </string>
    <string name="qrcode_scan_at_right_position">Align QR Code within frame to scan</string>
    <string name="qrcode_scan_with_another_account">Please note the QR code can only be scanned by
        one account.
    </string>
    <string name="not_connect">Disconnected</string>
    <string name="load_vcard_tip">Retrieving…</string>
    <string name="connecting">Connecting…</string>
    <string name="common_retrieving">Retrieving</string>
    <string name="common_connecting">Connecting</string>
    <string name="depth_control">Depth Control</string>
    <string name="depth_control_off">Off</string>
    <string name="depth_control_low_vibration">Off(0)</string>
    <string name="depth_control_high_vibration">High(20)</string>
    <string name="depth_control_intensity_title">Vibration intensity at each depth</string>
    <string name="depth_control_on">On</string>
    <string name="settings_chat">Chat</string>
    <string name="settings_chat_theme">Theme</string>
    <string name="settings_chat_background">Chat Background</string>
    <string name="settings_chat_theme_note">Chat room themes</string>
    <string name="chat_background_note">Current Background</string>
    <string name="chat_background_choose">Choose Background</string>
    <string name="chat_background_choose_photo">Choose a photo</string>
    <string name="chat_background_take_photo">Take a photo</string>
    <string name="clear_all_history_note">Are you sure? This will permanently delete ALL chat
        records from your device.
    </string>

    <string name="common_skip">Skip</string>

    <string name="playing_pattern_automatically">Received a pattern from %1$s. Playing…</string>


    <string name="qrcode_not_connet_toy">Connect at least one toy to Lovense Remote before scanning
        the QR code.
    </string>
    <string name="qrcode_not_wifi">Please ensure Lovense Remote and the Media Player are connected
        to the same network.
    </string>
    <string name="player_select_toy_connect">%1$s is used by Lovense Media Player</string>
    <string name="player_select_toy_disconnect">%1$s is disconnected, please check your connection
    </string>
    <string name="player_connect_error">Failed to establish a connection. Please try again.</string>
    <string name="player_disconnect_error">Connection with Lovense Media Player was lost. Please
        reconnect by scanning the QR code.
    </string>

    <string name="player_already_scanned">QR code already scanned.</string>
    <string name="player_recognize_QR_code">Unable to recognize QR code.</string>
    <string name="player_data_exception">Data exception.</string>
    <string name="player_data_parsing_exception">Data parsing exception.</string>

    <string name="operate_frequently">Request limit exceeded. Please try again later.</string>
    <string name="connect_incompatible_toy">%1$s is incompatible with the current version, please
        update the app.
    </string>


    <string name="toy_connected_toys">Connected: %1$s toys</string>
    <string name="toy_connect_now">Connect now</string>
    <string name="toy_add">Add Toy</string>
    <string name="toy_settings_no_toy">Please connect the toy before changing the settings.</string>
    <string name="toy_settings_no_toy_toast">Please connect your toy to change the setting.</string>
    <string name="toy_settings_toy_strength">Toy Strength</string>
    <string name="toy_settings_enable_led">Enable LED</string>
    <string name="toy_settings_delete">Delete Toy</string>
    <string name="toy_name_note">This name will serve as the Bluetooth device name when scanning for
        devices.
    </string>
    <string name="toy_control_type1">Vibration</string>
    <string name="toy_control_type2">Rotation</string>
    <string name="toy_control_type3">Contraction</string>
    <string name="common_min">Min</string>
    <string name="common_max">Max</string>
    <string name="version_up_to_date">Up to date</string>
    <string name="toy_settings_strength_note">Set max levels for this toy to reduce its vibration
        strength, sound level, and save on battery life. This setting is only in effect during
        long-distance control.
    </string>
    <string name="notification_doesnt_save_setting">Settings have not been saved. Save now?</string>
    <string name="discard_button">Discard</string>
    <string name="toy_strength_not_login">This is setting is for long distance control, please login
        to set
    </string>

    <string name="send_messgae_note">Please be aware that all text/voice messages and multimedia
        files you send will be stored on the recipient’s device and can be used in any way by the
        recipient. To ensure your security and privacy, please exercise appropriate caution and send
        messages at your own discretion.
    </string>
    <string name="dont_remind_button">Don’t remind me</string>

    <string name="group_chat_new">New Group</string>
    <string name="common_search">Search</string>
    <string name="people_chat_contact">Contacts</string>
    <string name="people_group_chats">Group Chats</string>
    <string name="common_search_no_result">No Results</string>
    <string name="common_show_more">Show more</string>
    <string name="group_chat_group_name">Group Name</string>
    <string name="group_chat_manage_group">Manage Group</string>
    <string name="group_chat_nickname">My Nickname in the Group</string>
    <string name="group_chat_remark">Remark</string>
    <string name="group_chat_setting_exit">Leave Group</string>
    <string name="group_chat_invite_others">Members can Invite Others</string>
    <string name="group_chat_invitation_approval">Invitation Approval</string>
    <string name="group_chat_invitation_approval_tip">Require an admin’s approval for invitation
        requests.
    </string>
    <string name="common_admin">Admin</string>
    <string name="group_chat_invitation_requests">Invitation Requests</string>
    <string name="coomon_send_msg">Send Message</string>
    <string name="group_chat_remove_menu">Remove from Group</string>


    <string name="group_chat_add_friend">Add Friend</string>
    <string name="group_chat_make_admin">Set as an Admin</string>
    <string name="group_chat_dismiss_admin">Dismiss as an Admin</string>
    <string name="group_chat_choose_members">Choose Members</string>
    <string name="group_chat_delete_group_note">Delete this group and its chat history?</string>
    <string name="group_chat_exited1">You are no longer a member of the group.</string>
    <string name="group_chat_exit_group_content">You will no longer be able to interact with this
        group. Leave group?
    </string>
    <string name="common_approve">Approve</string>
    <string name="group_chat_created1">%1$s created this group</string>
    <string name="group_chat_created2">You created this group</string>
    <string name="group_chat_make_admin1">%1$s set you as an admin</string>
    <string name="group_chat_make_admin2">%1$s set %2$s as an admin</string>
    <string name="group_chat_dismiss_admin1">%1$s dismissed you as an admin</string>
    <string name="group_chat_dismiss_admin2">%1$s dismissed %2$s as an admin</string>
    <string name="group_chat_removed1">%1$s removed you</string>
    <string name="group_chat_removed2">%1$s removed %2$s</string>
    <string name="group_chat_added_member1">%1$s added you to the group</string>
    <string name="group_chat_added_member2">%1$s added %2$s to the group</string>
    <string name="group_chat_left_note1">You left the group</string>
    <string name="group_chat_left_note2">%1$s left the group</string>
    <string name="group_chat_dismiss_note">You can not dismiss %1$s as an admin because they own
        this group.
    </string>
    <string name="group_chat_remove_note">You can not remove %1$s because they own this group.
    </string>
    <string name="group_chat_decline_invitation1">%1$s declined your group invitation</string>
    <string name="group_chat_decline_invitation2">You can only add your friends to the group. Please
        send a friend request to %1$s first.
    </string>
    <string name="group_chat_wrong_version">Unable to add %1$s because their app version does not
        support group chat.
    </string>
    <string name="leave_and_delete_group_content">Leave this group and delete its chat history?
    </string>
    <string name="group_chat_admin_leave">There is no other admin in the group. If you leave, the
        system will automatically set the first group member as the admin.
    </string>
    <string name="group_chat_cant_edit_info">You can’t edit it because you are no longer a member of
        the group.
    </string>

    <string name="group_chat_privacy_add_from_group">Allow friend requests from group chats</string>
    <string name="group_chat_member_invitations">Request to join has been sent for approval.
    </string>
    <string name="common_block_note">Block %1$s to not receive any direct messages or requests from
        this user. You and %2$s will still be able to interact inside of the group chat.
    </string>

    <string name="common_accepted">Accepted</string>
    <string name="common_declined">Declined</string>
    <string name="common_expired">Expired</string>

    <string name="group_chat_delete_group">Delete Group</string>
    <string name="group_chat_sync_wait">Waiting for group members to accept your request.</string>
    <string name="group_chat_pattern_save_member">Saved: %1$s members</string>
    <string name="group_chat_accept_members">Accepted: %1$s members</string>
    <string name="group_chat_sync_from">From %1$s</string>
    <string name="group_chat_unable_add_from_group">%1$s does not allow friend requests from group
        chats.
    </string>
    <string name="group_chat_set_controller">Set as controller</string>
    <string name="group_edit_group_name">Edit Group Name</string>
    <string name="group_edit_nickname">Edit My Nickname in the Group</string>
    <string name="group_edit_remark">Edit Remark</string>
    <string name="group_chat_user_unavailable">Unable to add %1$s to this group because their
        account has been deleted.
    </string>
    <string name="group_member">Member</string>

    <string name="group_chat_network_unstable">The controller’s network is unstable and is
        reconnecting…
    </string>
    <string name="group_chat_controller">Controller</string>
    <string name="group_chat_join_sync">Do you want to join Sync control (%1$s)?</string>
    <string name="common_join">Join</string>
    <string name="common_busy">Busy</string>
    <string name="group_chat_sync_end_note">Sync control will end for all members. End now?</string>
    <string name="group_chat_controller_transfer_successed">Transfer successful.</string>
    <string name="group_chat_controller_transfer_failed">Connection error with %1$s. Unable to
        transfer control. Please choose someone else.
    </string>
    <string name="group_chat_in_control_tip">Your toy’s motions will cause the participants’ toys to
        react.
    </string>
    <string name="group_chat_sync_invitation">invites you to join group Sync control.</string>
    <string name="group_chat_sync_in_call">%1$s other members invited to Sync control.</string>
    <string name="group_chat_joined_sync">%1$s joined the Sync control.</string>
    <string name="group_support_100people">Only supports groups of up to 500 people</string>
    <string name="group_user_create_50groups">Each user can create up to 50 groups</string>
    <string name="group_chat_none_members">None of the members you selected can be added to the
        group, please select another member
    </string>
    <string name="group_chat_stranger_member">%1$s is not in your friends list. Please mind your
        privacy and security.
    </string>
    <string name="common_block">You have blocked %1$s.</string>
    <string name="group_chat_disbanded">This group has been disbanded</string>
    <string name="group_end_sync_control">The sync control ended.</string>
    <string name="group_start_sync_sontrol">%1$s started the sync control</string>
    <string name="group_chat_left_note3">%1$s set %2$s as an admin, %3$s left the group</string>
    <string name="group_room_sync_failed">Sync control failed to start. Error [%1$s].</string>
    <string name="group_sync_in_progress">Sync control is already in progress in this group. You can
        choose to join it.
    </string>
    <string name="group_alarm_note">Alarm received. Please connect a toy to save the alarm.</string>
    <string name="people_group_chat">Group Chat</string>
    <string name="group_name_restrict_special_note">Group name cannot contain special characters.
    </string>
    <string name="group_nickname_restrict_special_note">Your nickname in the group cannot contain
        special characters.
    </string>
    <string name="group_nickname_restrict_note">Your nickname in the group cannot be empty.</string>
    <string name="group_remark_restrict_special_note">Remark cannot contain special characters.
    </string>
    <string name="group_chat_control_member">%1$s is controlling %2$s members</string>
    <string name="group_chat_sync_request_onhold">Waiting for %1$s to accept…</string>
    <string name="leave_and_delete_group_title">Leave and Delete Group</string>
    <string name="group_chat_not_member_note1">You can’t send messages to this group because you are
        no longer a member.
    </string>
    <string name="common_added_by">Added by %1$s.</string>


    <string name="offline_terms_note">By tapping Offline Control, you agree to our %1$s and %2$s.
    </string>
    <string name="new_terms_title">Welcome back!</string>
    <string name="new_terms_notification1">We’ve updated our %1$s. Please review.\n\nI agree to the
        changes in order to continue using Lovense Remote.
    </string>
    <string name="new_terms_notification2">We’ve updated our %1$s and %2$s. Please review.\n\nI
        agree to the changes in order to continue using Lovense Remote.
    </string>
    <string name="button_disagree_and_leave">Disagree and Leave</string>
    <string name="privacy_policy1">Privacy Policy</string>
    <string name="terms_and_conditions1">Terms &amp; Conditions</string>

    <string name="forgot_lock_passcode">Forgot passcode? Login to change passcode.</string>
    <string name="lock_biometric_recognition">Biometric recognition</string>
    <string name="lock_use_biometric_credential">Use biometric credential to unlock app</string>
    <string name="lock_change_passcode">Change Passcode</string>

    <string name="toy_name_error">You can only use letters, numbers, underscore, and space.</string>
    <string name="security_tip_title">Security Tip</string>
    <string name="security_tip_change_button">Change Now</string>
    <string name="security_tip_note">We have identified that your password is weak or has not been
        modified for a long time.
        \n\nPlease update your password to ensure the security of your account.
    </string>


    <string name="scan_check_you_internet">Please check your Internet connection and scan the QR
        code again.
    </string>
    <string name="lan_api_connect_service_error">Failed to connect to the server, please scan the QR
        code again.
    </string>
    <string name="lan_api_connect_toy">Connect Toy</string>
    <string name="lan_api_buy_toy">Buy Lovense Toy Now</string>
    <string name="lan_api_connect_toy_first">Please connect your toy first.</string>
    <string name="lan_api_authorizes_control">Authorize %1$s to control your toys?</string>
    <string name="lan_api_scan_error">Scan QR code error.</string>
    <string name="lan_api_stop">Stop allowing control from %1$s?</string>
    <string name="lan_api_control">Toys controlled by %1$s</string>
    <string name="lan_api_scan_try_again">Try Again</string>
    <string name="lan_api_need_update_toy">Firmware upgrade required for the following toy(s)
    </string>

    <string name="permission_buletooth_forground">Android 6.0 and above requires access to location
        permissions to connect Bluetooth devices. In order to connect your toys, please grant the
        app location permissions.
    </string>
    <string name="button_continue">Continue</string>
    <string name="permission_buletooth_background">Due to your location permissions, your toys will
        be unable to reconnect when the app is running in the background. Please tap settings to
        change permissions.
    </string>
    <string name="button_settings">Settings</string>
    <string name="button_go_to_settings">Go to settings</string>
    <string name="notification_bar_toy_disconneted_title">%1$s disconnected</string>
    <string name="notification_bar_toy_disconneted">Tap here to allow reconnection in the
        background.
    </string>
    <string name="buletooth_background_guide">In order for toys to be reconnected when Lovense
        Remote is running in the background, you must follow these instructions and choose “Allow
        all the time” for the location permission.
    </string>

    <string name="group_chat_qr_code">Group QR code</string>
    <string name="group_chat_qr_des">QR code will be valid for 7 days. A new QR code is generated
        each time you enter this page.
    </string>
    <string name="group_chat_qr_scan_error3">This group is full now. Unable to join.</string>
    <string name="group_chat_qr_scan_error1">This group has been disbanded. Unable to join.</string>
    <string name="group_chat_qr_scan_error2">This is a private group. You must be invited by the
        group admin.
    </string>
    <string name="group_chat_joined">%1$s has joined the group.</string>
    <string name="group_chat_qr_scan_error4">Group QR code has expired. Please scan an updated
        code.
    </string>
    <string name="group_chat_joined_by_qr">Join by QR code</string>
    <string name="group_chat_welcome_des">Group members can send you friend requests by default. If
        you want to disable this, please go to settings.
    </string>
    <string name="group_chat_welcome">Welcome to the Group Chat</string>
    <string name="common_change">Change</string>
    <string name="group_chat_join_button">Join Group Chat</string>
    <string name="connect_to_mirror">Connect to Mirror Life?</string>
    <string name="connect_to_mirror_error">Failed to connect, please try again.</string>
    <string name="sync_panel_mirror">Mirror</string>
    <string name="qr_scan_default">Your app version is too low to scan this QR code. Please update
        the app.
    </string>


    <string name="toy_settings_pin">Security PIN</string>
    <string name="pin_note">Enabling a security PIN is not required and is mostly targeted for more
        secure public play. \n\nThe security PIN is a secure way to connect your toy. Once enabled,
        malicious parties within physical Bluetooth range will be unable to hijack the toy\'s
        connection. If you set a security PIN, you must remember it in order to connect the toy to
        our apps.
    </string>
    <string name="pin_enale_tip_title">Enable this setting carefully</string>
    <string name="pin_enale_tip_des">Once enabled, all Lovense apps will require the security PIN in
        order to connect to this toy. This may complicate your user experience. \n\nIf you forget
        the security PIN for this toy, hold the toy\'s power button down for 10s to disable and
        erase the security PIN.
    </string>
    <string name="pin_set_pin">Set security PIN</string>
    <string name="pin_set_pin_title">Please enter 6 numbers</string>
    <string name="pin_set_pin_error">Pin code do not match</string>
    <string name="pin_set_pin_success">Security PIN has been set to &lt;font color=\'#FF2D89\'&gt;%1$s
        &lt;/font&gt;. The PIN will be required when connected to an app for the first time. &lt;br&gt;&lt;br&gt;Please
        remember this toy\'s PIN. &lt;br&gt;Please power the toy back on for the new setting to take
        effect.
    </string>
    <string name="pin_change_failed">Please make sure your toy is connected when you set the
        security PIN.
    </string>
    <string name="pin_change_pin">Change Security PIN</string>
    <string name="pin_connection_title">Security PIN connection</string>
    <string name="pin_connection_des1">The security PIN is enabled for this toy. Connection may
        require you to enter the PIN in the next window. Please power on the toy before connecting.
    </string>
    <string name="pin_connection_des2">Your security PIN may be required to connect to this toy.
        Enter it in the next window if necessary.
    </string>
    <string name="pin_cant_connect">Can’t connect?</string>

    <string name="pin_cant_connect_des_reasons">Reasons:</string>
    <string name="pin_cant_connect_des_1">Unable to connect?</string>
    <string name="pin_cant_connect_des_1_reasons_1">1. Security PIN is incorrect.</string>
    <string name="pin_cant_connect_des_1_reasons_2">2. You have changed the security PIN.</string>
    <string name="pin_cant_connect_des_2">If the PIN is invalid or forgotten, you may try the
        following solution.
    </string>
    <string name="pin_cant_connect_des_2_reasons_1">1. Hold the toy\'s power button for 10s to
        disable security PIN connection. After 10s, the toy\'s light will quickly flash 3 times and
        the toy will power off.
    </string>
    <string name="pin_cant_connect_des_2_reasons_2">2. Go to the your system’s Bluetooth manager,
        find your toy and forget/unpair it.
    </string>
    <string name="pin_cant_connect_des_2_reasons_3">3. Reconnect the toy using this app. The
        security PIN will no longer be required.
    </string>

    <string name="pin_connect_again">Connect again</string>
    <string name="common_reseted">Reseted</string>
    <string name="pin_disabled_ios">You have disabled connecting this toy using a security PIN.
        Please follow the instructions below to unpair the device before attempting to reconnect.
    </string>
    <string name="pin_disabled_or_changed">You have changed/disabled the security PIN. Please follow
        the instructions below to unpair the device before attempting to reconnect.
    </string>
    <string name="pin_disabled_successed">You have disabled connecting this toy using a security
        PIN. \nPlease power the toy back on for the new setting to take effect.
    </string>
    <string name="enter_pin_code_again">Please enter the 6 numbers again</string>
    <string name="pin_set_success">Success</string>
    <string name="pin_set_failed">Failed</string>

    <string name="notif_titile">Please Verify Your Email Address</string>
    <string name="notif_old_body1">To improve your account security, we request all our users to
        verify their email address. We sent an email to %1$s.
    </string>
    <string name="notif_old_body2">Please check your inbox and click on the link within 24 hours to
        complete your verification.
    </string>
    <string name="notif_old_body3">If you don’t see it, please check your spam folder or resend.
    </string>
    <string name="button_resend">resend</string>
    <string name="button_edit">Edit Email Address</string>
    <string name="button_login">Go to Login</string>
    <string name="button_skip">Skip</string>
    <string name="edit_email">Please enter your Email address</string>
    <string name="notif_note1">*A new email has been sent to your mailbox.</string>
    <string name="notif_note2">*Please wait 1 minute and try again.</string>

    <string name="f01_ready">Ready</string>
    <string name="f01_not_ready">Not ready</string>
    <string name="f01_notice">Rotate the dial to the “Off” position to allow Lovense software to control %1$s</string>
    <string name="f01_ready_notice">%1$s is being controlled by Lovense software.</string>
    <string name="dont_remind_me">Don’t remind me</string>
    <string name="got_it">Got it</string>

    <string name="sync_conflict_toast">Sync control is currently active. You must end it first.
    </string>
    <string name="live_conflict_toast">Live control is currently active. You must end it first.
    </string>
    <string name="video_conflict_toast">Video call is currently active. You must end it first.
    </string>
    <string name="voice_conflict_toast">Voice call is currently active. You must end it first.
    </string>
    <string name="ds_control_conflict_with_ds">D&amp;S control is currently active. You must end it
        first.
    </string>
    <string name="enable_floating_window_title">Floating windows are not enabled</string>
    <string name="enable_floating_window_des">Floating window permission is required to minimize the
        control interface. Tap the button below to change permissions.
    </string>
    <string name="sync_controller_notification">%1$s has switch the control power to you, please
        start to control.
    </string>
    <string name="live_controller_notification">%1$s has just connected their toy! You may control
        their toy now.
    </string>
    <string name="button_not_now">Not now</string>
    <string name="button_not_now_1">Not now</string>
    <string name="dont_support_a_feature">This feature isn\'t yet supported for the platform that
        %1$s is using.
    </string>
    <string name="notif_new_body1">You’re almost there! We sent an email to %1$s.</string>

    <string name="enter_qr_code">Input the unique code provided by the host.</string>
    <string name="lush3_campaign_no_toy">You need to connect your toy(s) before joinning the Lush 3
        sync control.
    </string>
    <string name="lush3_campaign_is_over">The Lush 3 sync control is over, unable to join.</string>
    <string name="lush3_campaign_join_notification">Do you want to join the Lush 3 sync control
        now?
    </string>
    <string name="lush3_campaign_waiting">The Lush 3 sync control will start in</string>
    <string name="lush3_campaign_running">The Lush 3 sync control will end in</string>
    <string name="lush3_campaign_ended">Lush 3 sync control is over. Thanks for participating!
    </string>
    <string name="run_app_in_foreground">Please do not run the app in the background or you may lose
        connection.
    </string>
    <string name="common_reconnecting">Reconnecting</string>
    <string name="qr_code_expired">The QR code expired.</string>

    <string name="function_suction">Suction</string>
    <string name="function_fingering">Fingering</string>
    <string name="vibe_no_toy">You need to connect your toy(s) in order to vibe with the model
    </string>
    <string name="vibe_join_notification">Are you sure you want to vibe with model’s toy?</string>
    <string name="vibe_network_error">Unable to establish a connection with the model. Please check
        your network and try again.
    </string>
    <string name="vibe_not_start">The model has not started the show</string>
    <string name="vibe_disable">The model has disabled this feature</string>
    <string name="invalid_unique_code">Invalid unique code</string>
    <string name="invalid_qr_code">Invalid QR code</string>
    <string name="not_found_model">Model not found</string>
    <string name="vibe_running">You are now vibing with model’s toy</string>
    <string name="vibe_end_notification">model’s show had ended.</string>
    <string name="vibe_title">Vibe with me</string>

    <!--    new ui  -->

    <string name="music_my_library">My Library</string>
    <string name="music_my_library_empty_andriod">The songs you download on this phone will be shown
        here.
    </string>
    <string name="alarm_instruction_des">Create one or multiple alarms for your toy. When the alarm
        goes off, your toy will vibrate according to the selected pattern and duration that you
        choose. Patterns sent by users that you have saved will also appear here.
    </string>
    <string name="patterns_mine_empty">Patterns you shared will show up here.</string>

    <string name="friends_new_requests">New Friend Requests</string>
    <string name="me_share_mood_des">Share your mood message here.</string>
    <string name="login_title">LOGIN</string>
    <string name="login_des">Log in with Lovense Account</string>
    <string name="login_forget_password">Forgot Password?</string>
    <string name="sign_up_title">SIGN UP</string>
    <string name="sign_up_des">Create a Lovense Account</string>
    <string name="search_all_friends">All Friends</string>
    <string name="search_requests">Requests</string>
    <string name="search_blocked">Blocked</string>
    <string name="common_search_requests_new">New</string>
    <string name="common_search_requests_others">Others</string>
    <string name="common_see_all">See All</string>
    <string name="sound_adjust_sensitivity">Adjust the sensitivity</string>
    <string name="music_instruction_title">Spotify Unavailable</string>
    <string name="music_instruction_des">As of September 1, 2022, Spotify has discontinued support
        for third-party apps. Lovense Remote is no longer able to play Spotify music.
    </string>
    <string name="settings_dark_mode">Dark Mode</string>
    <string name="settings_dark_mode_system">System</string>
    <string name="settings_dark_mode_system_des">The app will follow your system’s settings.
    </string>


    <string name="common_Recommended">Recommended</string>
    <string name="patterns_latest">Popular</string>
    <string name="common_liked">Liked</string>
    <string name="common_Mine">Mine</string>

    <string name="login_spotify_des">We support to sync with Spotify music</string>
    <string name="login_spotify_button">Log in Spotify</string>

    <string name="sound_db_input">dB level</string>
    <string name="common_patterns_filter">Filter</string>
    <string name="common_patterns_toys">Toys</string>
    <string name="common_patterns_time">Time</string>
    <string name="patterns_durations_more_than">Duration</string>
    <string name="filter_clear_button">Clear</string>
    <string name="common_patterns_time_today">Today</string>
    <string name="common_patterns_time_1week">Last 7 days</string>
    <string name="common_patterns_time_1month">Last 30 days</string>
    <string name="common_patterns_time_3months">Last 3 months</string>
    <string name="common_patterns_time_6months">Last 6 months</string>
    <string name="common_patterns_time_before2018">Before 2018</string>
    <string name="main_menu_home">Home</string>

    <string name="control_link_title">Give control of your toy</string>
    <string name="privacy_group_friend_request">Group Chat Friend Requests</string>
    <string name="group_chat_add_you_enable">Allow friend request from group chats</string>
    <string name="new_ui_survey_notification_title">Thanks for joining the beta test!</string>
    <string name="button_no_thanks">No, thanks</string>
    <string name="button_give_feedback_now">Give feedback now</string>
    <string name="new_ui_survey_notification_des">We would really appreciate your feedback, so we
        can make the new Lovense Remote even better before the official release.
    </string>
    <string name="common_notice">Notice</string>
    <string name="recommend_to_enable_dark_mode">Lovense Remote supports dark mode now, want to try
        it?
    </string>
    <string name="button_enable_dark_mode">Enable dark mode</string>

    <string name="pattern_store_des">Play a pattern and let your toy react to it. Patterns shared by
        other users will show up here. Log in or sign up to use this feature.
    </string>
    <string name="long_distance_des">Control your friend\'s toy(s), let them control yours, or sync
        your toys together - no matter where you are. Log in or sign up to use this feature.
    </string>
    <string name="setting_offline_note">Log in to adjust more settings.</string>
    <string name="offline_notification">Please login first.</string>
    <string name="email_hint">Enter your email address</string>
    <string name="offline_add_people">Please log in first before adding people.</string>
    <string name="offline_new_group">Please log in first before creating a group.</string>
    <string name="offline_scan_qr">Please log in first before scanning QR code.</string>
    <string name="offline_control_link">Please log in first to make a control link.</string>
    <string name="vibe_running_1">You are now vibing with %1$s’s toy</string>
    <string name="friend_request_page_empty">You have no friend requests.</string>
    <string name="blocked_page_empty">You haven’t blocked anyone.</string>
    <string name="friend_list_empty">Add a friend to control their toy, be controlled, or explore
        other features!
    </string>
    <string name="friend_list_page_empt">You have no friends yet.</string>
    <string name="string_hava_regist">This email address is tied to a deleted account. Please use
        another email address to sign up
    </string>
    <string name="system_people_text">People</string>
    <string name="home_music_empty">Supports locally stored music and Spotify Premium.</string>
    <string name="patterns_liked_empty">Patterns you liked will show up here.</string>
    <string name="enable_music_floating_window_des">Floating window permission is required to
        minimize the music interface. Tap the button below to change permissions.
    </string>

    <string name="group_chat_menu_ds">D&amp;S</string>
    <string name="group_sync_control_is_on">Sync control is running in this group.</string>
    <string name="group_ds_control_is_on">D&amp;S control is running in this group.</string>
    <string name="ds_choose_a_sub">Choose a sub</string>
    <string name="ds_unsupported_version">User has an old version of the app. Please ask them to
        update Lovense Remote to use this feature.
    </string>
    <string name="ds_button_next">Next</string>
    <string name="ds_choose_doms">Choose doms</string>
    <string name="ds_reach_limits">D&amp;S supports up to 300 users.</string>
    <string name="ds_creator_waiting_window">Waiting for group members to accept your request.
    </string>
    <string name="ds_sub">Sub</string>
    <string name="ds_control_started">%1$s started D&amp;S control.</string>
    <string name="ds_control_ended">D&amp;S control has ended.</string>
    <string name="ds_dom_waiting_window">%1$s invites you to be a dom in this D&amp;S control
        session.
    </string>
    <string name="ds_panels_sub_poor_network">The sub’s network is unstable.</string>
    <string name="ds_toast_kicked_out">You left D&amp;S control due to poor network connection.
    </string>
    <string name="ds_panels_your_network_poor">Network connection unstable.</string>
    <string name="ds_ended_because_of_network">Sub’s connection is unstable. D&amp;S control session
        ended.
    </string>
    <string name="ds_waiting_panel_duration_tip">Each control session lasts %1$s</string>
    <string name="ds_waiting_panel_order">You are the 6th dom.</string>
    <string name="ds_panel_button_switch">Switch to next</string>
    <string name="ds_controlling_bar_folded1">Waiting for the sub(%1$s) to accept the request %2$s
    </string>
    <string name="ds_controlling_bar_folded2">%1$s is controlling %2$s in D&amp;S control %3$s
    </string>
    <string name="ds_controlling_bar_folded3">You are controlling %1$s in D&amp;S control %2$s
    </string>
    <string name="ds_controlling_bar_unfolded1">You can join once the sub(%1$s) accepts the
        request.
    </string>
    <string name="ds_controlling_bar_unfolded2">Do you want to join D&amp;S to control %1$s(%2$s)?
    </string>
    <string name="ds_turn_to_control_notification">It’s your turn to control %1$s. Proceed to
        control?
    </string>
    <string name="ds_sub_waiting_window">%1$s invites you to be the sub of this D&amp;S control.
    </string>
    <string name="ds_sub_panel_duration_button">Control Duration %1$s</string>
    <string name="ds_sub_panel_dom_is_offline">%1$s is offline. Switch to the next dom or wait.
    </string>
    <string name="ds_no_available_sub">The sub must connect a toy. There are no available subs. You
        may connect a toy to become the sub.
    </string>
    <string name="ds_no_available_dom1">All group members are offline.</string>
    <string name="ds_no_available_dom2">All other group members are offline. You’re the only dom.
    </string>


</resources>



