package fd;

import aw.n;
import com.google.android.exoplayer2.text.ttml.TtmlNode;
import com.wear.bean.ProgramPattern;
import ew.c2;
import ew.h1;
import ew.h2;
import ew.n0;
import ew.r2;
import ew.w2;
import fd.LightColorDTO;
import hd.LightColorGroup;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import kotlin.Deprecated;
import kotlin.DeprecationLevel;
import kotlin.Metadata;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.JvmField;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.SourceDebugExtension;
import kotlinx.serialization.UnknownFieldException;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* compiled from: LightColorGroupDTO.kt */
@n
@Metadata(d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u0014\b\u0087\b\u0018\u0000 52\u00020\u0001:\u0002%\u001bB;\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0004\u0012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\u0007\u0012\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\b0\u0007¢\u0006\u0004\b\u000b\u0010\fBW\b\u0010\u0012\u0006\u0010\u000e\u001a\u00020\r\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\b\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0004\u0012\u000e\u0010\t\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007\u0012\u000e\u0010\n\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007\u0012\b\u0010\u0010\u001a\u0004\u0018\u00010\u000f¢\u0006\u0004\b\u000b\u0010\u0011J'\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u0012\u001a\u00020\u00002\u0006\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0016\u001a\u00020\u0015H\u0001¢\u0006\u0004\b\u0018\u0010\u0019J\r\u0010\u001b\u001a\u00020\u001a¢\u0006\u0004\b\u001b\u0010\u001cJ\u0010\u0010\u001d\u001a\u00020\u0004HÖ\u0001¢\u0006\u0004\b\u001d\u0010\u001eJ\u0010\u0010\u001f\u001a\u00020\rHÖ\u0001¢\u0006\u0004\b\u001f\u0010 J\u001a\u0010#\u001a\u00020\"2\b\u0010!\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b#\u0010$R\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b%\u0010&\u001a\u0004\b'\u0010(R\u0017\u0010\u0005\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b\u001b\u0010)\u001a\u0004\b*\u0010\u001eR\u0017\u0010\u0006\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b\u0018\u0010)\u001a\u0004\b+\u0010\u001eR(\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\u00078\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b,\u0010-\u001a\u0004\b.\u0010/\"\u0004\b0\u00101R(\u0010\n\u001a\b\u0012\u0004\u0012\u00020\b0\u00078\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b2\u0010-\u001a\u0004\b3\u0010/\"\u0004\b4\u00101¨\u00066"}, d2 = {"Lfd/b;", "", "", TtmlNode.ATTR_ID, "", "groupKey", "groupName", "", "Lfd/a;", "displayColors", "colors", "<init>", "(JLjava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;)V", "", "seen0", "Lew/r2;", "serializationConstructorMarker", "(IJLjava/lang/String;Ljava/lang/String;Ljava/util/List;Ljava/util/List;Lew/r2;)V", "self", "Ldw/d;", "output", "Lcw/f;", "serialDesc", "", "c", "(Lfd/b;Ldw/d;Lcw/f;)V", "Lhd/b;", r0.b.f37717b, "()Lhd/b;", "toString", "()Ljava/lang/String;", "hashCode", "()I", "other", "", "equals", "(Ljava/lang/Object;)Z", "a", "J", "getId", "()J", "Ljava/lang/String;", "getGroupKey", "getGroupName", "d", "Ljava/util/List;", "getDisplayColors", "()Ljava/util/List;", "setDisplayColors", "(Ljava/util/List;)V", "e", "getColors", "setColors", "Companion", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
@SourceDebugExtension({"SMAP\nLightColorGroupDTO.kt\nKotlin\n*S Kotlin\n*F\n+ 1 LightColorGroupDTO.kt\ncom/remotekmp/lightEffect/data/entities/LightColorGroupDTO\n+ 2 _Collections.kt\nkotlin/collections/CollectionsKt___CollectionsKt\n*L\n1#1,33:1\n1557#2:34\n1628#2,3:35\n1557#2:38\n1628#2,3:39\n*S KotlinDebug\n*F\n+ 1 LightColorGroupDTO.kt\ncom/remotekmp/lightEffect/data/entities/LightColorGroupDTO\n*L\n30#1:34\n30#1:35,3\n31#1:38\n31#1:39,3\n*E\n"})
/* renamed from: fd.b, reason: from toString */
/* loaded from: classes4.dex */
public final /* data */ class LightColorGroupDTO {

    /* renamed from: Companion, reason: from kotlin metadata */
    @NotNull
    public static final Companion INSTANCE = new Companion(null);

    /* renamed from: f */
    @JvmField
    @NotNull
    public static final aw.c<Object>[] f30605f;

    /* renamed from: a, reason: from kotlin metadata and from toString */
    public final long id;

    /* renamed from: b, reason: from toString */
    @NotNull
    public final String groupKey;

    /* renamed from: c, reason: from kotlin metadata and from toString */
    @NotNull
    public final String groupName;

    /* renamed from: d, reason: from kotlin metadata and from toString */
    @NotNull
    public List<LightColorDTO> displayColors;

    /* renamed from: e, reason: from kotlin metadata and from toString */
    @NotNull
    public List<LightColorDTO> colors;

    /* compiled from: LightColorGroupDTO.kt */
    @Metadata(d1 = {"\u0000:\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\bÇ\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0003\u0010\u0004J\u001d\u0010\t\u001a\u00020\b2\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u0002¢\u0006\u0004\b\t\u0010\nJ\u0015\u0010\r\u001a\u00020\u00022\u0006\u0010\f\u001a\u00020\u000b¢\u0006\u0004\b\r\u0010\u000eJ\u0017\u0010\u0011\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00100\u000f¢\u0006\u0004\b\u0011\u0010\u0012R\u0017\u0010\u0014\u001a\u00020\u00138\u0006¢\u0006\f\n\u0004\b\u0014\u0010\u0015\u001a\u0004\b\u0016\u0010\u0017¨\u0006\u0018"}, d2 = {"com/remotekmp/lightEffect/data/entities/LightColorGroupDTO.$serializer", "Lew/n0;", "Lfd/b;", "<init>", "()V", "Ldw/f;", "encoder", "value", "", ProgramPattern.writePatternChar100, "(Ldw/f;Lfd/b;)V", "Ldw/e;", "decoder", "e", "(Ldw/e;)Lfd/b;", "", "Law/c;", "d", "()[Law/c;", "Lcw/f;", "descriptor", "Lcw/f;", "getDescriptor", "()Lcw/f;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    @Deprecated(level = DeprecationLevel.HIDDEN, message = "This synthesized declaration should not be used directly")
    /* renamed from: fd.b$a */
    public /* synthetic */ class a implements n0<LightColorGroupDTO> {

        /* renamed from: a */
        @NotNull
        public static final a f30611a;

        @NotNull
        private static final cw.f descriptor;

        static {
            a aVar = new a();
            f30611a = aVar;
            h2 h2Var = new h2("com.remotekmp.lightEffect.data.entities.LightColorGroupDTO", aVar, 5);
            h2Var.o(TtmlNode.ATTR_ID, false);
            h2Var.o("groupKey", false);
            h2Var.o("groupName", false);
            h2Var.o("displayColors", false);
            h2Var.o("colors", false);
            descriptor = h2Var;
        }

        @Override // ew.n0
        @NotNull
        public aw.c<?>[] b() {
            return n0.a.a(this);
        }

        @Override // ew.n0
        @NotNull
        public final aw.c<?>[] d() {
            aw.c<?>[] cVarArr = LightColorGroupDTO.f30605f;
            aw.c<?> cVar = cVarArr[3];
            aw.c<?> cVar2 = cVarArr[4];
            w2 w2Var = w2.f30386a;
            return new aw.c[]{h1.f30272a, w2Var, w2Var, cVar, cVar2};
        }

        @Override // aw.b
        @NotNull
        /* renamed from: e */
        public final LightColorGroupDTO a(@NotNull dw.e decoder) {
            int i10;
            String str;
            String str2;
            List list;
            List list2;
            long j10;
            Intrinsics.checkNotNullParameter(decoder, "decoder");
            cw.f fVar = descriptor;
            dw.c b10 = decoder.b(fVar);
            aw.c[] cVarArr = LightColorGroupDTO.f30605f;
            String str3 = null;
            if (b10.o()) {
                long p10 = b10.p(fVar, 0);
                String F = b10.F(fVar, 1);
                String F2 = b10.F(fVar, 2);
                List list3 = (List) b10.H(fVar, 3, cVarArr[3], null);
                list2 = (List) b10.H(fVar, 4, cVarArr[4], null);
                str = F;
                str2 = F2;
                list = list3;
                j10 = p10;
                i10 = 31;
            } else {
                List list4 = null;
                long j11 = 0;
                int i11 = 0;
                boolean z10 = true;
                String str4 = null;
                List list5 = null;
                while (z10) {
                    int h10 = b10.h(fVar);
                    if (h10 == -1) {
                        z10 = false;
                    } else if (h10 == 0) {
                        j11 = b10.p(fVar, 0);
                        i11 |= 1;
                    } else if (h10 == 1) {
                        str3 = b10.F(fVar, 1);
                        i11 |= 2;
                    } else if (h10 == 2) {
                        str4 = b10.F(fVar, 2);
                        i11 |= 4;
                    } else if (h10 == 3) {
                        list5 = (List) b10.H(fVar, 3, cVarArr[3], list5);
                        i11 |= 8;
                    } else {
                        if (h10 != 4) {
                            throw new UnknownFieldException(h10);
                        }
                        list4 = (List) b10.H(fVar, 4, cVarArr[4], list4);
                        i11 |= 16;
                    }
                }
                i10 = i11;
                str = str3;
                str2 = str4;
                list = list5;
                list2 = list4;
                j10 = j11;
            }
            b10.c(fVar);
            return new LightColorGroupDTO(i10, j10, str, str2, list, list2, null);
        }

        @Override // aw.o
        /* renamed from: f */
        public final void c(@NotNull dw.f encoder, @NotNull LightColorGroupDTO value) {
            Intrinsics.checkNotNullParameter(encoder, "encoder");
            Intrinsics.checkNotNullParameter(value, "value");
            cw.f fVar = descriptor;
            dw.d b10 = encoder.b(fVar);
            LightColorGroupDTO.c(value, b10, fVar);
            b10.c(fVar);
        }

        @Override // aw.c, aw.o, aw.b
        @NotNull
        public final cw.f getDescriptor() {
            return descriptor;
        }
    }

    /* compiled from: LightColorGroupDTO.kt */
    @Metadata(d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J\u0013\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004¢\u0006\u0004\b\u0006\u0010\u0007¨\u0006\b"}, d2 = {"Lfd/b$b;", "", "<init>", "()V", "Law/c;", "Lfd/b;", "serializer", "()Law/c;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    /* renamed from: fd.b$b, reason: from kotlin metadata */
    public static final class Companion {
        public Companion() {
        }

        @NotNull
        public final aw.c<LightColorGroupDTO> serializer() {
            return a.f30611a;
        }

        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }
    }

    static {
        LightColorDTO.C0529a c0529a = LightColorDTO.C0529a.f30604a;
        f30605f = new aw.c[]{null, null, null, new ew.f(c0529a), new ew.f(c0529a)};
    }

    public /* synthetic */ LightColorGroupDTO(int i10, long j10, String str, String str2, List list, List list2, r2 r2Var) {
        if (31 != (i10 & 31)) {
            c2.b(i10, 31, a.f30611a.getDescriptor());
        }
        this.id = j10;
        this.groupKey = str;
        this.groupName = str2;
        this.displayColors = list;
        this.colors = list2;
    }

    @JvmStatic
    public static final /* synthetic */ void c(LightColorGroupDTO self, dw.d output, cw.f serialDesc) {
        aw.c<Object>[] cVarArr = f30605f;
        output.E(serialDesc, 0, self.id);
        output.B(serialDesc, 1, self.groupKey);
        output.B(serialDesc, 2, self.groupName);
        output.r(serialDesc, 3, cVarArr[3], self.displayColors);
        output.r(serialDesc, 4, cVarArr[4], self.colors);
    }

    @NotNull
    public final LightColorGroup b() {
        long j10 = this.id;
        String str = this.groupKey;
        String str2 = this.groupName;
        List<LightColorDTO> list = this.displayColors;
        ArrayList arrayList = new ArrayList(CollectionsKt.collectionSizeOrDefault(list, 10));
        Iterator<T> it = list.iterator();
        while (it.hasNext()) {
            arrayList.add(((LightColorDTO) it.next()).a());
        }
        List<LightColorDTO> list2 = this.colors;
        ArrayList arrayList2 = new ArrayList(CollectionsKt.collectionSizeOrDefault(list2, 10));
        Iterator<T> it2 = list2.iterator();
        while (it2.hasNext()) {
            arrayList2.add(((LightColorDTO) it2.next()).a());
        }
        return new LightColorGroup(j10, str, str2, arrayList, arrayList2);
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof LightColorGroupDTO)) {
            return false;
        }
        LightColorGroupDTO lightColorGroupDTO = (LightColorGroupDTO) other;
        return this.id == lightColorGroupDTO.id && Intrinsics.areEqual(this.groupKey, lightColorGroupDTO.groupKey) && Intrinsics.areEqual(this.groupName, lightColorGroupDTO.groupName) && Intrinsics.areEqual(this.displayColors, lightColorGroupDTO.displayColors) && Intrinsics.areEqual(this.colors, lightColorGroupDTO.colors);
    }

    public int hashCode() {
        return (((((((androidx.compose.animation.a.a(this.id) * 31) + this.groupKey.hashCode()) * 31) + this.groupName.hashCode()) * 31) + this.displayColors.hashCode()) * 31) + this.colors.hashCode();
    }

    @NotNull
    public String toString() {
        return "LightColorGroupDTO(id=" + this.id + ", groupKey=" + this.groupKey + ", groupName=" + this.groupName + ", displayColors=" + this.displayColors + ", colors=" + this.colors + ')';
    }

    public LightColorGroupDTO(long j10, @NotNull String groupKey, @NotNull String groupName, @NotNull List<LightColorDTO> displayColors, @NotNull List<LightColorDTO> colors) {
        Intrinsics.checkNotNullParameter(groupKey, "groupKey");
        Intrinsics.checkNotNullParameter(groupName, "groupName");
        Intrinsics.checkNotNullParameter(displayColors, "displayColors");
        Intrinsics.checkNotNullParameter(colors, "colors");
        this.id = j10;
        this.groupKey = groupKey;
        this.groupName = groupName;
        this.displayColors = displayColors;
        this.colors = colors;
    }
}
