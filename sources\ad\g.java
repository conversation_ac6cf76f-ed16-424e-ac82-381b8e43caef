package ad;

import ad.h;
import com.google.android.gms.common.internal.ImagesContract;
import com.google.firebase.analytics.FirebaseAnalytics;
import com.wear.bean.ProgramPattern;
import java.util.Map;
import kotlin.Metadata;
import kotlin.collections.MapsKt;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.SourceDebugExtension;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jivesoftware.smackx.receipts.DeliveryReceiptRequest;
import org.jivesoftware.smackx.shim.packet.HeadersExtension;

/* compiled from: HTTPRequest.kt */
@Metadata(d1 = {"\u0000:\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010$\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0011\b\u0086\b\u0018\u00002\u00020\u0001:\u0001\u0019BM\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0014\b\u0002\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00020\u0006\u0012\b\b\u0002\u0010\t\u001a\u00020\b\u0012\u0014\b\u0002\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00020\u0006¢\u0006\u0004\b\u000b\u0010\fJ\r\u0010\u000e\u001a\u00020\r¢\u0006\u0004\b\u000e\u0010\u000fJ\u001a\u0010\u0012\u001a\u00020\u00112\b\u0010\u0010\u001a\u0004\u0018\u00010\u0001H\u0096\u0002¢\u0006\u0004\b\u0012\u0010\u0013J\u000f\u0010\u0015\u001a\u00020\u0014H\u0016¢\u0006\u0004\b\u0015\u0010\u0016J\u0010\u0010\u0017\u001a\u00020\u0002HÖ\u0001¢\u0006\u0004\b\u0017\u0010\u0018R\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0019\u0010\u001a\u001a\u0004\b\u001b\u0010\u0018R\u0017\u0010\u0005\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b\u001c\u0010\u001d\u001a\u0004\b\u001e\u0010\u001fR#\u0010\u0007\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00020\u00068\u0006¢\u0006\f\n\u0004\b\u001e\u0010 \u001a\u0004\b\u001c\u0010!R\u0017\u0010\t\u001a\u00020\b8\u0006¢\u0006\f\n\u0004\b\u001b\u0010\"\u001a\u0004\b\u0019\u0010#R#\u0010\n\u001a\u000e\u0012\u0004\u0012\u00020\u0002\u0012\u0004\u0012\u00020\u00020\u00068\u0006¢\u0006\f\n\u0004\b$\u0010 \u001a\u0004\b$\u0010!¨\u0006%"}, d2 = {"Lad/g;", "", "", "path", "Lad/f;", FirebaseAnalytics.Param.METHOD, "", HeadersExtension.ELEMENT, "Lad/h;", "body", "queryParams", "<init>", "(Ljava/lang/String;Lad/f;Ljava/util/Map;Lad/h;Ljava/util/Map;)V", "Lad/g$a;", ProgramPattern.writePatternChar100, "()Lad/g$a;", "other", "", "equals", "(Ljava/lang/Object;)Z", "", "hashCode", "()I", "toString", "()Ljava/lang/String;", "a", "Ljava/lang/String;", "d", r0.b.f37717b, "Lad/f;", "c", "()Lad/f;", "Ljava/util/Map;", "()Ljava/util/Map;", "Lad/h;", "()Lad/h;", "e", "core"}, k = 1, mv = {2, 0, 0}, xi = 48)
/* renamed from: ad.g, reason: from toString */
/* loaded from: classes4.dex */
public final /* data */ class HTTPRequest {

    /* renamed from: a, reason: from kotlin metadata and from toString */
    @NotNull
    public final String path;

    /* renamed from: b, reason: from toString */
    @NotNull
    public final f method;

    /* renamed from: c, reason: from kotlin metadata and from toString */
    @NotNull
    public final Map<String, String> headers;

    /* renamed from: d, reason: from kotlin metadata and from toString */
    @NotNull
    public final h body;

    /* renamed from: e, reason: from kotlin metadata and from toString */
    @NotNull
    public final Map<String, String> queryParams;

    /* compiled from: HTTPRequest.kt */
    @Metadata(d1 = {"\u00000\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010%\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u00002\u00020\u0001B\u0011\b\u0000\u0012\u0006\u0010\u0003\u001a\u00020\u0002¢\u0006\u0004\b\u0004\u0010\u0005J\u001d\u0010\t\u001a\u00020\u00002\u0006\u0010\u0007\u001a\u00020\u00062\u0006\u0010\b\u001a\u00020\u0006¢\u0006\u0004\b\t\u0010\nJ\r\u0010\u000b\u001a\u00020\u0002¢\u0006\u0004\b\u000b\u0010\fR\u0016\u0010\u000e\u001a\u00020\u00068\u0002@\u0002X\u0082\u000e¢\u0006\u0006\n\u0004\b\u000b\u0010\rR\u0016\u0010\u0011\u001a\u00020\u000f8\u0002@\u0002X\u0082\u000e¢\u0006\u0006\n\u0004\b\t\u0010\u0010R\"\u0010\u0015\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060\u00128\u0002@\u0002X\u0082\u000e¢\u0006\u0006\n\u0004\b\u0013\u0010\u0014R\"\u0010\u0017\u001a\u000e\u0012\u0004\u0012\u00020\u0006\u0012\u0004\u0012\u00020\u00060\u00128\u0002@\u0002X\u0082\u000e¢\u0006\u0006\n\u0004\b\u0016\u0010\u0014R\u0016\u0010\u001b\u001a\u00020\u00188\u0002@\u0002X\u0082\u000e¢\u0006\u0006\n\u0004\b\u0019\u0010\u001a¨\u0006\u001c"}, d2 = {"Lad/g$a;", "", "Lad/g;", DeliveryReceiptRequest.ELEMENT, "<init>", "(Lad/g;)V", "", "key", "value", r0.b.f37717b, "(Ljava/lang/String;Ljava/lang/String;)Lad/g$a;", "a", "()Lad/g;", "Ljava/lang/String;", ImagesContract.URL, "Lad/f;", "Lad/f;", FirebaseAnalytics.Param.METHOD, "", "c", "Ljava/util/Map;", HeadersExtension.ELEMENT, "d", "queryParams", "Lad/h;", "e", "Lad/h;", "body", "core"}, k = 1, mv = {2, 0, 0}, xi = 48)
    @SourceDebugExtension({"SMAP\nHTTPRequest.kt\nKotlin\n*S Kotlin\n*F\n+ 1 HTTPRequest.kt\ncom/remotekmp/core/http/HTTPRequest$Builder\n+ 2 fake.kt\nkotlin/jvm/internal/FakeKt\n*L\n1#1,96:1\n1#2:97\n*E\n"})
    /* renamed from: ad.g$a */
    public static final class a {

        /* renamed from: a, reason: from kotlin metadata */
        @NotNull
        public String com.google.android.gms.common.internal.ImagesContract.URL java.lang.String;

        /* renamed from: b */
        @NotNull
        public f method;

        /* renamed from: c, reason: from kotlin metadata */
        @NotNull
        public Map<String, String> org.jivesoftware.smackx.shim.packet.HeadersExtension.ELEMENT java.lang.String;

        /* renamed from: d, reason: from kotlin metadata */
        @NotNull
        public Map<String, String> queryParams;

        /* renamed from: e, reason: from kotlin metadata */
        @NotNull
        public h body;

        public a(@NotNull HTTPRequest request) {
            Intrinsics.checkNotNullParameter(request, "request");
            this.com.google.android.gms.common.internal.ImagesContract.URL java.lang.String = request.getPath();
            this.method = request.getMethod();
            this.org.jivesoftware.smackx.shim.packet.HeadersExtension.ELEMENT java.lang.String = MapsKt.toMutableMap(request.b());
            this.queryParams = MapsKt.toMutableMap(request.e());
            this.body = request.getBody();
        }

        @NotNull
        public final HTTPRequest a() {
            return new HTTPRequest(this.com.google.android.gms.common.internal.ImagesContract.URL java.lang.String, this.method, MapsKt.toMap(this.org.jivesoftware.smackx.shim.packet.HeadersExtension.ELEMENT java.lang.String), this.body, MapsKt.toMap(this.queryParams));
        }

        @NotNull
        public final a b(@NotNull String key, @NotNull String value) {
            Intrinsics.checkNotNullParameter(key, "key");
            Intrinsics.checkNotNullParameter(value, "value");
            this.org.jivesoftware.smackx.shim.packet.HeadersExtension.ELEMENT java.lang.String.put(key, value);
            return this;
        }
    }

    public HTTPRequest(@NotNull String path, @NotNull f method, @NotNull Map<String, String> headers, @NotNull h body, @NotNull Map<String, String> queryParams) {
        Intrinsics.checkNotNullParameter(path, "path");
        Intrinsics.checkNotNullParameter(method, "method");
        Intrinsics.checkNotNullParameter(headers, "headers");
        Intrinsics.checkNotNullParameter(body, "body");
        Intrinsics.checkNotNullParameter(queryParams, "queryParams");
        this.path = path;
        this.method = method;
        this.headers = headers;
        this.body = body;
        this.queryParams = queryParams;
    }

    @NotNull
    /* renamed from: a, reason: from getter */
    public final h getBody() {
        return this.body;
    }

    @NotNull
    public final Map<String, String> b() {
        return this.headers;
    }

    @NotNull
    /* renamed from: c, reason: from getter */
    public final f getMethod() {
        return this.method;
    }

    @NotNull
    /* renamed from: d, reason: from getter */
    public final String getPath() {
        return this.path;
    }

    @NotNull
    public final Map<String, String> e() {
        return this.queryParams;
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (other == null || HTTPRequest.class != other.getClass()) {
            return false;
        }
        HTTPRequest hTTPRequest = (HTTPRequest) other;
        return Intrinsics.areEqual(this.path, hTTPRequest.path) && this.method == hTTPRequest.method && Intrinsics.areEqual(this.headers, hTTPRequest.headers) && Intrinsics.areEqual(this.queryParams, hTTPRequest.queryParams) && Intrinsics.areEqual(this.body, hTTPRequest.body);
    }

    @NotNull
    public final a f() {
        return new a(this);
    }

    public int hashCode() {
        return (((((((this.path.hashCode() * 31) + this.method.hashCode()) * 31) + this.headers.hashCode()) * 31) + this.queryParams.hashCode()) * 31) + this.body.hashCode();
    }

    @NotNull
    public String toString() {
        return "HTTPRequest(path=" + this.path + ", method=" + this.method + ", headers=" + this.headers + ", body=" + this.body + ", queryParams=" + this.queryParams + ')';
    }

    public /* synthetic */ HTTPRequest(String str, f fVar, Map map, h hVar, Map map2, int i10, DefaultConstructorMarker defaultConstructorMarker) {
        this(str, fVar, (i10 & 4) != 0 ? MapsKt.emptyMap() : map, (i10 & 8) != 0 ? h.b.f186a : hVar, (i10 & 16) != 0 ? MapsKt.emptyMap() : map2);
    }
}
