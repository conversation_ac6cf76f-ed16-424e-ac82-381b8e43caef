package hd;

import aw.n;
import com.wear.bean.ProgramPattern;
import ew.c2;
import ew.h1;
import ew.h2;
import ew.n0;
import ew.r2;
import ew.w0;
import kotlin.Deprecated;
import kotlin.DeprecationLevel;
import kotlin.Metadata;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlinx.serialization.UnknownFieldException;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* compiled from: LightGear.kt */
@n
@Metadata(d1 = {"\u0000B\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\t\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\n\b\u0087\b\u0018\u0000 $2\u00020\u0001:\u0002\u001e B\u001f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0002¢\u0006\u0004\b\u0007\u0010\bB3\b\u0010\u0012\u0006\u0010\t\u001a\u00020\u0002\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0002\u0012\b\u0010\u000b\u001a\u0004\u0018\u00010\n¢\u0006\u0004\b\u0007\u0010\fJ'\u0010\u0013\u001a\u00020\u00122\u0006\u0010\r\u001a\u00020\u00002\u0006\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0011\u001a\u00020\u0010H\u0001¢\u0006\u0004\b\u0013\u0010\u0014J\u0010\u0010\u0016\u001a\u00020\u0015HÖ\u0001¢\u0006\u0004\b\u0016\u0010\u0017J\u0010\u0010\u0018\u001a\u00020\u0002HÖ\u0001¢\u0006\u0004\b\u0018\u0010\u0019J\u001a\u0010\u001c\u001a\u00020\u001b2\b\u0010\u001a\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\u001c\u0010\u001dR\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u001e\u0010\u001f\u001a\u0004\b \u0010\u0019R\u0017\u0010\u0005\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b \u0010!\u001a\u0004\b\"\u0010#R\u0017\u0010\u0006\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0013\u0010\u001f\u001a\u0004\b\u001e\u0010\u0019¨\u0006%"}, d2 = {"Lhd/e;", "", "", "gear", "", "modeId", "frequency", "<init>", "(IJI)V", "seen0", "Lew/r2;", "serializationConstructorMarker", "(IIJILew/r2;)V", "self", "Ldw/d;", "output", "Lcw/f;", "serialDesc", "", "c", "(Lhd/e;Ldw/d;Lcw/f;)V", "", "toString", "()Ljava/lang/String;", "hashCode", "()I", "other", "", "equals", "(Ljava/lang/Object;)Z", "a", "I", r0.b.f37717b, "J", "getModeId", "()J", "Companion", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
/* renamed from: hd.e, reason: from toString */
/* loaded from: classes4.dex */
public final /* data */ class LightGear {

    /* renamed from: Companion, reason: from kotlin metadata */
    @NotNull
    public static final Companion INSTANCE = new Companion(null);

    /* renamed from: a, reason: from kotlin metadata and from toString */
    public final int gear;

    /* renamed from: b, reason: from toString */
    public final long modeId;

    /* renamed from: c, reason: from kotlin metadata and from toString */
    public final int frequency;

    /* compiled from: LightGear.kt */
    @Metadata(d1 = {"\u0000:\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\bÇ\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0003\u0010\u0004J\u001d\u0010\t\u001a\u00020\b2\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u0002¢\u0006\u0004\b\t\u0010\nJ\u0015\u0010\r\u001a\u00020\u00022\u0006\u0010\f\u001a\u00020\u000b¢\u0006\u0004\b\r\u0010\u000eJ\u0017\u0010\u0011\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00100\u000f¢\u0006\u0004\b\u0011\u0010\u0012R\u0017\u0010\u0014\u001a\u00020\u00138\u0006¢\u0006\f\n\u0004\b\u0014\u0010\u0015\u001a\u0004\b\u0016\u0010\u0017¨\u0006\u0018"}, d2 = {"com/remotekmp/lightEffect/domain/entities/LightGear.$serializer", "Lew/n0;", "Lhd/e;", "<init>", "()V", "Ldw/f;", "encoder", "value", "", ProgramPattern.writePatternChar100, "(Ldw/f;Lhd/e;)V", "Ldw/e;", "decoder", "e", "(Ldw/e;)Lhd/e;", "", "Law/c;", "d", "()[Law/c;", "Lcw/f;", "descriptor", "Lcw/f;", "getDescriptor", "()Lcw/f;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    @Deprecated(level = DeprecationLevel.HIDDEN, message = "This synthesized declaration should not be used directly")
    /* renamed from: hd.e$a */
    public /* synthetic */ class a implements n0<LightGear> {

        /* renamed from: a */
        @NotNull
        public static final a f31335a;

        @NotNull
        private static final cw.f descriptor;

        static {
            a aVar = new a();
            f31335a = aVar;
            h2 h2Var = new h2("com.remotekmp.lightEffect.domain.entities.LightGear", aVar, 3);
            h2Var.o("gear", false);
            h2Var.o("modeId", false);
            h2Var.o("frequency", false);
            descriptor = h2Var;
        }

        @Override // ew.n0
        @NotNull
        public aw.c<?>[] b() {
            return n0.a.a(this);
        }

        @Override // ew.n0
        @NotNull
        public final aw.c<?>[] d() {
            w0 w0Var = w0.f30382a;
            return new aw.c[]{w0Var, h1.f30272a, w0Var};
        }

        @Override // aw.b
        @NotNull
        /* renamed from: e */
        public final LightGear a(@NotNull dw.e decoder) {
            int i10;
            int i11;
            long j10;
            int i12;
            Intrinsics.checkNotNullParameter(decoder, "decoder");
            cw.f fVar = descriptor;
            dw.c b10 = decoder.b(fVar);
            if (b10.o()) {
                int f10 = b10.f(fVar, 0);
                long p10 = b10.p(fVar, 1);
                i10 = f10;
                i11 = b10.f(fVar, 2);
                j10 = p10;
                i12 = 7;
            } else {
                long j11 = 0;
                int i13 = 0;
                int i14 = 0;
                int i15 = 0;
                boolean z10 = true;
                while (z10) {
                    int h10 = b10.h(fVar);
                    if (h10 == -1) {
                        z10 = false;
                    } else if (h10 == 0) {
                        i13 = b10.f(fVar, 0);
                        i15 |= 1;
                    } else if (h10 == 1) {
                        j11 = b10.p(fVar, 1);
                        i15 |= 2;
                    } else {
                        if (h10 != 2) {
                            throw new UnknownFieldException(h10);
                        }
                        i14 = b10.f(fVar, 2);
                        i15 |= 4;
                    }
                }
                i10 = i13;
                i11 = i14;
                j10 = j11;
                i12 = i15;
            }
            b10.c(fVar);
            return new LightGear(i12, i10, j10, i11, null);
        }

        @Override // aw.o
        /* renamed from: f */
        public final void c(@NotNull dw.f encoder, @NotNull LightGear value) {
            Intrinsics.checkNotNullParameter(encoder, "encoder");
            Intrinsics.checkNotNullParameter(value, "value");
            cw.f fVar = descriptor;
            dw.d b10 = encoder.b(fVar);
            LightGear.c(value, b10, fVar);
            b10.c(fVar);
        }

        @Override // aw.c, aw.o, aw.b
        @NotNull
        public final cw.f getDescriptor() {
            return descriptor;
        }
    }

    /* compiled from: LightGear.kt */
    @Metadata(d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J\u0013\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004¢\u0006\u0004\b\u0006\u0010\u0007¨\u0006\b"}, d2 = {"Lhd/e$b;", "", "<init>", "()V", "Law/c;", "Lhd/e;", "serializer", "()Law/c;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    /* renamed from: hd.e$b, reason: from kotlin metadata */
    public static final class Companion {
        public Companion() {
        }

        @NotNull
        public final aw.c<LightGear> serializer() {
            return a.f31335a;
        }

        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }
    }

    public /* synthetic */ LightGear(int i10, int i11, long j10, int i12, r2 r2Var) {
        if (7 != (i10 & 7)) {
            c2.b(i10, 7, a.f31335a.getDescriptor());
        }
        this.gear = i11;
        this.modeId = j10;
        this.frequency = i12;
    }

    @JvmStatic
    public static final /* synthetic */ void c(LightGear self, dw.d output, cw.f serialDesc) {
        output.z(serialDesc, 0, self.gear);
        output.E(serialDesc, 1, self.modeId);
        output.z(serialDesc, 2, self.frequency);
    }

    /* renamed from: a, reason: from getter */
    public final int getFrequency() {
        return this.frequency;
    }

    /* renamed from: b, reason: from getter */
    public final int getGear() {
        return this.gear;
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof LightGear)) {
            return false;
        }
        LightGear lightGear = (LightGear) other;
        return this.gear == lightGear.gear && this.modeId == lightGear.modeId && this.frequency == lightGear.frequency;
    }

    public int hashCode() {
        return (((this.gear * 31) + androidx.compose.animation.a.a(this.modeId)) * 31) + this.frequency;
    }

    @NotNull
    public String toString() {
        return "LightGear(gear=" + this.gear + ", modeId=" + this.modeId + ", frequency=" + this.frequency + ')';
    }

    public LightGear(int i10, long j10, int i11) {
        this.gear = i10;
        this.modeId = j10;
        this.frequency = i11;
    }
}
