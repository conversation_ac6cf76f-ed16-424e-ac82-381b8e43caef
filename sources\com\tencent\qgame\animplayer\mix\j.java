package com.tencent.qgame.animplayer.mix;

import android.graphics.Bitmap;
import androidx.core.os.EnvironmentCompat;
import com.google.android.gms.common.internal.ImagesContract;
import com.wear.bean.ProgramPattern;
import kl.l;
import kotlin.Metadata;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jivesoftware.smackx.xhtmlim.XHTMLText;

/* compiled from: Src.kt */
@Metadata(d1 = {"\u00008\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\u000e\n\u0002\b\b\n\u0002\u0010\b\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u000f\n\u0002\u0018\u0002\n\u0002\b\b\n\u0002\u0018\u0002\n\u0002\b\b\u0018\u0000 =2\u00020\u0001:\u0004\u0005\f\u0013\u000eJ\u000f\u0010\u0003\u001a\u00020\u0002H\u0016¢\u0006\u0004\b\u0003\u0010\u0004R\"\u0010\n\u001a\u00020\u00028\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\u0005\u0010\u0006\u001a\u0004\b\u0007\u0010\u0004\"\u0004\b\b\u0010\tR\"\u0010\u0012\u001a\u00020\u000b8\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\f\u0010\r\u001a\u0004\b\u000e\u0010\u000f\"\u0004\b\u0010\u0010\u0011R\"\u0010\u0015\u001a\u00020\u000b8\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\u0013\u0010\r\u001a\u0004\b\u0013\u0010\u000f\"\u0004\b\u0014\u0010\u0011R\"\u0010\u001c\u001a\u00020\u00168\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\u000e\u0010\u0017\u001a\u0004\b\u0018\u0010\u0019\"\u0004\b\u001a\u0010\u001bR\"\u0010$\u001a\u00020\u001d8\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\u001e\u0010\u001f\u001a\u0004\b \u0010!\"\u0004\b\"\u0010#R\"\u0010'\u001a\u00020\u00028\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b \u0010\u0006\u001a\u0004\b%\u0010\u0004\"\u0004\b&\u0010\tR\"\u0010*\u001a\u00020\u00028\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\u0007\u0010\u0006\u001a\u0004\b(\u0010\u0004\"\u0004\b)\u0010\tR\"\u0010,\u001a\u00020\u000b8\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b%\u0010\r\u001a\u0004\b\f\u0010\u000f\"\u0004\b+\u0010\u0011R\"\u00103\u001a\u00020-8\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b.\u0010/\u001a\u0004\b\u001e\u00100\"\u0004\b1\u00102R\"\u00105\u001a\u00020\u000b8\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\u0018\u0010\r\u001a\u0004\b.\u0010\u000f\"\u0004\b4\u0010\u0011R.\u0010<\u001a\u0004\u0018\u0001062\b\u00107\u001a\u0004\u0018\u0001068\u0006@FX\u0086\u000e¢\u0006\u0012\n\u0004\b4\u00108\u001a\u0004\b\u0005\u00109\"\u0004\b:\u0010;¨\u0006>"}, d2 = {"Lcom/tencent/qgame/animplayer/mix/j;", "", "", "toString", "()Ljava/lang/String;", "a", "Ljava/lang/String;", "g", "setSrcId", "(Ljava/lang/String;)V", "srcId", "", r0.b.f37717b, "I", "d", "()I", "setDrawWidth", "(I)V", "drawWidth", "c", "setDrawHeight", "drawHeight", "Lcom/tencent/qgame/animplayer/mix/j$d;", "Lcom/tencent/qgame/animplayer/mix/j$d;", "j", "()Lcom/tencent/qgame/animplayer/mix/j$d;", "setSrcType", "(Lcom/tencent/qgame/animplayer/mix/j$d;)V", "srcType", "Lcom/tencent/qgame/animplayer/mix/j$c;", "e", "Lcom/tencent/qgame/animplayer/mix/j$c;", ProgramPattern.writePatternChar100, "()Lcom/tencent/qgame/animplayer/mix/j$c;", "setLoadType", "(Lcom/tencent/qgame/animplayer/mix/j$c;)V", "loadType", XHTMLText.H, "setSrcTag", "srcTag", "getTxt", "setTxt", "txt", "setColor", "color", "Lcom/tencent/qgame/animplayer/mix/j$b;", jl.i.f32548i, "Lcom/tencent/qgame/animplayer/mix/j$b;", "()Lcom/tencent/qgame/animplayer/mix/j$b;", "setFitType", "(Lcom/tencent/qgame/animplayer/mix/j$b;)V", "fitType", jl.k.f32572f, "srcTextureId", "Landroid/graphics/Bitmap;", "value", "Landroid/graphics/Bitmap;", "()Landroid/graphics/Bitmap;", "setBitmap", "(Landroid/graphics/Bitmap;)V", "bitmap", l.f32926l, "animplayer_release"}, k = 1, mv = {1, 4, 0})
/* renamed from: com.tencent.qgame.animplayer.mix.j, reason: from toString */
/* loaded from: classes4.dex */
public final class Src {

    /* renamed from: a, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public String srcId;

    /* renamed from: b, reason: collision with root package name and from kotlin metadata */
    public int drawWidth;

    /* renamed from: c, reason: collision with root package name and from kotlin metadata */
    public int drawHeight;

    /* renamed from: d, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public d srcType;

    /* renamed from: e, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public c loadType;

    /* renamed from: f, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public String srcTag;

    /* renamed from: g, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public String txt;

    /* renamed from: h, reason: collision with root package name and from kotlin metadata */
    public int color;

    /* renamed from: i, reason: collision with root package name and from kotlin metadata */
    @NotNull
    public b fitType;

    /* renamed from: j, reason: collision with root package name and from kotlin metadata */
    public int srcTextureId;

    /* renamed from: k, reason: collision with root package name and from kotlin metadata and from toString */
    @Nullable
    public Bitmap bitmap;

    /* compiled from: Src.kt */
    @Metadata(d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0010\u000e\n\u0002\b\t\b\u0086\u0001\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0003\u001a\u00020\u0002¢\u0006\u0004\b\u0004\u0010\u0005R\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0003\u0010\u0006\u001a\u0004\b\u0007\u0010\bj\u0002\b\tj\u0002\b\n¨\u0006\u000b"}, d2 = {"Lcom/tencent/qgame/animplayer/mix/j$b;", "", "", "type", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "Ljava/lang/String;", "getType", "()Ljava/lang/String;", "a", r0.b.f37717b, "animplayer_release"}, k = 1, mv = {1, 4, 0})
    /* renamed from: com.tencent.qgame.animplayer.mix.j$b */
    public enum b {
        FIT_XY("fitXY"),
        CENTER_FULL("centerFull");


        @NotNull
        private final String type;

        b(String str) {
            this.type = str;
        }
    }

    /* compiled from: Src.kt */
    @Metadata(d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0010\u000e\n\u0002\b\n\b\u0086\u0001\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0003\u001a\u00020\u0002¢\u0006\u0004\b\u0004\u0010\u0005R\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0003\u0010\u0006\u001a\u0004\b\u0007\u0010\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b¨\u0006\f"}, d2 = {"Lcom/tencent/qgame/animplayer/mix/j$c;", "", "", "type", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "Ljava/lang/String;", "getType", "()Ljava/lang/String;", "a", r0.b.f37717b, "c", "animplayer_release"}, k = 1, mv = {1, 4, 0})
    /* renamed from: com.tencent.qgame.animplayer.mix.j$c */
    public enum c {
        UNKNOWN(EnvironmentCompat.MEDIA_UNKNOWN),
        NET("net"),
        LOCAL(ImagesContract.LOCAL);


        @NotNull
        private final String type;

        c(String str) {
            this.type = str;
        }
    }

    /* compiled from: Src.kt */
    @Metadata(d1 = {"\u0000\u0010\n\u0002\u0018\u0002\n\u0002\u0010\u0010\n\u0002\u0010\u000e\n\u0002\b\n\b\u0086\u0001\u0018\u00002\b\u0012\u0004\u0012\u00020\u00000\u0001B\u0011\b\u0002\u0012\u0006\u0010\u0003\u001a\u00020\u0002¢\u0006\u0004\b\u0004\u0010\u0005R\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0003\u0010\u0006\u001a\u0004\b\u0007\u0010\bj\u0002\b\tj\u0002\b\nj\u0002\b\u000b¨\u0006\f"}, d2 = {"Lcom/tencent/qgame/animplayer/mix/j$d;", "", "", "type", "<init>", "(Ljava/lang/String;ILjava/lang/String;)V", "Ljava/lang/String;", "getType", "()Ljava/lang/String;", "a", r0.b.f37717b, "c", "animplayer_release"}, k = 1, mv = {1, 4, 0})
    /* renamed from: com.tencent.qgame.animplayer.mix.j$d */
    public enum d {
        UNKNOWN(EnvironmentCompat.MEDIA_UNKNOWN),
        IMG(XHTMLText.IMG),
        TXT("txt");


        @NotNull
        private final String type;

        d(String str) {
            this.type = str;
        }
    }

    @Nullable
    /* renamed from: a, reason: from getter */
    public final Bitmap getBitmap() {
        return this.bitmap;
    }

    /* renamed from: b, reason: from getter */
    public final int getColor() {
        return this.color;
    }

    /* renamed from: c, reason: from getter */
    public final int getDrawHeight() {
        return this.drawHeight;
    }

    /* renamed from: d, reason: from getter */
    public final int getDrawWidth() {
        return this.drawWidth;
    }

    @NotNull
    /* renamed from: e, reason: from getter */
    public final b getFitType() {
        return this.fitType;
    }

    @NotNull
    /* renamed from: f, reason: from getter */
    public final c getLoadType() {
        return this.loadType;
    }

    @NotNull
    /* renamed from: g, reason: from getter */
    public final String getSrcId() {
        return this.srcId;
    }

    @NotNull
    /* renamed from: h, reason: from getter */
    public final String getSrcTag() {
        return this.srcTag;
    }

    /* renamed from: i, reason: from getter */
    public final int getSrcTextureId() {
        return this.srcTextureId;
    }

    @NotNull
    /* renamed from: j, reason: from getter */
    public final d getSrcType() {
        return this.srcType;
    }

    public final void k(int i10) {
        this.srcTextureId = i10;
    }

    @NotNull
    public String toString() {
        return "Src(srcId='" + this.srcId + "', srcType=" + this.srcType + ", loadType=" + this.loadType + ", srcTag='" + this.srcTag + "', bitmap=" + this.bitmap + ", txt='" + this.txt + "')";
    }
}
