package com.tencent.qgame.animplayer;

import android.os.Handler;
import android.os.HandlerThread;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* compiled from: Decoder.kt */
@Metadata(d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0010\u000b\n\u0002\b\r\b\u0086\b\u0018\u00002\u00020\u0001B\u001b\u0012\b\u0010\u0003\u001a\u0004\u0018\u00010\u0002\u0012\b\u0010\u0005\u001a\u0004\u0018\u00010\u0004¢\u0006\u0004\b\u0006\u0010\u0007J\u0010\u0010\t\u001a\u00020\bHÖ\u0001¢\u0006\u0004\b\t\u0010\nJ\u0010\u0010\f\u001a\u00020\u000bHÖ\u0001¢\u0006\u0004\b\f\u0010\rJ\u001a\u0010\u0010\u001a\u00020\u000f2\b\u0010\u000e\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\u0010\u0010\u0011R$\u0010\u0003\u001a\u0004\u0018\u00010\u00028\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\u0012\u0010\u0013\u001a\u0004\b\u0014\u0010\u0015\"\u0004\b\u0016\u0010\u0017R$\u0010\u0005\u001a\u0004\u0018\u00010\u00048\u0006@\u0006X\u0086\u000e¢\u0006\u0012\n\u0004\b\u0014\u0010\u0018\u001a\u0004\b\u0012\u0010\u0019\"\u0004\b\u001a\u0010\u001b¨\u0006\u001c"}, d2 = {"Lcom/tencent/qgame/animplayer/h;", "", "Landroid/os/HandlerThread;", "thread", "Landroid/os/Handler;", "handler", "<init>", "(Landroid/os/HandlerThread;Landroid/os/Handler;)V", "", "toString", "()Ljava/lang/String;", "", "hashCode", "()I", "other", "", "equals", "(Ljava/lang/Object;)Z", "a", "Landroid/os/HandlerThread;", r0.b.f37717b, "()Landroid/os/HandlerThread;", "d", "(Landroid/os/HandlerThread;)V", "Landroid/os/Handler;", "()Landroid/os/Handler;", "c", "(Landroid/os/Handler;)V", "animplayer_release"}, k = 1, mv = {1, 4, 0})
/* renamed from: com.tencent.qgame.animplayer.h, reason: from toString */
/* loaded from: classes4.dex */
public final /* data */ class HandlerHolder {

    /* renamed from: a, reason: collision with root package name and from kotlin metadata and from toString */
    @Nullable
    public HandlerThread thread;

    /* renamed from: b, reason: collision with root package name and from kotlin metadata and from toString */
    @Nullable
    public Handler handler;

    public HandlerHolder(@Nullable HandlerThread handlerThread, @Nullable Handler handler) {
        this.thread = handlerThread;
        this.handler = handler;
    }

    @Nullable
    /* renamed from: a, reason: from getter */
    public final Handler getHandler() {
        return this.handler;
    }

    @Nullable
    /* renamed from: b, reason: from getter */
    public final HandlerThread getThread() {
        return this.thread;
    }

    public final void c(@Nullable Handler handler) {
        this.handler = handler;
    }

    public final void d(@Nullable HandlerThread handlerThread) {
        this.thread = handlerThread;
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof HandlerHolder)) {
            return false;
        }
        HandlerHolder handlerHolder = (HandlerHolder) other;
        return Intrinsics.areEqual(this.thread, handlerHolder.thread) && Intrinsics.areEqual(this.handler, handlerHolder.handler);
    }

    public int hashCode() {
        HandlerThread handlerThread = this.thread;
        int hashCode = (handlerThread != null ? handlerThread.hashCode() : 0) * 31;
        Handler handler = this.handler;
        return hashCode + (handler != null ? handler.hashCode() : 0);
    }

    @NotNull
    public String toString() {
        return "HandlerHolder(thread=" + this.thread + ", handler=" + this.handler + ")";
    }
}
