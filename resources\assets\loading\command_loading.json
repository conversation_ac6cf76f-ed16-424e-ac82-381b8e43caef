{"v": "5.6.10", "fr": 24, "ip": 0, "op": 24, "w": 72, "h": 72, "nm": "loading_me", "ddd": 0, "assets": [{"id": "image_0", "w": 72, "h": 72, "u": "", "p": "data:image/png;base64,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", "e": 1}], "layers": [{"ddd": 0, "ind": 2, "ty": 2, "nm": "light icon-refresh-fill.png", "cl": "png", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]}, {"t": 24, "s": [360]}], "ix": 10}, "p": {"a": 0, "k": [36, 36, 0], "ix": 2}, "a": {"a": 0, "k": [36, 36, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6}}, "ao": 0, "ip": 0, "op": 24, "st": 0, "bm": 0}], "markers": []}