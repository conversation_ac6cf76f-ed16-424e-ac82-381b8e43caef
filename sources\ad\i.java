package ad;

import java.util.Arrays;
import java.util.Map;
import kotlin.Metadata;
import kotlin.jvm.internal.Intrinsics;
import kotlin.text.StringsKt;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jivesoftware.smackx.receipts.DeliveryReceiptRequest;
import org.jivesoftware.smackx.shim.packet.HeadersExtension;

/* compiled from: HTTPResponse.kt */
@Metadata(d1 = {"\u0000.\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010$\n\u0002\u0010\u000e\n\u0000\n\u0002\u0010\u0012\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u0013\b\u0086\b\u0018\u00002\u00020\u0001B3\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0012\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00070\u0006\u0012\u0006\u0010\n\u001a\u00020\t¢\u0006\u0004\b\u000b\u0010\fJ\u0017\u0010\u000e\u001a\u00020\u00072\b\b\u0002\u0010\r\u001a\u00020\u0007¢\u0006\u0004\b\u000e\u0010\u000fJ\u001a\u0010\u0012\u001a\u00020\u00112\b\u0010\u0010\u001a\u0004\u0018\u00010\u0001H\u0096\u0002¢\u0006\u0004\b\u0012\u0010\u0013J\u000f\u0010\u0014\u001a\u00020\u0004H\u0016¢\u0006\u0004\b\u0014\u0010\u0015J\u0010\u0010\u0016\u001a\u00020\u0007HÖ\u0001¢\u0006\u0004\b\u0016\u0010\u0017R\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u000e\u0010\u0018\u001a\u0004\b\u0019\u0010\u001aR\u0017\u0010\u0005\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b\u001b\u0010\u001c\u001a\u0004\b\u001d\u0010\u0015R#\u0010\b\u001a\u000e\u0012\u0004\u0012\u00020\u0007\u0012\u0004\u0012\u00020\u00070\u00068\u0006¢\u0006\f\n\u0004\b\u001e\u0010\u001f\u001a\u0004\b \u0010!R\u0017\u0010\n\u001a\u00020\t8\u0006¢\u0006\f\n\u0004\b \u0010\"\u001a\u0004\b\u001e\u0010#¨\u0006$"}, d2 = {"Lad/i;", "", "Lad/g;", DeliveryReceiptRequest.ELEMENT, "", "statusCode", "", "", HeadersExtension.ELEMENT, "", "body", "<init>", "(Lad/g;ILjava/util/Map;[B)V", "charset", "a", "(Ljava/lang/String;)Ljava/lang/String;", "other", "", "equals", "(Ljava/lang/Object;)Z", "hashCode", "()I", "toString", "()Ljava/lang/String;", "Lad/g;", "getRequest", "()Lad/g;", r0.b.f37717b, "I", "e", "c", "Ljava/util/Map;", "d", "()Ljava/util/Map;", "[B", "()[B", "core"}, k = 1, mv = {2, 0, 0}, xi = 48)
/* renamed from: ad.i, reason: from toString */
/* loaded from: classes4.dex */
public final /* data */ class HTTPResponse {

    /* renamed from: a, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final HTTPRequest request;

    /* renamed from: b, reason: collision with root package name and from kotlin metadata and from toString */
    public final int statusCode;

    /* renamed from: c, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final Map<String, String> headers;

    /* renamed from: d, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final byte[] body;

    public HTTPResponse(@NotNull HTTPRequest request, int i10, @NotNull Map<String, String> headers, @NotNull byte[] body) {
        Intrinsics.checkNotNullParameter(request, "request");
        Intrinsics.checkNotNullParameter(headers, "headers");
        Intrinsics.checkNotNullParameter(body, "body");
        this.request = request;
        this.statusCode = i10;
        this.headers = headers;
        this.body = body;
    }

    public static /* synthetic */ String b(HTTPResponse hTTPResponse, String str, int i10, Object obj) {
        if ((i10 & 1) != 0) {
            str = "UTF-8";
        }
        return hTTPResponse.a(str);
    }

    @NotNull
    public final String a(@NotNull String charset) {
        Intrinsics.checkNotNullParameter(charset, "charset");
        return StringsKt.decodeToString(this.body);
    }

    @NotNull
    /* renamed from: c, reason: from getter */
    public final byte[] getBody() {
        return this.body;
    }

    @NotNull
    public final Map<String, String> d() {
        return this.headers;
    }

    /* renamed from: e, reason: from getter */
    public final int getStatusCode() {
        return this.statusCode;
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (other == null || HTTPResponse.class != other.getClass()) {
            return false;
        }
        HTTPResponse hTTPResponse = (HTTPResponse) other;
        return Intrinsics.areEqual(this.request, hTTPResponse.request) && this.statusCode == hTTPResponse.statusCode && Intrinsics.areEqual(this.headers, hTTPResponse.headers) && Arrays.equals(this.body, hTTPResponse.body);
    }

    public int hashCode() {
        return (((((this.request.hashCode() * 31) + this.statusCode) * 31) + this.headers.hashCode()) * 31) + Arrays.hashCode(this.body);
    }

    @NotNull
    public String toString() {
        return "HTTPResponse(request=" + this.request + ", statusCode=" + this.statusCode + ", headers=" + this.headers + ", body=" + Arrays.toString(this.body) + ')';
    }
}
