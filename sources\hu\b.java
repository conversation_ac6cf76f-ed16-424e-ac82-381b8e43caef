package hu;

import androidx.core.app.FrameMetricsAggregator;
import aw.n;
import com.google.firebase.database.core.ServerValues;
import com.wear.bean.ProgramPattern;
import cw.f;
import dw.e;
import ew.c2;
import ew.h1;
import ew.h2;
import ew.j0;
import ew.n0;
import ew.r2;
import ew.w0;
import jl.i;
import kotlin.Deprecated;
import kotlin.DeprecationLevel;
import kotlin.Metadata;
import kotlin.jvm.JvmField;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlinx.serialization.UnknownFieldException;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jivesoftware.smackx.xhtmlim.XHTMLText;

/* compiled from: Date.kt */
@n
@Metadata(d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0010\u000f\n\u0002\u0010\b\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\t\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0005\n\u0002\u0010\u000e\n\u0002\b\u0004\n\u0002\u0010\u0000\n\u0002\u0010\u000b\n\u0002\b\u001b\b\u0087\b\u0018\u0000 @2\b\u0012\u0004\u0012\u00020\u00000\u0001:\u0002\u001e)BO\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0002\u0012\u0006\u0010\u0007\u001a\u00020\u0006\u0012\u0006\u0010\b\u001a\u00020\u0002\u0012\u0006\u0010\t\u001a\u00020\u0002\u0012\u0006\u0010\u000b\u001a\u00020\n\u0012\u0006\u0010\f\u001a\u00020\u0002\u0012\u0006\u0010\u000e\u001a\u00020\r¢\u0006\u0004\b\u000f\u0010\u0010Bg\b\u0010\u0012\u0006\u0010\u0011\u001a\u00020\u0002\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0002\u0012\b\u0010\u0007\u001a\u0004\u0018\u00010\u0006\u0012\u0006\u0010\b\u001a\u00020\u0002\u0012\u0006\u0010\t\u001a\u00020\u0002\u0012\b\u0010\u000b\u001a\u0004\u0018\u00010\n\u0012\u0006\u0010\f\u001a\u00020\u0002\u0012\u0006\u0010\u000e\u001a\u00020\r\u0012\b\u0010\u0013\u001a\u0004\u0018\u00010\u0012¢\u0006\u0004\b\u000f\u0010\u0014J'\u0010\u001b\u001a\u00020\u001a2\u0006\u0010\u0015\u001a\u00020\u00002\u0006\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0019\u001a\u00020\u0018H\u0001¢\u0006\u0004\b\u001b\u0010\u001cJ\u0018\u0010\u001e\u001a\u00020\u00022\u0006\u0010\u001d\u001a\u00020\u0000H\u0096\u0002¢\u0006\u0004\b\u001e\u0010\u001fJ\u0010\u0010!\u001a\u00020 HÖ\u0001¢\u0006\u0004\b!\u0010\"J\u0010\u0010#\u001a\u00020\u0002HÖ\u0001¢\u0006\u0004\b#\u0010$J\u001a\u0010'\u001a\u00020&2\b\u0010\u001d\u001a\u0004\u0018\u00010%HÖ\u0003¢\u0006\u0004\b'\u0010(R\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b)\u0010*\u001a\u0004\b+\u0010$R\u0017\u0010\u0004\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u001e\u0010*\u001a\u0004\b,\u0010$R\u0017\u0010\u0005\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u001b\u0010*\u001a\u0004\b-\u0010$R\u0017\u0010\u0007\u001a\u00020\u00068\u0006¢\u0006\f\n\u0004\b.\u0010/\u001a\u0004\b0\u00101R\u0017\u0010\b\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b2\u0010*\u001a\u0004\b3\u0010$R\u0017\u0010\t\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b4\u0010*\u001a\u0004\b5\u0010$R\u0017\u0010\u000b\u001a\u00020\n8\u0006¢\u0006\f\n\u0004\b6\u00107\u001a\u0004\b8\u00109R\u0017\u0010\f\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b:\u0010*\u001a\u0004\b;\u0010$R\u0017\u0010\u000e\u001a\u00020\r8\u0006¢\u0006\f\n\u0004\b<\u0010=\u001a\u0004\b>\u0010?¨\u0006A"}, d2 = {"Lhu/b;", "", "", "seconds", "minutes", "hours", "Lhu/d;", "dayOfWeek", "dayOfMonth", "dayOfYear", "Lhu/c;", "month", "year", "", ServerValues.NAME_OP_TIMESTAMP, "<init>", "(IIILhu/d;IILhu/c;IJ)V", "seen0", "Lew/r2;", "serializationConstructorMarker", "(IIIILhu/d;IILhu/c;IJLew/r2;)V", "self", "Ldw/d;", "output", "Lcw/f;", "serialDesc", "", "c", "(Lhu/b;Ldw/d;Lcw/f;)V", "other", r0.b.f37717b, "(Lhu/b;)I", "", "toString", "()Ljava/lang/String;", "hashCode", "()I", "", "", "equals", "(Ljava/lang/Object;)Z", "a", "I", "getSeconds", "getMinutes", "getHours", "d", "Lhu/d;", "getDayOfWeek", "()Lhu/d;", "e", "getDayOfMonth", ProgramPattern.writePatternChar100, "getDayOfYear", "g", "Lhu/c;", "getMonth", "()Lhu/c;", XHTMLText.H, "getYear", i.f32548i, "J", "getTimestamp", "()J", "Companion", "ktor-utils"}, k = 1, mv = {2, 0, 0}, xi = 48)
/* renamed from: hu.b, reason: from toString */
/* loaded from: classes5.dex */
public final /* data */ class GMTDate implements Comparable<GMTDate> {

    /* renamed from: Companion, reason: from kotlin metadata */
    @NotNull
    public static final Companion INSTANCE = new Companion(null);

    /* renamed from: j */
    @JvmField
    @NotNull
    public static final aw.c<Object>[] f31443j = {null, null, null, j0.a("io.ktor.util.date.WeekDay", d.values()), null, null, j0.a("io.ktor.util.date.Month", c.values()), null, null};

    /* renamed from: k */
    @NotNull
    public static final GMTDate f31444k = hu.a.a(0L);

    /* renamed from: a, reason: from kotlin metadata and from toString */
    public final int seconds;

    /* renamed from: b, reason: from toString */
    public final int minutes;

    /* renamed from: c, reason: from kotlin metadata and from toString */
    public final int hours;

    /* renamed from: d, reason: from kotlin metadata and from toString */
    @NotNull
    public final d dayOfWeek;

    /* renamed from: e, reason: from kotlin metadata and from toString */
    public final int dayOfMonth;

    /* renamed from: f, reason: from toString */
    public final int dayOfYear;

    /* renamed from: g, reason: from kotlin metadata and from toString */
    @NotNull
    public final c month;

    /* renamed from: h, reason: from toString */
    public final int year;

    /* renamed from: i, reason: from toString */
    public final long timestamp;

    /* compiled from: Date.kt */
    @Metadata(d1 = {"\u0000:\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\bÇ\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0003\u0010\u0004J\u001d\u0010\t\u001a\u00020\b2\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u0002¢\u0006\u0004\b\t\u0010\nJ\u0015\u0010\r\u001a\u00020\u00022\u0006\u0010\f\u001a\u00020\u000b¢\u0006\u0004\b\r\u0010\u000eJ\u0017\u0010\u0011\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00100\u000f¢\u0006\u0004\b\u0011\u0010\u0012R\u0017\u0010\u0014\u001a\u00020\u00138\u0006¢\u0006\f\n\u0004\b\u0014\u0010\u0015\u001a\u0004\b\u0016\u0010\u0017¨\u0006\u0018"}, d2 = {"io/ktor/util/date/GMTDate.$serializer", "Lew/n0;", "Lhu/b;", "<init>", "()V", "Ldw/f;", "encoder", "value", "", ProgramPattern.writePatternChar100, "(Ldw/f;Lhu/b;)V", "Ldw/e;", "decoder", "e", "(Ldw/e;)Lhu/b;", "", "Law/c;", "d", "()[Law/c;", "Lcw/f;", "descriptor", "Lcw/f;", "getDescriptor", "()Lcw/f;", "ktor-utils"}, k = 1, mv = {2, 0, 0}, xi = 48)
    @Deprecated(level = DeprecationLevel.HIDDEN, message = "This synthesized declaration should not be used directly")
    /* renamed from: hu.b$a */
    public /* synthetic */ class a implements n0<GMTDate> {

        /* renamed from: a */
        @NotNull
        public static final a f31454a;

        @NotNull
        private static final f descriptor;

        static {
            a aVar = new a();
            f31454a = aVar;
            h2 h2Var = new h2("io.ktor.util.date.GMTDate", aVar, 9);
            h2Var.o("seconds", false);
            h2Var.o("minutes", false);
            h2Var.o("hours", false);
            h2Var.o("dayOfWeek", false);
            h2Var.o("dayOfMonth", false);
            h2Var.o("dayOfYear", false);
            h2Var.o("month", false);
            h2Var.o("year", false);
            h2Var.o(ServerValues.NAME_OP_TIMESTAMP, false);
            descriptor = h2Var;
        }

        @Override // ew.n0
        @NotNull
        public aw.c<?>[] b() {
            return n0.a.a(this);
        }

        @Override // ew.n0
        @NotNull
        public final aw.c<?>[] d() {
            aw.c<?>[] cVarArr = GMTDate.f31443j;
            aw.c<?> cVar = cVarArr[3];
            aw.c<?> cVar2 = cVarArr[6];
            w0 w0Var = w0.f30382a;
            return new aw.c[]{w0Var, w0Var, w0Var, cVar, w0Var, w0Var, cVar2, w0Var, h1.f30272a};
        }

        @Override // aw.b
        @NotNull
        /* renamed from: e */
        public final GMTDate a(@NotNull e decoder) {
            int i10;
            c cVar;
            d dVar;
            int i11;
            int i12;
            int i13;
            int i14;
            int i15;
            int i16;
            long j10;
            Intrinsics.checkNotNullParameter(decoder, "decoder");
            f fVar = descriptor;
            dw.c b10 = decoder.b(fVar);
            aw.c[] cVarArr = GMTDate.f31443j;
            int i17 = 7;
            if (b10.o()) {
                int f10 = b10.f(fVar, 0);
                int f11 = b10.f(fVar, 1);
                int f12 = b10.f(fVar, 2);
                d dVar2 = (d) b10.H(fVar, 3, cVarArr[3], null);
                int f13 = b10.f(fVar, 4);
                int f14 = b10.f(fVar, 5);
                cVar = (c) b10.H(fVar, 6, cVarArr[6], null);
                i10 = f10;
                i11 = b10.f(fVar, 7);
                i12 = f14;
                i14 = f13;
                i15 = f12;
                dVar = dVar2;
                i16 = f11;
                j10 = b10.p(fVar, 8);
                i13 = FrameMetricsAggregator.EVERY_DURATION;
            } else {
                c cVar2 = null;
                d dVar3 = null;
                long j11 = 0;
                int i18 = 0;
                int i19 = 0;
                int i20 = 0;
                int i21 = 0;
                int i22 = 0;
                int i23 = 0;
                int i24 = 0;
                boolean z10 = true;
                while (z10) {
                    int h10 = b10.h(fVar);
                    switch (h10) {
                        case -1:
                            i17 = 7;
                            z10 = false;
                        case 0:
                            i21 |= 1;
                            i18 = b10.f(fVar, 0);
                            i17 = 7;
                        case 1:
                            i24 = b10.f(fVar, 1);
                            i21 |= 2;
                            i17 = 7;
                        case 2:
                            i23 = b10.f(fVar, 2);
                            i21 |= 4;
                        case 3:
                            dVar3 = (d) b10.H(fVar, 3, cVarArr[3], dVar3);
                            i21 |= 8;
                        case 4:
                            i22 = b10.f(fVar, 4);
                            i21 |= 16;
                        case 5:
                            i20 = b10.f(fVar, 5);
                            i21 |= 32;
                        case 6:
                            cVar2 = (c) b10.H(fVar, 6, cVarArr[6], cVar2);
                            i21 |= 64;
                        case 7:
                            i19 = b10.f(fVar, i17);
                            i21 |= 128;
                        case 8:
                            j11 = b10.p(fVar, 8);
                            i21 |= 256;
                        default:
                            throw new UnknownFieldException(h10);
                    }
                }
                i10 = i18;
                cVar = cVar2;
                dVar = dVar3;
                i11 = i19;
                i12 = i20;
                i13 = i21;
                i14 = i22;
                i15 = i23;
                i16 = i24;
                j10 = j11;
            }
            b10.c(fVar);
            return new GMTDate(i13, i10, i16, i15, dVar, i14, i12, cVar, i11, j10, null);
        }

        @Override // aw.o
        /* renamed from: f */
        public final void c(@NotNull dw.f encoder, @NotNull GMTDate value) {
            Intrinsics.checkNotNullParameter(encoder, "encoder");
            Intrinsics.checkNotNullParameter(value, "value");
            f fVar = descriptor;
            dw.d b10 = encoder.b(fVar);
            GMTDate.c(value, b10, fVar);
            b10.c(fVar);
        }

        @Override // aw.c, aw.o, aw.b
        @NotNull
        public final f getDescriptor() {
            return descriptor;
        }
    }

    /* compiled from: Date.kt */
    @Metadata(d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J\u0013\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004¢\u0006\u0004\b\u0006\u0010\u0007¨\u0006\b"}, d2 = {"Lhu/b$b;", "", "<init>", "()V", "Law/c;", "Lhu/b;", "serializer", "()Law/c;", "ktor-utils"}, k = 1, mv = {2, 0, 0}, xi = 48)
    /* renamed from: hu.b$b, reason: from kotlin metadata */
    public static final class Companion {
        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }

        @NotNull
        public final aw.c<GMTDate> serializer() {
            return a.f31454a;
        }

        public Companion() {
        }
    }

    public /* synthetic */ GMTDate(int i10, int i11, int i12, int i13, d dVar, int i14, int i15, c cVar, int i16, long j10, r2 r2Var) {
        if (511 != (i10 & FrameMetricsAggregator.EVERY_DURATION)) {
            c2.b(i10, FrameMetricsAggregator.EVERY_DURATION, a.f31454a.getDescriptor());
        }
        this.seconds = i11;
        this.minutes = i12;
        this.hours = i13;
        this.dayOfWeek = dVar;
        this.dayOfMonth = i14;
        this.dayOfYear = i15;
        this.month = cVar;
        this.year = i16;
        this.timestamp = j10;
    }

    @JvmStatic
    public static final /* synthetic */ void c(GMTDate self, dw.d output, f serialDesc) {
        aw.c<Object>[] cVarArr = f31443j;
        output.z(serialDesc, 0, self.seconds);
        output.z(serialDesc, 1, self.minutes);
        output.z(serialDesc, 2, self.hours);
        output.r(serialDesc, 3, cVarArr[3], self.dayOfWeek);
        output.z(serialDesc, 4, self.dayOfMonth);
        output.z(serialDesc, 5, self.dayOfYear);
        output.r(serialDesc, 6, cVarArr[6], self.month);
        output.z(serialDesc, 7, self.year);
        output.E(serialDesc, 8, self.timestamp);
    }

    @Override // java.lang.Comparable
    /* renamed from: b */
    public int compareTo(@NotNull GMTDate other) {
        Intrinsics.checkNotNullParameter(other, "other");
        return Intrinsics.compare(this.timestamp, other.timestamp);
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof GMTDate)) {
            return false;
        }
        GMTDate gMTDate = (GMTDate) other;
        return this.seconds == gMTDate.seconds && this.minutes == gMTDate.minutes && this.hours == gMTDate.hours && this.dayOfWeek == gMTDate.dayOfWeek && this.dayOfMonth == gMTDate.dayOfMonth && this.dayOfYear == gMTDate.dayOfYear && this.month == gMTDate.month && this.year == gMTDate.year && this.timestamp == gMTDate.timestamp;
    }

    public int hashCode() {
        return (((((((((((((((this.seconds * 31) + this.minutes) * 31) + this.hours) * 31) + this.dayOfWeek.hashCode()) * 31) + this.dayOfMonth) * 31) + this.dayOfYear) * 31) + this.month.hashCode()) * 31) + this.year) * 31) + androidx.compose.animation.a.a(this.timestamp);
    }

    @NotNull
    public String toString() {
        return "GMTDate(seconds=" + this.seconds + ", minutes=" + this.minutes + ", hours=" + this.hours + ", dayOfWeek=" + this.dayOfWeek + ", dayOfMonth=" + this.dayOfMonth + ", dayOfYear=" + this.dayOfYear + ", month=" + this.month + ", year=" + this.year + ", timestamp=" + this.timestamp + ')';
    }

    public GMTDate(int i10, int i11, int i12, @NotNull d dayOfWeek, int i13, int i14, @NotNull c month, int i15, long j10) {
        Intrinsics.checkNotNullParameter(dayOfWeek, "dayOfWeek");
        Intrinsics.checkNotNullParameter(month, "month");
        this.seconds = i10;
        this.minutes = i11;
        this.hours = i12;
        this.dayOfWeek = dayOfWeek;
        this.dayOfMonth = i13;
        this.dayOfYear = i14;
        this.month = month;
        this.year = i15;
        this.timestamp = j10;
    }
}
