package com.tencent.qgame.animplayer;

import com.wear.widget.control.FingImageLayout;
import kotlin.Metadata;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.jivesoftware.smackx.xhtmlim.XHTMLText;

/* compiled from: AnimConfig.kt */
@Metadata(d1 = {"\u0000 \n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\b\n\u0002\b\u0006\n\u0002\u0010\u000e\n\u0002\b\u0005\n\u0002\u0010\u000b\n\u0002\b\b\b\u0086\b\u0018\u00002\u00020\u0001B'\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0004\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0002\u0012\u0006\u0010\u0006\u001a\u00020\u0002¢\u0006\u0004\b\u0007\u0010\bJ\u0010\u0010\n\u001a\u00020\tHÖ\u0001¢\u0006\u0004\b\n\u0010\u000bJ\u0010\u0010\f\u001a\u00020\u0002HÖ\u0001¢\u0006\u0004\b\f\u0010\rJ\u001a\u0010\u0010\u001a\u00020\u000f2\b\u0010\u000e\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\u0010\u0010\u0011R\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0012\u0010\u0013\u001a\u0004\b\u0014\u0010\rR\u0017\u0010\u0004\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0015\u0010\u0013\u001a\u0004\b\u0016\u0010\rR\u0017\u0010\u0005\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0014\u0010\u0013\u001a\u0004\b\u0015\u0010\rR\u0017\u0010\u0006\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u0016\u0010\u0013\u001a\u0004\b\u0012\u0010\r¨\u0006\u0017"}, d2 = {"Lcom/tencent/qgame/animplayer/l;", "", "", "x", FingImageLayout.ObjectAnimatorY, "w", XHTMLText.H, "<init>", "(IIII)V", "", "toString", "()Ljava/lang/String;", "hashCode", "()I", "other", "", "equals", "(Ljava/lang/Object;)Z", "a", "I", "c", r0.b.f37717b, "d", "animplayer_release"}, k = 1, mv = {1, 4, 0})
/* renamed from: com.tencent.qgame.animplayer.l, reason: from toString */
/* loaded from: classes4.dex */
public final /* data */ class PointRect {

    /* renamed from: a, reason: collision with root package name and from kotlin metadata and from toString */
    public final int x;

    /* renamed from: b, reason: collision with root package name and from kotlin metadata and from toString */
    public final int y;

    /* renamed from: c, reason: collision with root package name and from kotlin metadata and from toString */
    public final int w;

    /* renamed from: d, reason: collision with root package name and from kotlin metadata and from toString */
    public final int h;

    public PointRect(int i10, int i11, int i12, int i13) {
        this.x = i10;
        this.y = i11;
        this.w = i12;
        this.h = i13;
    }

    /* renamed from: a, reason: from getter */
    public final int getH() {
        return this.h;
    }

    /* renamed from: b, reason: from getter */
    public final int getW() {
        return this.w;
    }

    /* renamed from: c, reason: from getter */
    public final int getX() {
        return this.x;
    }

    /* renamed from: d, reason: from getter */
    public final int getY() {
        return this.y;
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof PointRect)) {
            return false;
        }
        PointRect pointRect = (PointRect) other;
        return this.x == pointRect.x && this.y == pointRect.y && this.w == pointRect.w && this.h == pointRect.h;
    }

    public int hashCode() {
        return (((((this.x * 31) + this.y) * 31) + this.w) * 31) + this.h;
    }

    @NotNull
    public String toString() {
        return "PointRect(x=" + this.x + ", y=" + this.y + ", w=" + this.w + ", h=" + this.h + ")";
    }
}
