package fd;

import aw.n;
import com.google.android.exoplayer2.text.ttml.TtmlNode;
import com.wear.bean.ProgramPattern;
import ew.c2;
import ew.h1;
import ew.h2;
import ew.n0;
import ew.r2;
import ew.w2;
import fd.LightModeDTO;
import hd.LightModeGroup;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import kotlin.Deprecated;
import kotlin.DeprecationLevel;
import kotlin.Metadata;
import kotlin.collections.CollectionsKt;
import kotlin.jvm.JvmField;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlin.jvm.internal.SourceDebugExtension;
import kotlinx.serialization.UnknownFieldException;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* compiled from: LightModeGroupDTO.kt */
@n
@Metadata(d1 = {"\u0000T\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u000f\b\u0087\b\u0018\u0000 /2\u00020\u0001:\u0002$\u001aB-\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0004\u0012\f\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\u0007¢\u0006\u0004\b\n\u0010\u000bBG\b\u0010\u0012\u0006\u0010\r\u001a\u00020\f\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\b\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0004\u0012\u000e\u0010\t\u001a\n\u0012\u0004\u0012\u00020\b\u0018\u00010\u0007\u0012\b\u0010\u000f\u001a\u0004\u0018\u00010\u000e¢\u0006\u0004\b\n\u0010\u0010J'\u0010\u0017\u001a\u00020\u00162\u0006\u0010\u0011\u001a\u00020\u00002\u0006\u0010\u0013\u001a\u00020\u00122\u0006\u0010\u0015\u001a\u00020\u0014H\u0001¢\u0006\u0004\b\u0017\u0010\u0018J\r\u0010\u001a\u001a\u00020\u0019¢\u0006\u0004\b\u001a\u0010\u001bJ\u0010\u0010\u001c\u001a\u00020\u0004HÖ\u0001¢\u0006\u0004\b\u001c\u0010\u001dJ\u0010\u0010\u001e\u001a\u00020\fHÖ\u0001¢\u0006\u0004\b\u001e\u0010\u001fJ\u001a\u0010\"\u001a\u00020!2\b\u0010 \u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b\"\u0010#R\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b$\u0010%\u001a\u0004\b&\u0010'R\u0017\u0010\u0005\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b\u001a\u0010(\u001a\u0004\b)\u0010\u001dR\u0017\u0010\u0006\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b\u0017\u0010(\u001a\u0004\b*\u0010\u001dR\u001d\u0010\t\u001a\b\u0012\u0004\u0012\u00020\b0\u00078\u0006¢\u0006\f\n\u0004\b+\u0010,\u001a\u0004\b-\u0010.¨\u00060"}, d2 = {"Lfd/g;", "", "", TtmlNode.ATTR_ID, "", "groupKey", "groupName", "", "Lfd/f;", "group", "<init>", "(JLjava/lang/String;Ljava/lang/String;Ljava/util/List;)V", "", "seen0", "Lew/r2;", "serializationConstructorMarker", "(IJLjava/lang/String;Ljava/lang/String;Ljava/util/List;Lew/r2;)V", "self", "Ldw/d;", "output", "Lcw/f;", "serialDesc", "", "c", "(Lfd/g;Ldw/d;Lcw/f;)V", "Lhd/g;", r0.b.f37717b, "()Lhd/g;", "toString", "()Ljava/lang/String;", "hashCode", "()I", "other", "", "equals", "(Ljava/lang/Object;)Z", "a", "J", "getId", "()J", "Ljava/lang/String;", "getGroupKey", "getGroupName", "d", "Ljava/util/List;", "getGroup", "()Ljava/util/List;", "Companion", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
@SourceDebugExtension({"SMAP\nLightModeGroupDTO.kt\nKotlin\n*S Kotlin\n*F\n+ 1 LightModeGroupDTO.kt\ncom/remotekmp/lightEffect/data/entities/LightModeGroupDTO\n+ 2 _Collections.kt\nkotlin/collections/CollectionsKt___CollectionsKt\n*L\n1#1,30:1\n1557#2:31\n1628#2,3:32\n*S KotlinDebug\n*F\n+ 1 LightModeGroupDTO.kt\ncom/remotekmp/lightEffect/data/entities/LightModeGroupDTO\n*L\n28#1:31\n28#1:32,3\n*E\n"})
/* renamed from: fd.g, reason: from toString */
/* loaded from: classes4.dex */
public final /* data */ class LightModeGroupDTO {

    /* renamed from: Companion, reason: from kotlin metadata */
    @NotNull
    public static final Companion INSTANCE = new Companion(null);

    /* renamed from: e, reason: collision with root package name */
    @JvmField
    @NotNull
    public static final aw.c<Object>[] f30633e = {null, null, null, new ew.f(LightModeDTO.a.f30632a)};

    /* renamed from: a, reason: collision with root package name and from kotlin metadata and from toString */
    public final long id;

    /* renamed from: b, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final String groupKey;

    /* renamed from: c, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final String groupName;

    /* renamed from: d, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final List<LightModeDTO> group;

    /* compiled from: LightModeGroupDTO.kt */
    @Metadata(d1 = {"\u0000:\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\bÇ\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0003\u0010\u0004J\u001d\u0010\t\u001a\u00020\b2\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u0002¢\u0006\u0004\b\t\u0010\nJ\u0015\u0010\r\u001a\u00020\u00022\u0006\u0010\f\u001a\u00020\u000b¢\u0006\u0004\b\r\u0010\u000eJ\u0017\u0010\u0011\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00100\u000f¢\u0006\u0004\b\u0011\u0010\u0012R\u0017\u0010\u0014\u001a\u00020\u00138\u0006¢\u0006\f\n\u0004\b\u0014\u0010\u0015\u001a\u0004\b\u0016\u0010\u0017¨\u0006\u0018"}, d2 = {"com/remotekmp/lightEffect/data/entities/LightModeGroupDTO.$serializer", "Lew/n0;", "Lfd/g;", "<init>", "()V", "Ldw/f;", "encoder", "value", "", ProgramPattern.writePatternChar100, "(Ldw/f;Lfd/g;)V", "Ldw/e;", "decoder", "e", "(Ldw/e;)Lfd/g;", "", "Law/c;", "d", "()[Law/c;", "Lcw/f;", "descriptor", "Lcw/f;", "getDescriptor", "()Lcw/f;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    @Deprecated(level = DeprecationLevel.HIDDEN, message = "This synthesized declaration should not be used directly")
    /* renamed from: fd.g$a */
    public /* synthetic */ class a implements n0<LightModeGroupDTO> {

        /* renamed from: a, reason: collision with root package name */
        @NotNull
        public static final a f30638a;

        @NotNull
        private static final cw.f descriptor;

        static {
            a aVar = new a();
            f30638a = aVar;
            h2 h2Var = new h2("com.remotekmp.lightEffect.data.entities.LightModeGroupDTO", aVar, 4);
            h2Var.o(TtmlNode.ATTR_ID, false);
            h2Var.o("groupKey", false);
            h2Var.o("groupName", false);
            h2Var.o("group", false);
            descriptor = h2Var;
        }

        @Override // ew.n0
        @NotNull
        public aw.c<?>[] b() {
            return n0.a.a(this);
        }

        @Override // ew.n0
        @NotNull
        public final aw.c<?>[] d() {
            aw.c<?> cVar = LightModeGroupDTO.f30633e[3];
            w2 w2Var = w2.f30386a;
            return new aw.c[]{h1.f30272a, w2Var, w2Var, cVar};
        }

        @Override // aw.b
        @NotNull
        /* renamed from: e, reason: merged with bridge method [inline-methods] */
        public final LightModeGroupDTO a(@NotNull dw.e decoder) {
            int i10;
            String str;
            String str2;
            List list;
            long j10;
            Intrinsics.checkNotNullParameter(decoder, "decoder");
            cw.f fVar = descriptor;
            dw.c b10 = decoder.b(fVar);
            aw.c[] cVarArr = LightModeGroupDTO.f30633e;
            String str3 = null;
            if (b10.o()) {
                long p10 = b10.p(fVar, 0);
                String F = b10.F(fVar, 1);
                String F2 = b10.F(fVar, 2);
                list = (List) b10.H(fVar, 3, cVarArr[3], null);
                str = F;
                str2 = F2;
                j10 = p10;
                i10 = 15;
            } else {
                long j11 = 0;
                int i11 = 0;
                boolean z10 = true;
                String str4 = null;
                List list2 = null;
                while (z10) {
                    int h10 = b10.h(fVar);
                    if (h10 == -1) {
                        z10 = false;
                    } else if (h10 == 0) {
                        j11 = b10.p(fVar, 0);
                        i11 |= 1;
                    } else if (h10 == 1) {
                        str3 = b10.F(fVar, 1);
                        i11 |= 2;
                    } else if (h10 == 2) {
                        str4 = b10.F(fVar, 2);
                        i11 |= 4;
                    } else {
                        if (h10 != 3) {
                            throw new UnknownFieldException(h10);
                        }
                        list2 = (List) b10.H(fVar, 3, cVarArr[3], list2);
                        i11 |= 8;
                    }
                }
                i10 = i11;
                str = str3;
                str2 = str4;
                list = list2;
                j10 = j11;
            }
            b10.c(fVar);
            return new LightModeGroupDTO(i10, j10, str, str2, list, null);
        }

        @Override // aw.o
        /* renamed from: f, reason: merged with bridge method [inline-methods] */
        public final void c(@NotNull dw.f encoder, @NotNull LightModeGroupDTO value) {
            Intrinsics.checkNotNullParameter(encoder, "encoder");
            Intrinsics.checkNotNullParameter(value, "value");
            cw.f fVar = descriptor;
            dw.d b10 = encoder.b(fVar);
            LightModeGroupDTO.c(value, b10, fVar);
            b10.c(fVar);
        }

        @Override // aw.c, aw.o, aw.b
        @NotNull
        public final cw.f getDescriptor() {
            return descriptor;
        }
    }

    /* compiled from: LightModeGroupDTO.kt */
    @Metadata(d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J\u0013\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004¢\u0006\u0004\b\u0006\u0010\u0007¨\u0006\b"}, d2 = {"Lfd/g$b;", "", "<init>", "()V", "Law/c;", "Lfd/g;", "serializer", "()Law/c;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    /* renamed from: fd.g$b, reason: from kotlin metadata */
    public static final class Companion {
        public Companion() {
        }

        @NotNull
        public final aw.c<LightModeGroupDTO> serializer() {
            return a.f30638a;
        }

        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }
    }

    public /* synthetic */ LightModeGroupDTO(int i10, long j10, String str, String str2, List list, r2 r2Var) {
        if (15 != (i10 & 15)) {
            c2.b(i10, 15, a.f30638a.getDescriptor());
        }
        this.id = j10;
        this.groupKey = str;
        this.groupName = str2;
        this.group = list;
    }

    @JvmStatic
    public static final /* synthetic */ void c(LightModeGroupDTO self, dw.d output, cw.f serialDesc) {
        aw.c<Object>[] cVarArr = f30633e;
        output.E(serialDesc, 0, self.id);
        output.B(serialDesc, 1, self.groupKey);
        output.B(serialDesc, 2, self.groupName);
        output.r(serialDesc, 3, cVarArr[3], self.group);
    }

    @NotNull
    public final LightModeGroup b() {
        long j10 = this.id;
        String str = this.groupKey;
        String str2 = this.groupName;
        List<LightModeDTO> list = this.group;
        ArrayList arrayList = new ArrayList(CollectionsKt.collectionSizeOrDefault(list, 10));
        Iterator<T> it = list.iterator();
        while (it.hasNext()) {
            arrayList.add(((LightModeDTO) it.next()).b());
        }
        return new LightModeGroup(j10, str, str2, arrayList);
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof LightModeGroupDTO)) {
            return false;
        }
        LightModeGroupDTO lightModeGroupDTO = (LightModeGroupDTO) other;
        return this.id == lightModeGroupDTO.id && Intrinsics.areEqual(this.groupKey, lightModeGroupDTO.groupKey) && Intrinsics.areEqual(this.groupName, lightModeGroupDTO.groupName) && Intrinsics.areEqual(this.group, lightModeGroupDTO.group);
    }

    public int hashCode() {
        return (((((androidx.compose.animation.a.a(this.id) * 31) + this.groupKey.hashCode()) * 31) + this.groupName.hashCode()) * 31) + this.group.hashCode();
    }

    @NotNull
    public String toString() {
        return "LightModeGroupDTO(id=" + this.id + ", groupKey=" + this.groupKey + ", groupName=" + this.groupName + ", group=" + this.group + ')';
    }

    public LightModeGroupDTO(long j10, @NotNull String groupKey, @NotNull String groupName, @NotNull List<LightModeDTO> group) {
        Intrinsics.checkNotNullParameter(groupKey, "groupKey");
        Intrinsics.checkNotNullParameter(groupName, "groupName");
        Intrinsics.checkNotNullParameter(group, "group");
        this.id = j10;
        this.groupKey = groupKey;
        this.groupName = groupName;
        this.group = group;
    }
}
