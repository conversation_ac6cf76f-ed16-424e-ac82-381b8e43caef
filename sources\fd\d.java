package fd;

import aw.n;
import com.google.android.exoplayer2.text.ttml.TtmlNode;
import com.wear.bean.ProgramPattern;
import ew.c2;
import ew.h1;
import ew.h2;
import ew.n0;
import ew.r2;
import ew.w2;
import fd.LightColorGroupDTO;
import fd.LightModeGroupDTO;
import hd.LightEffect;
import kotlin.Deprecated;
import kotlin.DeprecationLevel;
import kotlin.Metadata;
import kotlin.jvm.JvmStatic;
import kotlin.jvm.internal.DefaultConstructorMarker;
import kotlin.jvm.internal.Intrinsics;
import kotlinx.serialization.UnknownFieldException;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

/* compiled from: LightEffectDTO.kt */
@n
@Metadata(d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\u0010\t\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\b\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0010\u000b\n\u0002\b\u0013\b\u0087\b\u0018\u0000 42\u00020\u0001:\u0002\u001b\u0018B/\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\u0006\u0010\u0005\u001a\u00020\u0004\u0012\u0006\u0010\u0006\u001a\u00020\u0004\u0012\u0006\u0010\b\u001a\u00020\u0007\u0012\u0006\u0010\n\u001a\u00020\t¢\u0006\u0004\b\u000b\u0010\fBK\b\u0010\u0012\u0006\u0010\u000e\u001a\u00020\r\u0012\u0006\u0010\u0003\u001a\u00020\u0002\u0012\b\u0010\u0005\u001a\u0004\u0018\u00010\u0004\u0012\b\u0010\u0006\u001a\u0004\u0018\u00010\u0004\u0012\b\u0010\b\u001a\u0004\u0018\u00010\u0007\u0012\b\u0010\n\u001a\u0004\u0018\u00010\t\u0012\b\u0010\u0010\u001a\u0004\u0018\u00010\u000f¢\u0006\u0004\b\u000b\u0010\u0011J'\u0010\u0018\u001a\u00020\u00172\u0006\u0010\u0012\u001a\u00020\u00002\u0006\u0010\u0014\u001a\u00020\u00132\u0006\u0010\u0016\u001a\u00020\u0015H\u0001¢\u0006\u0004\b\u0018\u0010\u0019J\r\u0010\u001b\u001a\u00020\u001a¢\u0006\u0004\b\u001b\u0010\u001cJ\u0010\u0010\u001d\u001a\u00020\u0004HÖ\u0001¢\u0006\u0004\b\u001d\u0010\u001eJ\u0010\u0010\u001f\u001a\u00020\rHÖ\u0001¢\u0006\u0004\b\u001f\u0010 J\u001a\u0010#\u001a\u00020\"2\b\u0010!\u001a\u0004\u0018\u00010\u0001HÖ\u0003¢\u0006\u0004\b#\u0010$R\u0017\u0010\u0003\u001a\u00020\u00028\u0006¢\u0006\f\n\u0004\b\u001b\u0010%\u001a\u0004\b&\u0010'R\u0017\u0010\u0005\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b\u0018\u0010(\u001a\u0004\b)\u0010\u001eR\u0017\u0010\u0006\u001a\u00020\u00048\u0006¢\u0006\f\n\u0004\b*\u0010(\u001a\u0004\b+\u0010\u001eR\u0017\u0010\b\u001a\u00020\u00078\u0006¢\u0006\f\n\u0004\b,\u0010-\u001a\u0004\b.\u0010/R\u0017\u0010\n\u001a\u00020\t8\u0006¢\u0006\f\n\u0004\b0\u00101\u001a\u0004\b2\u00103¨\u00065"}, d2 = {"Lfd/d;", "", "", TtmlNode.ATTR_ID, "", "effectKey", "effectName", "Lfd/b;", "colorGroup", "Lfd/g;", "modeGroup", "<init>", "(JLjava/lang/String;Ljava/lang/String;Lfd/b;Lfd/g;)V", "", "seen0", "Lew/r2;", "serializationConstructorMarker", "(IJLjava/lang/String;Ljava/lang/String;Lfd/b;Lfd/g;Lew/r2;)V", "self", "Ldw/d;", "output", "Lcw/f;", "serialDesc", "", r0.b.f37717b, "(Lfd/d;Ldw/d;Lcw/f;)V", "Lhd/d;", "a", "()Lhd/d;", "toString", "()Ljava/lang/String;", "hashCode", "()I", "other", "", "equals", "(Ljava/lang/Object;)Z", "J", "getId", "()J", "Ljava/lang/String;", "getEffectKey", "c", "getEffectName", "d", "Lfd/b;", "getColorGroup", "()Lfd/b;", "e", "Lfd/g;", "getModeGroup", "()Lfd/g;", "Companion", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
/* renamed from: fd.d, reason: from toString */
/* loaded from: classes4.dex */
public final /* data */ class LightEffectDTO {

    /* renamed from: Companion, reason: from kotlin metadata */
    @NotNull
    public static final Companion INSTANCE = new Companion(null);

    /* renamed from: a, reason: collision with root package name and from kotlin metadata and from toString */
    public final long id;

    /* renamed from: b, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final String effectKey;

    /* renamed from: c, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final String effectName;

    /* renamed from: d, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final LightColorGroupDTO colorGroup;

    /* renamed from: e, reason: collision with root package name and from kotlin metadata and from toString */
    @NotNull
    public final LightModeGroupDTO modeGroup;

    /* compiled from: LightEffectDTO.kt */
    @Metadata(d1 = {"\u0000:\n\u0000\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\u0011\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0005\bÇ\u0002\u0018\u00002\b\u0012\u0004\u0012\u00020\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0003\u0010\u0004J\u001d\u0010\t\u001a\u00020\b2\u0006\u0010\u0006\u001a\u00020\u00052\u0006\u0010\u0007\u001a\u00020\u0002¢\u0006\u0004\b\t\u0010\nJ\u0015\u0010\r\u001a\u00020\u00022\u0006\u0010\f\u001a\u00020\u000b¢\u0006\u0004\b\r\u0010\u000eJ\u0017\u0010\u0011\u001a\f\u0012\b\u0012\u0006\u0012\u0002\b\u00030\u00100\u000f¢\u0006\u0004\b\u0011\u0010\u0012R\u0017\u0010\u0014\u001a\u00020\u00138\u0006¢\u0006\f\n\u0004\b\u0014\u0010\u0015\u001a\u0004\b\u0016\u0010\u0017¨\u0006\u0018"}, d2 = {"com/remotekmp/lightEffect/data/entities/LightEffectDTO.$serializer", "Lew/n0;", "Lfd/d;", "<init>", "()V", "Ldw/f;", "encoder", "value", "", ProgramPattern.writePatternChar100, "(Ldw/f;Lfd/d;)V", "Ldw/e;", "decoder", "e", "(Ldw/e;)Lfd/d;", "", "Law/c;", "d", "()[Law/c;", "Lcw/f;", "descriptor", "Lcw/f;", "getDescriptor", "()Lcw/f;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    @Deprecated(level = DeprecationLevel.HIDDEN, message = "This synthesized declaration should not be used directly")
    /* renamed from: fd.d$a */
    public /* synthetic */ class a implements n0<LightEffectDTO> {

        /* renamed from: a, reason: collision with root package name */
        @NotNull
        public static final a f30623a;

        @NotNull
        private static final cw.f descriptor;

        static {
            a aVar = new a();
            f30623a = aVar;
            h2 h2Var = new h2("com.remotekmp.lightEffect.data.entities.LightEffectDTO", aVar, 5);
            h2Var.o(TtmlNode.ATTR_ID, false);
            h2Var.o("effectKey", false);
            h2Var.o("effectName", false);
            h2Var.o("colorGroup", false);
            h2Var.o("modeGroup", false);
            descriptor = h2Var;
        }

        @Override // ew.n0
        @NotNull
        public aw.c<?>[] b() {
            return n0.a.a(this);
        }

        @Override // ew.n0
        @NotNull
        public final aw.c<?>[] d() {
            w2 w2Var = w2.f30386a;
            return new aw.c[]{h1.f30272a, w2Var, w2Var, LightColorGroupDTO.a.f30611a, LightModeGroupDTO.a.f30638a};
        }

        @Override // aw.b
        @NotNull
        /* renamed from: e, reason: merged with bridge method [inline-methods] */
        public final LightEffectDTO a(@NotNull dw.e decoder) {
            int i10;
            String str;
            String str2;
            LightColorGroupDTO lightColorGroupDTO;
            LightModeGroupDTO lightModeGroupDTO;
            long j10;
            Intrinsics.checkNotNullParameter(decoder, "decoder");
            cw.f fVar = descriptor;
            dw.c b10 = decoder.b(fVar);
            String str3 = null;
            if (b10.o()) {
                long p10 = b10.p(fVar, 0);
                String F = b10.F(fVar, 1);
                String F2 = b10.F(fVar, 2);
                str = F;
                lightColorGroupDTO = (LightColorGroupDTO) b10.H(fVar, 3, LightColorGroupDTO.a.f30611a, null);
                lightModeGroupDTO = (LightModeGroupDTO) b10.H(fVar, 4, LightModeGroupDTO.a.f30638a, null);
                str2 = F2;
                j10 = p10;
                i10 = 31;
            } else {
                LightModeGroupDTO lightModeGroupDTO2 = null;
                long j11 = 0;
                int i11 = 0;
                boolean z10 = true;
                String str4 = null;
                LightColorGroupDTO lightColorGroupDTO2 = null;
                while (z10) {
                    int h10 = b10.h(fVar);
                    if (h10 == -1) {
                        z10 = false;
                    } else if (h10 == 0) {
                        j11 = b10.p(fVar, 0);
                        i11 |= 1;
                    } else if (h10 == 1) {
                        str3 = b10.F(fVar, 1);
                        i11 |= 2;
                    } else if (h10 == 2) {
                        str4 = b10.F(fVar, 2);
                        i11 |= 4;
                    } else if (h10 == 3) {
                        lightColorGroupDTO2 = (LightColorGroupDTO) b10.H(fVar, 3, LightColorGroupDTO.a.f30611a, lightColorGroupDTO2);
                        i11 |= 8;
                    } else {
                        if (h10 != 4) {
                            throw new UnknownFieldException(h10);
                        }
                        lightModeGroupDTO2 = (LightModeGroupDTO) b10.H(fVar, 4, LightModeGroupDTO.a.f30638a, lightModeGroupDTO2);
                        i11 |= 16;
                    }
                }
                i10 = i11;
                str = str3;
                str2 = str4;
                lightColorGroupDTO = lightColorGroupDTO2;
                lightModeGroupDTO = lightModeGroupDTO2;
                j10 = j11;
            }
            b10.c(fVar);
            return new LightEffectDTO(i10, j10, str, str2, lightColorGroupDTO, lightModeGroupDTO, null);
        }

        @Override // aw.o
        /* renamed from: f, reason: merged with bridge method [inline-methods] */
        public final void c(@NotNull dw.f encoder, @NotNull LightEffectDTO value) {
            Intrinsics.checkNotNullParameter(encoder, "encoder");
            Intrinsics.checkNotNullParameter(value, "value");
            cw.f fVar = descriptor;
            dw.d b10 = encoder.b(fVar);
            LightEffectDTO.b(value, b10, fVar);
            b10.c(fVar);
        }

        @Override // aw.c, aw.o, aw.b
        @NotNull
        public final cw.f getDescriptor() {
            return descriptor;
        }
    }

    /* compiled from: LightEffectDTO.kt */
    @Metadata(d1 = {"\u0000\u0018\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0003\b\u0086\u0003\u0018\u00002\u00020\u0001B\t\b\u0002¢\u0006\u0004\b\u0002\u0010\u0003J\u0013\u0010\u0006\u001a\b\u0012\u0004\u0012\u00020\u00050\u0004¢\u0006\u0004\b\u0006\u0010\u0007¨\u0006\b"}, d2 = {"Lfd/d$b;", "", "<init>", "()V", "Law/c;", "Lfd/d;", "serializer", "()Law/c;", "lightEffect"}, k = 1, mv = {2, 0, 0}, xi = 48)
    /* renamed from: fd.d$b, reason: from kotlin metadata */
    public static final class Companion {
        public Companion() {
        }

        @NotNull
        public final aw.c<LightEffectDTO> serializer() {
            return a.f30623a;
        }

        public /* synthetic */ Companion(DefaultConstructorMarker defaultConstructorMarker) {
            this();
        }
    }

    public /* synthetic */ LightEffectDTO(int i10, long j10, String str, String str2, LightColorGroupDTO lightColorGroupDTO, LightModeGroupDTO lightModeGroupDTO, r2 r2Var) {
        if (31 != (i10 & 31)) {
            c2.b(i10, 31, a.f30623a.getDescriptor());
        }
        this.id = j10;
        this.effectKey = str;
        this.effectName = str2;
        this.colorGroup = lightColorGroupDTO;
        this.modeGroup = lightModeGroupDTO;
    }

    @JvmStatic
    public static final /* synthetic */ void b(LightEffectDTO self, dw.d output, cw.f serialDesc) {
        output.E(serialDesc, 0, self.id);
        output.B(serialDesc, 1, self.effectKey);
        output.B(serialDesc, 2, self.effectName);
        output.r(serialDesc, 3, LightColorGroupDTO.a.f30611a, self.colorGroup);
        output.r(serialDesc, 4, LightModeGroupDTO.a.f30638a, self.modeGroup);
    }

    @NotNull
    public final LightEffect a() {
        return new LightEffect(this.id, this.effectKey, this.effectName, this.colorGroup.b(), this.modeGroup.b());
    }

    public boolean equals(@Nullable Object other) {
        if (this == other) {
            return true;
        }
        if (!(other instanceof LightEffectDTO)) {
            return false;
        }
        LightEffectDTO lightEffectDTO = (LightEffectDTO) other;
        return this.id == lightEffectDTO.id && Intrinsics.areEqual(this.effectKey, lightEffectDTO.effectKey) && Intrinsics.areEqual(this.effectName, lightEffectDTO.effectName) && Intrinsics.areEqual(this.colorGroup, lightEffectDTO.colorGroup) && Intrinsics.areEqual(this.modeGroup, lightEffectDTO.modeGroup);
    }

    public int hashCode() {
        return (((((((androidx.compose.animation.a.a(this.id) * 31) + this.effectKey.hashCode()) * 31) + this.effectName.hashCode()) * 31) + this.colorGroup.hashCode()) * 31) + this.modeGroup.hashCode();
    }

    @NotNull
    public String toString() {
        return "LightEffectDTO(id=" + this.id + ", effectKey=" + this.effectKey + ", effectName=" + this.effectName + ", colorGroup=" + this.colorGroup + ", modeGroup=" + this.modeGroup + ')';
    }

    public LightEffectDTO(long j10, @NotNull String effectKey, @NotNull String effectName, @NotNull LightColorGroupDTO colorGroup, @NotNull LightModeGroupDTO modeGroup) {
        Intrinsics.checkNotNullParameter(effectKey, "effectKey");
        Intrinsics.checkNotNullParameter(effectName, "effectName");
        Intrinsics.checkNotNullParameter(colorGroup, "colorGroup");
        Intrinsics.checkNotNullParameter(modeGroup, "modeGroup");
        this.id = j10;
        this.effectKey = effectKey;
        this.effectName = effectName;
        this.colorGroup = colorGroup;
        this.modeGroup = modeGroup;
    }
}
